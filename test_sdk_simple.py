#!/usr/bin/env python3
"""
Simple SDK Test

Plugin SDK'nın temel fonksiyonlarını test eder.
"""

import sys
import asyncio
from pathlib import Path

# Proje root'unu path'e ekle
sys.path.insert(0, str(Path(__file__).parent))

try:
    from llm_vision.sdk.plugin_manifest import PluginManifest, PluginType
    from llm_vision.sdk.plugin_data import PluginData, PluginResponse
    from llm_vision.sdk.examples.sensor_plugin import ExampleSensorPlugin
    print("✅ SDK import başarılı!")
except ImportError as e:
    print(f"❌ SDK import hatası: {e}")
    sys.exit(1)


def test_plugin_manifest():
    """Plugin manifest testi"""
    print("\n🧪 Plugin Manifest testi...")
    
    try:
        manifest = PluginManifest(
            name="test_plugin",
            version="1.0.0",
            type=PluginType.SENSOR,
            description="Test plugin",
            author="Test Author"
        )
        
        print(f"   Plugin adı: {manifest.name}")
        print(f"   Versiyon: {manifest.version}")
        print(f"   Tip: {manifest.type.value}")
        
        # Doğrulama
        errors = manifest.validate()
        if errors:
            print(f"   ❌ Doğrulama hataları: {errors}")
        else:
            print("   ✅ Manifest doğrulama başarılı")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Manifest testi hatası: {e}")
        return False


def test_plugin_data():
    """Plugin data testi"""
    print("\n🧪 Plugin Data testi...")
    
    try:
        data = PluginData(
            source="test_plugin",
            data={"test": True, "value": 42},
            confidence=0.95,
            tags=["test", "example"]
        )
        
        print(f"   Source: {data.source}")
        print(f"   Data: {data.data}")
        print(f"   Confidence: {data.confidence}")
        print(f"   Tags: {data.tags}")
        
        # Tag işlemleri
        data.add_tag("new_tag")
        print(f"   Tag eklendi: {data.tags}")
        
        # Metadata
        data.set_metadata("test_key", "test_value")
        print(f"   Metadata: {data.get_metadata('test_key')}")
        
        print("   ✅ Plugin Data testi başarılı")
        return True
        
    except Exception as e:
        print(f"   ❌ Plugin Data testi hatası: {e}")
        return False


async def test_example_sensor_plugin():
    """Örnek sensor plugin testi"""
    print("\n🧪 Example Sensor Plugin testi...")
    
    try:
        plugin = ExampleSensorPlugin()
        print(f"   Plugin oluşturuldu: {plugin.manifest.name}")
        
        # Plugin'i başlat
        success = await plugin._safe_initialize()
        if success:
            print("   ✅ Plugin başlatma başarılı")
        else:
            print("   ❌ Plugin başlatma başarısız")
            return False
        
        # Veri topla
        response = await plugin.collect_data()
        if response.success:
            print("   ✅ Veri toplama başarılı")
            print(f"   Execution time: {response.execution_time_ms:.2f}ms")
        else:
            print(f"   ❌ Veri toplama başarısız: {response.error_message}")
            return False
        
        # Schema al
        schema = plugin.get_schema()
        print(f"   Schema type: {schema.get('type')}")
        
        # Plugin'i temizle
        await plugin._safe_cleanup()
        print("   ✅ Plugin temizleme başarılı")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Example Sensor Plugin testi hatası: {e}")
        return False


async def main():
    """Ana test fonksiyonu"""
    print("🚀 LLM Vision SDK Test Başlıyor...\n")
    
    tests = [
        ("Plugin Manifest", test_plugin_manifest()),
        ("Plugin Data", test_plugin_data()),
        ("Example Sensor Plugin", await test_example_sensor_plugin())
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, result in tests:
        if result:
            passed += 1
    
    print(f"\n📊 Test Sonuçları:")
    print(f"   Toplam: {total}")
    print(f"   Başarılı: {passed}")
    print(f"   Başarısız: {total - passed}")
    print(f"   Başarı oranı: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 Tüm testler başarılı! Plugin SDK hazır.")
        return 0
    else:
        print("\n⚠️ Bazı testler başarısız. Lütfen hataları kontrol edin.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
