"""
Example Action Plugin

Action plugin geliştirme için örnek implementasyon.
"""

import time
import asyncio
import json
from typing import Dict, Any, List
from pathlib import Path

from ..base_plugin import BaseActionPlugin
from ..plugin_manifest import PluginManifest, PluginType, PluginPermission
from ..plugin_data import PluginResponse, PluginData
from ...utils.logger import get_logger

logger = get_logger(__name__)


class ExampleActionPlugin(BaseActionPlugin):
    """Örnek action plugin implementasyonu"""
    
    def __init__(self, manifest: PluginManifest = None):
        # Manifest oluştur (eğer verilmemişse)
        if manifest is None:
            manifest = PluginManifest(
                name="example_action",
                version="1.0.0",
                type=PluginType.ACTION,
                description="Örnek action plugin - dosya işlemleri ve sistem komutları",
                author="LLM Vision Team",
                permissions=[
                    PluginPermission(name="file_read", description="<PERSON>sya okuma izni"),
                    PluginPermission(name="file_write", description="<PERSON><PERSON>a yazma izni")
                ]
            )
        
        super().__init__(manifest)
        
        # Plugin özel ayarları
        self.allowed_extensions = {'.txt', '.json', '.yml', '.yaml', '.md'}
        self.max_file_size_mb = 10
        self.action_history = []
        self.max_history_size = 100
    
    async def initialize(self, config: Dict[str, Any] = None) -> bool:
        """Plugin'i başlat"""
        try:
            logger.info("Example Action Plugin başlatılıyor...")
            
            # Konfigürasyonu uygula
            if config:
                self.allowed_extensions = set(config.get("allowed_extensions", self.allowed_extensions))
                self.max_file_size_mb = config.get("max_file_size_mb", 10)
                self.max_history_size = config.get("max_history_size", 100)
            
            # Test aksiyonu çalıştır
            test_result = await self.execute_action("test", {})
            if not test_result.success:
                logger.error("Test aksiyonu başarısız")
                return False
            
            logger.info("Example Action Plugin başarıyla başlatıldı")
            return True
            
        except Exception as e:
            logger.error(f"Plugin başlatma hatası: {e}")
            return False
    
    async def cleanup(self) -> None:
        """Plugin'i temizle"""
        try:
            logger.info("Example Action Plugin temizleniyor...")
            
            # Geçmişi temizle
            self.action_history.clear()
            
            logger.info("Example Action Plugin temizlendi")
            
        except Exception as e:
            logger.error(f"Plugin temizleme hatası: {e}")
    
    async def execute_action(self, action_name: str, parameters: Dict[str, Any]) -> PluginResponse:
        """Aksiyon çalıştır"""
        try:
            start_time = time.time()
            
            # Aksiyon geçmişine ekle
            self._add_to_history(action_name, parameters)
            
            # Aksiyon routing
            if action_name == "test":
                result = await self._test_action(parameters)
            elif action_name == "read_file":
                result = await self._read_file_action(parameters)
            elif action_name == "write_file":
                result = await self._write_file_action(parameters)
            elif action_name == "list_files":
                result = await self._list_files_action(parameters)
            elif action_name == "create_directory":
                result = await self._create_directory_action(parameters)
            elif action_name == "get_file_info":
                result = await self._get_file_info_action(parameters)
            else:
                raise ValueError(f"Bilinmeyen aksiyon: {action_name}")
            
            execution_time = (time.time() - start_time) * 1000
            
            # Plugin data oluştur
            plugin_data = PluginData(
                source=self.manifest.name,
                data={
                    "action": action_name,
                    "parameters": parameters,
                    "result": result,
                    "execution_time_ms": execution_time
                },
                tags=["action", action_name]
            )
            
            return PluginResponse.success_response(plugin_data, execution_time)
            
        except Exception as e:
            logger.error(f"Aksiyon çalıştırma hatası ({action_name}): {e}")
            execution_time = (time.time() - start_time) * 1000 if 'start_time' in locals() else 0
            return PluginResponse.error_response(
                error_message=str(e),
                error_code="ACTION_EXECUTION_ERROR",
                execution_time_ms=execution_time
            )
    
    def get_available_actions(self) -> List[Dict[str, Any]]:
        """Mevcut aksiyonları listele"""
        return [
            {
                "name": "test",
                "description": "Test aksiyonu",
                "parameters": {}
            },
            {
                "name": "read_file",
                "description": "Dosya okuma",
                "parameters": {
                    "file_path": {"type": "string", "required": True}
                }
            },
            {
                "name": "write_file",
                "description": "Dosya yazma",
                "parameters": {
                    "file_path": {"type": "string", "required": True},
                    "content": {"type": "string", "required": True},
                    "append": {"type": "boolean", "default": False}
                }
            },
            {
                "name": "list_files",
                "description": "Dosya listesi",
                "parameters": {
                    "directory_path": {"type": "string", "required": True},
                    "recursive": {"type": "boolean", "default": False}
                }
            },
            {
                "name": "create_directory",
                "description": "Dizin oluşturma",
                "parameters": {
                    "directory_path": {"type": "string", "required": True}
                }
            },
            {
                "name": "get_file_info",
                "description": "Dosya bilgisi alma",
                "parameters": {
                    "file_path": {"type": "string", "required": True}
                }
            }
        ]
    
    def get_schema(self) -> Dict[str, Any]:
        """Plugin veri şemasını döndür"""
        return {
            "type": "object",
            "properties": {
                "action": {"type": "string"},
                "parameters": {"type": "object"},
                "result": {"type": "object"},
                "execution_time_ms": {"type": "number"}
            },
            "required": ["action", "result"]
        }
    
    async def _test_action(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Test aksiyonu"""
        return {
            "status": "success",
            "message": "Test aksiyonu başarılı",
            "timestamp": time.time(),
            "plugin_info": {
                "name": self.manifest.name,
                "version": self.manifest.version,
                "status": self.status.value
            }
        }
    
    async def _read_file_action(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Dosya okuma aksiyonu"""
        file_path = Path(parameters.get("file_path", ""))
        
        # Güvenlik kontrolleri
        if not file_path.exists():
            raise FileNotFoundError(f"Dosya bulunamadı: {file_path}")
        
        if file_path.suffix not in self.allowed_extensions:
            raise ValueError(f"İzin verilmeyen dosya uzantısı: {file_path.suffix}")
        
        file_size_mb = file_path.stat().st_size / (1024 * 1024)
        if file_size_mb > self.max_file_size_mb:
            raise ValueError(f"Dosya çok büyük: {file_size_mb:.2f}MB > {self.max_file_size_mb}MB")
        
        # Dosyayı oku
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return {
            "file_path": str(file_path),
            "content": content,
            "size_bytes": file_path.stat().st_size,
            "encoding": "utf-8"
        }
    
    async def _write_file_action(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Dosya yazma aksiyonu"""
        file_path = Path(parameters.get("file_path", ""))
        content = parameters.get("content", "")
        append_mode = parameters.get("append", False)
        
        # Güvenlik kontrolleri
        if file_path.suffix not in self.allowed_extensions:
            raise ValueError(f"İzin verilmeyen dosya uzantısı: {file_path.suffix}")
        
        # Dizini oluştur (gerekirse)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Dosyayı yaz
        mode = 'a' if append_mode else 'w'
        with open(file_path, mode, encoding='utf-8') as f:
            f.write(content)
        
        return {
            "file_path": str(file_path),
            "bytes_written": len(content.encode('utf-8')),
            "mode": "append" if append_mode else "write"
        }
    
    async def _list_files_action(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Dosya listesi aksiyonu"""
        directory_path = Path(parameters.get("directory_path", "."))
        recursive = parameters.get("recursive", False)
        
        if not directory_path.exists():
            raise FileNotFoundError(f"Dizin bulunamadı: {directory_path}")
        
        if not directory_path.is_dir():
            raise ValueError(f"Path bir dizin değil: {directory_path}")
        
        files = []
        pattern = "**/*" if recursive else "*"
        
        for item in directory_path.glob(pattern):
            files.append({
                "name": item.name,
                "path": str(item),
                "is_file": item.is_file(),
                "is_directory": item.is_dir(),
                "size_bytes": item.stat().st_size if item.is_file() else 0,
                "modified_time": item.stat().st_mtime
            })
        
        return {
            "directory_path": str(directory_path),
            "file_count": len([f for f in files if f["is_file"]]),
            "directory_count": len([f for f in files if f["is_directory"]]),
            "files": files
        }
    
    async def _create_directory_action(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Dizin oluşturma aksiyonu"""
        directory_path = Path(parameters.get("directory_path", ""))
        
        # Dizini oluştur
        directory_path.mkdir(parents=True, exist_ok=True)
        
        return {
            "directory_path": str(directory_path),
            "created": True
        }
    
    async def _get_file_info_action(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Dosya bilgisi alma aksiyonu"""
        file_path = Path(parameters.get("file_path", ""))
        
        if not file_path.exists():
            raise FileNotFoundError(f"Dosya bulunamadı: {file_path}")
        
        stat = file_path.stat()
        
        return {
            "file_path": str(file_path),
            "name": file_path.name,
            "extension": file_path.suffix,
            "size_bytes": stat.st_size,
            "size_mb": stat.st_size / (1024 * 1024),
            "is_file": file_path.is_file(),
            "is_directory": file_path.is_dir(),
            "created_time": stat.st_ctime,
            "modified_time": stat.st_mtime,
            "accessed_time": stat.st_atime
        }
    
    def _add_to_history(self, action_name: str, parameters: Dict[str, Any]) -> None:
        """Aksiyon geçmişine ekle"""
        self.action_history.append({
            "action": action_name,
            "parameters": parameters,
            "timestamp": time.time()
        })
        
        # Geçmiş boyutunu kontrol et
        if len(self.action_history) > self.max_history_size:
            self.action_history.pop(0)
    
    def get_action_history(self, limit: int = None) -> List[Dict[str, Any]]:
        """Aksiyon geçmişini al"""
        if limit:
            return self.action_history[-limit:]
        return self.action_history.copy()
