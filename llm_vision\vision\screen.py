"""
Ekran görü<PERSON><PERSON><PERSON><PERSON> modülü
"""

import cv2
import numpy as np
import asyncio
from typing import Optional, Tuple, List
import platform

# Platform-specific imports
if platform.system() == "Windows":
    import pyautogui
    try:
        import win32gui
        import win32ui
        import win32con
        WINDOWS_API_AVAILABLE = True
    except ImportError:
        WINDOWS_API_AVAILABLE = False
elif platform.system() == "Linux":
    import pyautogui
    try:
        import pyscreenshot as ImageGrab
    except ImportError:
        try:
            import PIL.ImageGrab as ImageGrab
        except ImportError:
            ImageGrab = None
elif platform.system() == "Darwin":  # macOS
    import pyautogui
    try:
        import PIL.ImageGrab as ImageGrab
    except ImportError:
        ImageGrab = None

from ..utils.logger import get_logger
from ..utils.exceptions import VisionError

logger = get_logger(__name__)


class ScreenCapture:
    """Ekran görüntüsü sınıfı"""
    
    def __init__(self):
        self.platform = platform.system()
        logger.info(f"Ekran görünt<PERSON><PERSON>ü modülü başlatıldı - Platform: {self.platform}")
        
        # PyAutoGUI güvenlik özelliğini devre dışı bırak
        if hasattr(pyautogui, 'FAILSAFE'):
            pyautogui.FAILSAFE = False
    
    def capture_screen(self, 
                      region: Tuple[int, int, int, int] = None) -> Optional[np.ndarray]:
        """Ekran görüntüsü al"""
        try:
            if region:
                # Belirli bir bölgeyi yakala
                x, y, width, height = region
                screenshot = pyautogui.screenshot(region=(x, y, width, height))
            else:
                # Tüm ekranı yakala
                screenshot = pyautogui.screenshot()
            
            # PIL Image'ı OpenCV formatına çevir
            screenshot_np = np.array(screenshot)
            screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)
            
            logger.info(f"Ekran görüntüsü alındı - Boyut: {screenshot_cv.shape}")
            return screenshot_cv
            
        except Exception as e:
            logger.error(f"Ekran görüntüsü alma hatası: {e}")
            raise VisionError(f"Ekran görüntüsü alma hatası: {str(e)}")
    
    async def capture_screen_async(self, 
                                  region: Tuple[int, int, int, int] = None) -> Optional[np.ndarray]:
        """Async ekran görüntüsü al"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.capture_screen, region
        )
    
    def capture_window(self, window_title: str = None) -> Optional[np.ndarray]:
        """Belirli bir pencereyi yakala"""
        try:
            if self.platform == "Windows" and WINDOWS_API_AVAILABLE:
                return self._capture_window_windows(window_title)
            else:
                # Diğer platformlar için genel yaklaşım
                return self._capture_window_generic(window_title)
                
        except Exception as e:
            logger.error(f"Pencere yakalama hatası: {e}")
            raise VisionError(f"Pencere yakalama hatası: {str(e)}")
    
    def _capture_window_windows(self, window_title: str = None) -> Optional[np.ndarray]:
        """Windows'ta pencere yakala"""
        if not WINDOWS_API_AVAILABLE:
            return self._capture_window_generic(window_title)
            
        try:
            if window_title:
                # Belirli pencereyi bul
                hwnd = win32gui.FindWindow(None, window_title)
                if hwnd == 0:
                    logger.warning(f"Pencere bulunamadı: {window_title}")
                    return None
            else:
                # Aktif pencereyi al
                hwnd = win32gui.GetForegroundWindow()
            
            # Pencere boyutlarını al
            left, top, right, bottom = win32gui.GetWindowRect(hwnd)
            width = right - left
            height = bottom - top
            
            # Device context al
            hwndDC = win32gui.GetWindowDC(hwnd)
            mfcDC = win32ui.CreateDCFromHandle(hwndDC)
            saveDC = mfcDC.CreateCompatibleDC()
            
            # Bitmap oluştur
            saveBitMap = win32ui.CreateBitmap()
            saveBitMap.CreateCompatibleBitmap(mfcDC, width, height)
            saveDC.SelectObject(saveBitMap)
            
            # Pencereyi kopyala
            result = saveDC.BitBlt((0, 0), (width, height), mfcDC, (0, 0), win32con.SRCCOPY)
            
            if result:
                # Bitmap'i numpy array'e çevir
                bmpinfo = saveBitMap.GetInfo()
                bmpstr = saveBitMap.GetBitmapBits(True)
                
                img = np.frombuffer(bmpstr, dtype='uint8')
                img.shape = (height, width, 4)  # BGRA format
                
                # BGRA'dan BGR'ye çevir
                img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
                
                # Cleanup
                win32gui.DeleteObject(saveBitMap.GetHandle())
                saveDC.DeleteDC()
                mfcDC.DeleteDC()
                win32gui.ReleaseDC(hwnd, hwndDC)
                
                return img
            else:
                logger.error("BitBlt işlemi başarısız")
                return None
                
        except Exception as e:
            logger.error(f"Windows pencere yakalama hatası: {e}")
            return None
    
    def _capture_window_generic(self, window_title: str = None) -> Optional[np.ndarray]:
        """Genel pencere yakalama"""
        try:
            # PyAutoGUI ile aktif pencereyi yakala
            screenshot = pyautogui.screenshot()
            screenshot_np = np.array(screenshot)
            screenshot_cv = cv2.cvtColor(screenshot_np, cv2.COLOR_RGB2BGR)
            
            return screenshot_cv
            
        except Exception as e:
            logger.error(f"Genel pencere yakalama hatası: {e}")
            return None
    
    async def capture_window_async(self, window_title: str = None) -> Optional[np.ndarray]:
        """Async pencere yakalama"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.capture_window, window_title
        )
    
    def get_screen_size(self) -> Tuple[int, int]:
        """Ekran boyutunu al"""
        try:
            size = pyautogui.size()
            return (size.width, size.height)
        except Exception as e:
            logger.error(f"Ekran boyutu alma hatası: {e}")
            return (1920, 1080)  # Varsayılan değer
    
    def get_window_list(self) -> List[str]:
        """Açık pencere listesini al"""
        try:
            if self.platform == "Windows" and WINDOWS_API_AVAILABLE:
                return self._get_window_list_windows()
            else:
                return self._get_window_list_generic()
                
        except Exception as e:
            logger.error(f"Pencere listesi alma hatası: {e}")
            return []
    
    def _get_window_list_windows(self) -> List[str]:
        """Windows pencere listesi"""
        windows = []
        
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if window_title:
                    windows.append(window_title)
            return True
        
        try:
            win32gui.EnumWindows(enum_windows_callback, windows)
            return windows
        except Exception as e:
            logger.error(f"Windows pencere listesi hatası: {e}")
            return []
    
    def _get_window_list_generic(self) -> List[str]:
        """Genel pencere listesi"""
        # Bu platform için detaylı pencere listesi mevcut değil
        return ["Active Window"]
    
    def capture_multiple_screens(self) -> List[np.ndarray]:
        """Çoklu ekran görüntüsü al"""
        screenshots = []
        
        try:
            # Ana ekran
            screenshot = self.capture_screen()
            if screenshot is not None:
                screenshots.append(screenshot)
            
            return screenshots
            
        except Exception as e:
            logger.error(f"Çoklu ekran yakalama hatası: {e}")
            return []
    
    async def capture_multiple_screens_async(self) -> List[np.ndarray]:
        """Async çoklu ekran yakalama"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.capture_multiple_screens
        )
    
    def save_screenshot(self, 
                       image: np.ndarray, 
                       filename: str = "screenshot.png") -> bool:
        """Ekran görüntüsünü kaydet"""
        try:
            success = cv2.imwrite(filename, image)
            if success:
                logger.info(f"Ekran görüntüsü kaydedildi: {filename}")
            else:
                logger.error(f"Ekran görüntüsü kaydedilemedi: {filename}")
            return success
            
        except Exception as e:
            logger.error(f"Ekran görüntüsü kaydetme hatası: {e}")
            return False
    
    def is_available(self) -> bool:
        """Ekran yakalama kullanılabilir mi kontrol et"""
        try:
            # Basit bir test yakalama
            test_screenshot = pyautogui.screenshot(region=(0, 0, 100, 100))
            return test_screenshot is not None
            
        except Exception as e:
            logger.error(f"Ekran yakalama kullanılabilirlik testi hatası: {e}")
            return False
