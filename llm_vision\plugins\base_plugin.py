# Bu dosya artık deprecated - lütfen llm_vision.sdk.base_plugin kullanın
from ..sdk.base_plugin import *
from ..sdk.plugin_manifest import *
from ..sdk.plugin_data import *


class BasePlugin(ABC):
    """Temel plugin sınıfı"""
    
    def __init__(self, manifest: PluginManifest):
        self.manifest = manifest
        self.status = PluginStatus.INACTIVE
        self.config: Dict[str, Any] = {}
        self.error_message: Optional[str] = None
        self.performance_metrics = {
            "calls_count": 0,
            "success_count": 0,
            "error_count": 0,
            "average_response_time": 0.0
        }
        
        logger.info(f"Plugin oluşturuldu: {self.manifest.name}")
    
    @abstractmethod
    async def initialize(self, config: Dict[str, Any] = None) -> bool:
        """Plugin'i başlat"""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Plugin'i temizle"""
        pass
    
    @abstractmethod
    def get_schema(self) -> Dict[str, Any]:
        """Plugin veri şemasını döndür"""
        pass
    
    async def health_check(self) -> bool:
        """Plugin sağlık kontrolü"""
        return self.status == PluginStatus.ACTIVE
    
    def update_metrics(self, success: bool, response_time: float) -> None:
        """Performans metriklerini güncelle"""
        self.performance_metrics["calls_count"] += 1
        if success:
            self.performance_metrics["success_count"] += 1
        else:
            self.performance_metrics["error_count"] += 1
        
        # Ortalama yanıt süresini güncelle
        current_avg = self.performance_metrics["average_response_time"]
        total_calls = self.performance_metrics["calls_count"]
        self.performance_metrics["average_response_time"] = (
            (current_avg * (total_calls - 1) + response_time) / total_calls
        )
    
    def get_metrics(self) -> Dict[str, Any]:
        """Performans metriklerini al"""
        return {
            "manifest": {
                "name": self.manifest.name,
                "version": self.manifest.version,
                "type": self.manifest.type.value
            },
            "status": self.status.value,
            "error_message": self.error_message,
            "performance": self.performance_metrics
        }


class BaseSensorPlugin(BasePlugin):
    """Sensör plugin temel sınıfı"""
    
    def __init__(self, manifest: PluginManifest):
        super().__init__(manifest)
        self.collection_interval = 1.0  # saniye
        self.is_collecting = False
        self._collection_task: Optional[asyncio.Task] = None
    
    @abstractmethod
    async def collect_data(self) -> PluginData:
        """Veri topla"""
        pass
    
    async def start_collection(self, interval: float = None) -> None:
        """Veri toplama başlat"""
        if self.is_collecting:
            return
        
        if interval:
            self.collection_interval = interval
        
        self.is_collecting = True
        self._collection_task = asyncio.create_task(self._collection_loop())
        logger.info(f"Veri toplama başlatıldı: {self.manifest.name}")
    
    async def stop_collection(self) -> None:
        """Veri toplama durdur"""
        self.is_collecting = False
        if self._collection_task:
            self._collection_task.cancel()
            try:
                await self._collection_task
            except asyncio.CancelledError:
                pass
        logger.info(f"Veri toplama durduruldu: {self.manifest.name}")
    
    async def _collection_loop(self) -> None:
        """Veri toplama döngüsü"""
        while self.is_collecting:
            try:
                start_time = asyncio.get_event_loop().time()
                
                data = await self.collect_data()
                
                end_time = asyncio.get_event_loop().time()
                response_time = end_time - start_time
                
                self.update_metrics(True, response_time)
                
                # Veriyi event dispatcher'a gönder (implement edilecek)
                # await self.dispatch_data(data)
                
                await asyncio.sleep(self.collection_interval)
                
            except Exception as e:
                logger.error(f"Veri toplama hatası ({self.manifest.name}): {e}")
                self.update_metrics(False, 0.0)
                self.error_message = str(e)
                await asyncio.sleep(self.collection_interval)


class BaseContextPlugin(BasePlugin):
    """Bağlam analizi plugin temel sınıfı"""
    
    @abstractmethod
    async def analyze_context(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Bağlam analizi yap"""
        pass
    
    @abstractmethod
    async def build_prompt(self, context: Dict[str, Any]) -> str:
        """Prompt oluştur"""
        pass


class BaseActionPlugin(BasePlugin):
    """Aksiyon plugin temel sınıfı"""
    
    @abstractmethod
    async def execute_action(self, action: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Aksiyon çalıştır"""
        pass
    
    @abstractmethod
    def get_available_actions(self) -> List[str]:
        """Mevcut aksiyonları listele"""
        pass


class BaseMemoryPlugin(BasePlugin):
    """Hafıza plugin temel sınıfı"""
    
    @abstractmethod
    async def store_memory(self, data: Dict[str, Any], metadata: Dict[str, Any]) -> str:
        """Hafızaya kaydet"""
        pass
    
    @abstractmethod
    async def retrieve_memory(self, query: str, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Hafızadan al"""
        pass
    
    @abstractmethod
    async def update_memory(self, memory_id: str, data: Dict[str, Any]) -> bool:
        """Hafızayı güncelle"""
        pass
    
    @abstractmethod
    async def delete_memory(self, memory_id: str) -> bool:
        """Hafızadan sil"""
        pass
