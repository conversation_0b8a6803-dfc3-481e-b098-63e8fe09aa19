"""
Nesne tanıma modülü
"""

import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional
import asyncio

from ..utils.logger import get_logger
from ..utils.exceptions import VisionError

logger = get_logger(__name__)


class DetectedObject:
    """Tanınan nesne sınıfı"""
    
    def __init__(self, 
                 class_name: str, 
                 confidence: float, 
                 bbox: Tuple[int, int, int, int],
                 class_id: int = None):
        self.class_name = class_name
        self.confidence = confidence
        self.bbox = bbox  # (x, y, width, height)
        self.class_id = class_id
    
    def get_center(self) -> Tuple[int, int]:
        """Nesnenin merkez koordinatını al"""
        x, y, w, h = self.bbox
        return (x + w // 2, y + h // 2)
    
    def get_area(self) -> int:
        """Nesnenin alanını al"""
        _, _, w, h = self.bbox
        return w * h
    
    def to_dict(self) -> dict:
        """Sözlük formatına çevir"""
        return {
            "class_name": self.class_name,
            "confidence": self.confidence,
            "bbox": self.bbox,
            "center": self.get_center(),
            "area": self.get_area(),
            "class_id": self.class_id
        }


class ObjectDetector:
    """Nesne tanıma sınıfı"""
    
    def __init__(self):
        self.net = None
        self.classes = []
        self.colors = []
        self.is_loaded = False
        
        # COCO sınıf isimleri (YOLOv4/v5 için)
        self.coco_classes = [
            "person", "bicycle", "car", "motorcycle", "airplane", "bus", "train", "truck",
            "boat", "traffic light", "fire hydrant", "stop sign", "parking meter", "bench",
            "bird", "cat", "dog", "horse", "sheep", "cow", "elephant", "bear", "zebra",
            "giraffe", "backpack", "umbrella", "handbag", "tie", "suitcase", "frisbee",
            "skis", "snowboard", "sports ball", "kite", "baseball bat", "baseball glove",
            "skateboard", "surfboard", "tennis racket", "bottle", "wine glass", "cup",
            "fork", "knife", "spoon", "bowl", "banana", "apple", "sandwich", "orange",
            "broccoli", "carrot", "hot dog", "pizza", "donut", "cake", "chair", "couch",
            "potted plant", "bed", "dining table", "toilet", "tv", "laptop", "mouse",
            "remote", "keyboard", "cell phone", "microwave", "oven", "toaster", "sink",
            "refrigerator", "book", "clock", "vase", "scissors", "teddy bear", "hair drier",
            "toothbrush"
        ]
        
        logger.info("Nesne tanıma modülü başlatıldı")
    
    def load_yolo_model(self, 
                       weights_path: str = None, 
                       config_path: str = None, 
                       classes_path: str = None) -> bool:
        """YOLO modelini yükle"""
        try:
            if weights_path and config_path:
                # Özel YOLO modeli yükle
                self.net = cv2.dnn.readNet(weights_path, config_path)
                
                if classes_path:
                    with open(classes_path, 'r') as f:
                        self.classes = [line.strip() for line in f.readlines()]
                else:
                    self.classes = self.coco_classes
            else:
                # Varsayılan olarak COCO sınıflarını kullan
                self.classes = self.coco_classes
                logger.warning("YOLO model dosyaları belirtilmedi, sadece COCO sınıfları kullanılacak")
            
            # Renk paleti oluştur
            self.colors = np.random.uniform(0, 255, size=(len(self.classes), 3))
            
            if self.net is not None:
                # GPU kullanımını kontrol et
                if cv2.cuda.getCudaEnabledDeviceCount() > 0:
                    self.net.setPreferableBackend(cv2.dnn.DNN_BACKEND_CUDA)
                    self.net.setPreferableTarget(cv2.dnn.DNN_TARGET_CUDA)
                    logger.info("CUDA backend kullanılıyor")
                else:
                    self.net.setPreferableBackend(cv2.dnn.DNN_BACKEND_OPENCV)
                    self.net.setPreferableTarget(cv2.dnn.DNN_TARGET_CPU)
                    logger.info("CPU backend kullanılıyor")
                
                self.is_loaded = True
                logger.info(f"YOLO modeli yüklendi - {len(self.classes)} sınıf")
                return True
            else:
                logger.warning("YOLO modeli yüklenemedi, basit algılama kullanılacak")
                return False
                
        except Exception as e:
            logger.error(f"YOLO model yükleme hatası: {e}")
            return False
    
    def detect_objects_yolo(self, 
                           image: np.ndarray, 
                           confidence_threshold: float = 0.5,
                           nms_threshold: float = 0.4) -> List[DetectedObject]:
        """YOLO ile nesne tanıma"""
        if not self.is_loaded or self.net is None:
            raise VisionError("YOLO modeli yüklenmemiş")
        
        try:
            height, width = image.shape[:2]
            
            # Görüntüyü blob formatına çevir
            blob = cv2.dnn.blobFromImage(
                image, 1/255.0, (416, 416), swapRB=True, crop=False
            )
            
            # Ağa giriş ver
            self.net.setInput(blob)
            
            # Çıkış katmanlarını al
            layer_names = self.net.getLayerNames()
            output_layers = [layer_names[i - 1] for i in self.net.getUnconnectedOutLayers()]
            
            # İleri geçiş
            outputs = self.net.forward(output_layers)
            
            # Sonuçları işle
            boxes = []
            confidences = []
            class_ids = []
            
            for output in outputs:
                for detection in output:
                    scores = detection[5:]
                    class_id = np.argmax(scores)
                    confidence = scores[class_id]
                    
                    if confidence > confidence_threshold:
                        # Bounding box koordinatları
                        center_x = int(detection[0] * width)
                        center_y = int(detection[1] * height)
                        w = int(detection[2] * width)
                        h = int(detection[3] * height)
                        
                        # Sol üst köşe koordinatları
                        x = int(center_x - w / 2)
                        y = int(center_y - h / 2)
                        
                        boxes.append([x, y, w, h])
                        confidences.append(float(confidence))
                        class_ids.append(class_id)
            
            # Non-maximum suppression uygula
            indices = cv2.dnn.NMSBoxes(boxes, confidences, confidence_threshold, nms_threshold)
            
            detected_objects = []
            if len(indices) > 0:
                for i in indices.flatten():
                    x, y, w, h = boxes[i]
                    class_name = self.classes[class_ids[i]] if class_ids[i] < len(self.classes) else "unknown"
                    confidence = confidences[i]
                    
                    detected_objects.append(DetectedObject(
                        class_name=class_name,
                        confidence=confidence,
                        bbox=(x, y, w, h),
                        class_id=class_ids[i]
                    ))
            
            logger.info(f"YOLO ile {len(detected_objects)} nesne tanındı")
            return detected_objects
            
        except Exception as e:
            logger.error(f"YOLO nesne tanıma hatası: {e}")
            raise VisionError(f"YOLO nesne tanıma hatası: {str(e)}")
    
    def detect_objects_simple(self, 
                             image: np.ndarray, 
                             confidence_threshold: float = 0.5) -> List[DetectedObject]:
        """Basit nesne tanıma (OpenCV cascade'ler ile)"""
        try:
            detected_objects = []
            
            # Yüz tanıma
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)
            
            for (x, y, w, h) in faces:
                detected_objects.append(DetectedObject(
                    class_name="face",
                    confidence=0.8,  # Sabit güven değeri
                    bbox=(x, y, w, h)
                ))
            
            # Göz tanıma
            eye_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_eye.xml')
            eyes = eye_cascade.detectMultiScale(gray, 1.1, 4)
            
            for (x, y, w, h) in eyes:
                detected_objects.append(DetectedObject(
                    class_name="eye",
                    confidence=0.7,
                    bbox=(x, y, w, h)
                ))
            
            logger.info(f"Basit algılama ile {len(detected_objects)} nesne tanındı")
            return detected_objects
            
        except Exception as e:
            logger.error(f"Basit nesne tanıma hatası: {e}")
            raise VisionError(f"Basit nesne tanıma hatası: {str(e)}")
    
    def detect_objects(self, 
                      image: np.ndarray, 
                      confidence_threshold: float = 0.5,
                      use_yolo: bool = True) -> List[DetectedObject]:
        """Nesne tanıma (YOLO veya basit)"""
        if use_yolo and self.is_loaded:
            return self.detect_objects_yolo(image, confidence_threshold)
        else:
            return self.detect_objects_simple(image, confidence_threshold)
    
    async def detect_objects_async(self, 
                                  image: np.ndarray, 
                                  confidence_threshold: float = 0.5,
                                  use_yolo: bool = True) -> List[DetectedObject]:
        """Async nesne tanıma"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.detect_objects, image, confidence_threshold, use_yolo
        )
    
    def draw_detections(self, 
                       image: np.ndarray, 
                       detections: List[DetectedObject],
                       draw_confidence: bool = True) -> np.ndarray:
        """Tanınan nesneleri görüntü üzerine çiz"""
        result_image = image.copy()
        
        for detection in detections:
            x, y, w, h = detection.bbox
            
            # Renk seç
            if detection.class_id is not None and detection.class_id < len(self.colors):
                color = self.colors[detection.class_id]
            else:
                color = (0, 255, 0)  # Varsayılan yeşil
            
            # Bounding box çiz
            cv2.rectangle(result_image, (x, y), (x + w, y + h), color, 2)
            
            # Etiket metni
            if draw_confidence:
                label = f"{detection.class_name}: {detection.confidence:.2f}"
            else:
                label = detection.class_name
            
            # Etiket arka planı
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            cv2.rectangle(result_image, (x, y - label_size[1] - 10), 
                         (x + label_size[0], y), color, -1)
            
            # Etiket metni
            cv2.putText(result_image, label, (x, y - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
        
        return result_image
    
    def get_detection_summary(self, detections: List[DetectedObject]) -> dict:
        """Tanıma özetini al"""
        summary = {
            "total_objects": len(detections),
            "classes": {},
            "average_confidence": 0.0,
            "objects": []
        }
        
        if detections:
            # Sınıf sayıları
            for detection in detections:
                class_name = detection.class_name
                if class_name in summary["classes"]:
                    summary["classes"][class_name] += 1
                else:
                    summary["classes"][class_name] = 1
                
                summary["objects"].append(detection.to_dict())
            
            # Ortalama güven
            total_confidence = sum(d.confidence for d in detections)
            summary["average_confidence"] = total_confidence / len(detections)
        
        return summary
