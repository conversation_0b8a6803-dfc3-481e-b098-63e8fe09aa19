"""
Memory Lifecycle Manager

<PERSON><PERSON><PERSON><PERSON> arasında veri yaşam dö<PERSON>ünü yöneten sistem.
"""

import asyncio
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from ..utils.logger import get_logger

logger = get_logger(__name__)


class MemoryPriority(Enum):
    """Hafıza öncelik seviyeleri"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class MemoryEntry:
    """Hafıza girişi"""
    id: str
    data: Dict[str, Any]
    timestamp: float
    priority: MemoryPriority
    access_count: int = 0
    last_access: float = 0
    tags: List[str] = None
    size_bytes: int = 0


@dataclass
class MigrationRule:
    """Veri taşıma kuralı"""
    from_layer: str
    to_layer: str
    condition: str  # age, access_count, priority, size
    threshold: Any
    enabled: bool = True


class MemoryLifecycleManager:
    """<PERSON><PERSON>ıza yaşam döngü<PERSON>ü yöneticisi"""
    
    def __init__(self):
        self.redis_layer = None
        self.sqlite_layer = None
        self.is_initialized = False
        
        # Migration kuralları
        self.migration_rules = [
            # L1 -> L2 (5 dakika sonra)
            MigrationRule("l1", "l2", "age", 300),
            
            # L2 -> L3 (1 saat sonra)
            MigrationRule("l2", "l3", "age", 3600),
            
            # L3 -> L4 (1 gün sonra)
            MigrationRule("l3", "l4", "age", 86400),
            
            # Yüksek öncelikli veriler daha uzun L1'de kalır
            MigrationRule("l1", "l2", "priority_age", {"priority": "high", "age": 900}),
            
            # Düşük öncelikli veriler hızlı taşınır
            MigrationRule("l1", "l2", "priority_age", {"priority": "low", "age": 60})
        ]
        
        # Cleanup kuralları
        self.cleanup_rules = {
            "l1_max_age": 3600,      # 1 saat
            "l2_max_age": 86400,     # 1 gün
            "l3_max_age": 604800,    # 1 hafta
            "l4_max_age": 2592000,   # 1 ay
            "max_total_size_mb": 1000  # 1GB
        }
        
        # Performans metrikleri
        self.metrics = {
            "migrations_performed": 0,
            "cleanups_performed": 0,
            "total_entries_managed": 0,
            "errors": 0,
            "last_migration_time": 0,
            "last_cleanup_time": 0
        }
        
        logger.info("Memory Lifecycle Manager oluşturuldu")
    
    async def initialize(self, redis_layer=None, sqlite_layer=None) -> None:
        """Lifecycle manager'ı başlat"""
        try:
            if self.is_initialized:
                return
            
            logger.info("Memory Lifecycle Manager başlatılıyor...")
            
            self.redis_layer = redis_layer
            self.sqlite_layer = sqlite_layer
            
            # Periyodik görevleri başlat
            asyncio.create_task(self._migration_loop())
            asyncio.create_task(self._cleanup_loop())
            
            self.is_initialized = True
            logger.info("Memory Lifecycle Manager başarıyla başlatıldı")
            
        except Exception as e:
            logger.error(f"Lifecycle Manager başlatma hatası: {e}")
            raise
    
    async def _migration_loop(self) -> None:
        """Periyodik migration döngüsü"""
        while True:
            try:
                await asyncio.sleep(300)  # 5 dakikada bir çalıştır
                await self.perform_migrations()
            except Exception as e:
                logger.error(f"Migration loop hatası: {e}")
                self.metrics["errors"] += 1
    
    async def _cleanup_loop(self) -> None:
        """Periyodik cleanup döngüsü"""
        while True:
            try:
                await asyncio.sleep(3600)  # 1 saatte bir çalıştır
                await self.cleanup_expired()
            except Exception as e:
                logger.error(f"Cleanup loop hatası: {e}")
                self.metrics["errors"] += 1
    
    async def perform_migrations(self) -> Dict[str, int]:
        """Veri migrasyonlarını gerçekleştir"""
        try:
            migration_stats = {
                "l1_to_l2": 0,
                "l2_to_l3": 0,
                "l3_to_l4": 0,
                "errors": 0
            }
            
            logger.debug("Migration işlemi başlatılıyor...")
            
            # L1 -> L2 migration
            if self.redis_layer:
                l1_keys = await self.redis_layer.get_keys("*", "l1")
                for key in l1_keys:
                    try:
                        data = await self.redis_layer.get_l1(key)
                        if data and self._should_migrate(data, "l1", "l2"):
                            await self.redis_layer.store_l2(key, data)
                            await self.redis_layer.delete(key, "l1")
                            migration_stats["l1_to_l2"] += 1
                    except Exception as e:
                        logger.error(f"L1->L2 migration hatası ({key}): {e}")
                        migration_stats["errors"] += 1
            
            # L2 -> L3 migration
            if self.redis_layer and self.sqlite_layer:
                l2_keys = await self.redis_layer.get_keys("*", "l2")
                for key in l2_keys:
                    try:
                        data = await self.redis_layer.get_l2(key)
                        if data and self._should_migrate(data, "l2", "l3"):
                            await self.sqlite_layer.store_l3(key, data)
                            await self.redis_layer.delete(key, "l2")
                            migration_stats["l2_to_l3"] += 1
                    except Exception as e:
                        logger.error(f"L2->L3 migration hatası ({key}): {e}")
                        migration_stats["errors"] += 1
            
            # L3 -> L4 migration
            if self.sqlite_layer:
                # SQLite'da L3 verilerini bul ve L4'e taşı
                # Bu implementasyon SQLite layer'da yapılacak
                pass
            
            total_migrations = sum(v for k, v in migration_stats.items() if k != "errors")
            if total_migrations > 0:
                logger.info(f"Migration tamamlandı: {migration_stats}")
            
            self.metrics["migrations_performed"] += total_migrations
            self.metrics["last_migration_time"] = time.time()
            
            return migration_stats
            
        except Exception as e:
            logger.error(f"Migration hatası: {e}")
            self.metrics["errors"] += 1
            return {"errors": 1}
    
    def _should_migrate(self, data: Dict[str, Any], from_layer: str, to_layer: str) -> bool:
        """Veri taşınmalı mı kontrol et"""
        try:
            current_time = time.time()
            data_timestamp = data.get("timestamp", current_time)
            data_priority = data.get("priority", "medium")
            
            # Yaş kontrolü
            age = current_time - data_timestamp
            
            for rule in self.migration_rules:
                if (rule.from_layer == from_layer and 
                    rule.to_layer == to_layer and 
                    rule.enabled):
                    
                    if rule.condition == "age":
                        return age > rule.threshold
                    
                    elif rule.condition == "priority_age":
                        threshold_data = rule.threshold
                        if (data_priority == threshold_data["priority"] and
                            age > threshold_data["age"]):
                            return True
            
            return False
            
        except Exception as e:
            logger.error(f"Migration kontrolü hatası: {e}")
            return False
    
    async def cleanup_expired(self) -> Dict[str, int]:
        """Süresi dolmuş verileri temizle"""
        try:
            cleanup_stats = {
                "l1_cleaned": 0,
                "l2_cleaned": 0,
                "l3_cleaned": 0,
                "l4_cleaned": 0,
                "errors": 0
            }
            
            logger.debug("Cleanup işlemi başlatılıyor...")
            current_time = time.time()
            
            # Redis katmanları temizliği
            if self.redis_layer:
                # L1 temizliği
                l1_keys = await self.redis_layer.get_keys("*", "l1")
                for key in l1_keys:
                    try:
                        data = await self.redis_layer.get_l1(key)
                        if data:
                            age = current_time - data.get("timestamp", current_time)
                            if age > self.cleanup_rules["l1_max_age"]:
                                await self.redis_layer.delete(key, "l1")
                                cleanup_stats["l1_cleaned"] += 1
                    except Exception as e:
                        logger.error(f"L1 cleanup hatası ({key}): {e}")
                        cleanup_stats["errors"] += 1
                
                # L2 temizliği
                l2_keys = await self.redis_layer.get_keys("*", "l2")
                for key in l2_keys:
                    try:
                        data = await self.redis_layer.get_l2(key)
                        if data:
                            age = current_time - data.get("timestamp", current_time)
                            if age > self.cleanup_rules["l2_max_age"]:
                                await self.redis_layer.delete(key, "l2")
                                cleanup_stats["l2_cleaned"] += 1
                    except Exception as e:
                        logger.error(f"L2 cleanup hatası ({key}): {e}")
                        cleanup_stats["errors"] += 1
            
            # SQLite katmanları temizliği SQLite layer'da otomatik yapılıyor
            
            total_cleaned = sum(v for k, v in cleanup_stats.items() if k != "errors")
            if total_cleaned > 0:
                logger.info(f"Cleanup tamamlandı: {cleanup_stats}")
            
            self.metrics["cleanups_performed"] += total_cleaned
            self.metrics["last_cleanup_time"] = time.time()
            
            return cleanup_stats
            
        except Exception as e:
            logger.error(f"Cleanup hatası: {e}")
            self.metrics["errors"] += 1
            return {"errors": 1}
    
    async def force_migration(self, key: str, from_layer: str, to_layer: str) -> bool:
        """Belirli bir veriyi zorla taşı"""
        try:
            data = None
            
            # Kaynak katmandan veriyi al
            if from_layer == "l1" and self.redis_layer:
                data = await self.redis_layer.get_l1(key)
            elif from_layer == "l2" and self.redis_layer:
                data = await self.redis_layer.get_l2(key)
            elif from_layer == "l3" and self.sqlite_layer:
                data = await self.sqlite_layer.get_l3(key)
            elif from_layer == "l4" and self.sqlite_layer:
                data = await self.sqlite_layer.get_l4(key)
            
            if not data:
                logger.warning(f"Veri bulunamadı: {key} ({from_layer})")
                return False
            
            # Hedef katmana kaydet
            success = False
            if to_layer == "l1" and self.redis_layer:
                success = await self.redis_layer.store_l1(key, data)
            elif to_layer == "l2" and self.redis_layer:
                success = await self.redis_layer.store_l2(key, data)
            elif to_layer == "l3" and self.sqlite_layer:
                success = await self.sqlite_layer.store_l3(key, data)
            elif to_layer == "l4" and self.sqlite_layer:
                success = await self.sqlite_layer.store_l4(key, data)
            
            if success:
                # Kaynak katmandan sil
                if from_layer in ["l1", "l2"] and self.redis_layer:
                    await self.redis_layer.delete(key, from_layer)
                elif from_layer in ["l3", "l4"] and self.sqlite_layer:
                    await self.sqlite_layer.delete(key, from_layer)
                
                logger.info(f"Zorla migration başarılı: {key} ({from_layer} -> {to_layer})")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Zorla migration hatası: {e}")
            return False
    
    async def get_memory_stats(self) -> Dict[str, Any]:
        """Hafıza istatistiklerini al"""
        try:
            stats = {
                "layers": {},
                "total_entries": 0,
                "total_size_mb": 0,
                "lifecycle_metrics": self.metrics
            }
            
            # Redis istatistikleri
            if self.redis_layer:
                redis_metrics = await self.redis_layer.get_metrics()
                stats["layers"]["redis"] = redis_metrics
            
            # SQLite istatistikleri
            if self.sqlite_layer:
                sqlite_metrics = await self.sqlite_layer.get_metrics()
                stats["layers"]["sqlite"] = sqlite_metrics
            
            return stats
            
        except Exception as e:
            logger.error(f"İstatistik alma hatası: {e}")
            return {"error": str(e)}
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Lifecycle manager metriklerini al"""
        return {
            **self.metrics,
            "migration_rules_count": len(self.migration_rules),
            "cleanup_rules": self.cleanup_rules,
            "is_initialized": self.is_initialized
        }
