"""
Example Context Plugin

Context plugin geli<PERSON><PERSON>rme için örnek implementasyon.
"""

import time
import asyncio
from typing import Dict, Any, List, Optional

from ..base_plugin import BaseContextPlugin
from ..plugin_manifest import PluginManifest, PluginType, PluginPermission
from ..plugin_data import PluginResponse, PluginData
from ...utils.logger import get_logger

logger = get_logger(__name__)


class ExampleContextPlugin(BaseContextPlugin):
    """Örnek context plugin implementasyonu"""
    
    def __init__(self, manifest: PluginManifest = None):
        # Manifest oluştur (eğer verilmemişse)
        if manifest is None:
            manifest = PluginManifest(
                name="example_context",
                version="1.0.0",
                type=PluginType.CONTEXT,
                description="Örnek context plugin - veri analizi ve bağlam oluşturma",
                author="LLM Vision Team",
                permissions=[
                    PluginPermission(name="system_info", description="Sistem bilgilerine erişim")
                ]
            )
        
        super().__init__(manifest)
        
        # Plugin özel ayarları
        self.analysis_cache = {}
        self.cache_ttl = 300  # 5 dakika
        self.max_cache_size = 100
        self.context_templates = {}
        
        # Analiz kuralları
        self.analysis_rules = {
            "cpu_threshold": 80.0,
            "memory_threshold": 85.0,
            "disk_threshold": 90.0,
            "process_threshold": 200
        }
    
    async def initialize(self, config: Dict[str, Any] = None) -> bool:
        """Plugin'i başlat"""
        try:
            logger.info("Example Context Plugin başlatılıyor...")
            
            # Konfigürasyonu uygula
            if config:
                self.cache_ttl = config.get("cache_ttl", 300)
                self.max_cache_size = config.get("max_cache_size", 100)
                self.analysis_rules.update(config.get("analysis_rules", {}))
            
            # Context template'lerini yükle
            await self._load_context_templates()
            
            # Test analizi çalıştır
            test_data = {"test": True, "timestamp": time.time()}
            test_result = await self.analyze_context(test_data)
            if not test_result.success:
                logger.error("Test analizi başarısız")
                return False
            
            logger.info("Example Context Plugin başarıyla başlatıldı")
            return True
            
        except Exception as e:
            logger.error(f"Plugin başlatma hatası: {e}")
            return False
    
    async def cleanup(self) -> None:
        """Plugin'i temizle"""
        try:
            logger.info("Example Context Plugin temizleniyor...")
            
            # Cache'i temizle
            self.analysis_cache.clear()
            self.context_templates.clear()
            
            logger.info("Example Context Plugin temizlendi")
            
        except Exception as e:
            logger.error(f"Plugin temizleme hatası: {e}")
    
    async def analyze_context(self, data: Dict[str, Any]) -> PluginResponse:
        """Bağlam analizi yap"""
        try:
            start_time = time.time()
            
            # Cache kontrolü
            cache_key = self._generate_cache_key(data)
            cached_result = self._get_from_cache(cache_key)
            if cached_result:
                execution_time = (time.time() - start_time) * 1000
                return PluginResponse.success_response(cached_result, execution_time)
            
            # Veri tipini belirle
            data_type = self._determine_data_type(data)
            
            # Analiz yap
            analysis_result = await self._perform_analysis(data, data_type)
            
            # Bağlam oluştur
            context = await self._build_context(data, analysis_result, data_type)
            
            # Öneriler oluştur
            suggestions = await self._generate_suggestions(analysis_result, data_type)
            
            # Sonucu birleştir
            result_data = {
                "data_type": data_type,
                "analysis": analysis_result,
                "context": context,
                "suggestions": suggestions,
                "confidence": self._calculate_confidence(analysis_result),
                "timestamp": time.time()
            }
            
            # Plugin data oluştur
            plugin_data = PluginData(
                source=self.manifest.name,
                data=result_data,
                confidence=result_data["confidence"],
                tags=["context", "analysis", data_type]
            )
            
            # Cache'e ekle
            self._add_to_cache(cache_key, plugin_data)
            
            execution_time = (time.time() - start_time) * 1000
            return PluginResponse.success_response(plugin_data, execution_time)
            
        except Exception as e:
            logger.error(f"Bağlam analizi hatası: {e}")
            execution_time = (time.time() - start_time) * 1000 if 'start_time' in locals() else 0
            return PluginResponse.error_response(
                error_message=str(e),
                error_code="CONTEXT_ANALYSIS_ERROR",
                execution_time_ms=execution_time
            )
    
    def get_context_schema(self) -> Dict[str, Any]:
        """Bağlam şemasını döndür"""
        return {
            "type": "object",
            "properties": {
                "data_type": {"type": "string"},
                "analysis": {
                    "type": "object",
                    "properties": {
                        "summary": {"type": "string"},
                        "metrics": {"type": "object"},
                        "alerts": {"type": "array"},
                        "trends": {"type": "object"}
                    }
                },
                "context": {
                    "type": "object",
                    "properties": {
                        "description": {"type": "string"},
                        "key_points": {"type": "array"},
                        "relationships": {"type": "array"}
                    }
                },
                "suggestions": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "type": {"type": "string"},
                            "message": {"type": "string"},
                            "priority": {"type": "string"},
                            "action": {"type": "string"}
                        }
                    }
                },
                "confidence": {"type": "number", "minimum": 0, "maximum": 1},
                "timestamp": {"type": "number"}
            },
            "required": ["data_type", "analysis", "context", "confidence"]
        }
    
    def get_schema(self) -> Dict[str, Any]:
        """Plugin veri şemasını döndür"""
        return self.get_context_schema()
    
    def _determine_data_type(self, data: Dict[str, Any]) -> str:
        """Veri tipini belirle"""
        if "cpu_percent" in data and "memory_percent" in data:
            return "system_metrics"
        elif "image" in data or "visual_objects" in data:
            return "vision_data"
        elif "text" in data or "content" in data:
            return "text_data"
        elif "file_path" in data or "files" in data:
            return "file_data"
        else:
            return "generic_data"
    
    async def _perform_analysis(self, data: Dict[str, Any], data_type: str) -> Dict[str, Any]:
        """Analiz gerçekleştir"""
        if data_type == "system_metrics":
            return await self._analyze_system_metrics(data)
        elif data_type == "vision_data":
            return await self._analyze_vision_data(data)
        elif data_type == "text_data":
            return await self._analyze_text_data(data)
        elif data_type == "file_data":
            return await self._analyze_file_data(data)
        else:
            return await self._analyze_generic_data(data)
    
    async def _analyze_system_metrics(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Sistem metriklerini analiz et"""
        cpu_percent = data.get("cpu_percent", 0)
        memory_percent = data.get("memory_percent", 0)
        disk_usage = data.get("disk_usage", {})
        process_count = data.get("process_count", 0)
        
        alerts = []
        
        # CPU kontrolü
        if cpu_percent > self.analysis_rules["cpu_threshold"]:
            alerts.append({
                "type": "warning",
                "message": f"Yüksek CPU kullanımı: {cpu_percent:.1f}%",
                "metric": "cpu_percent",
                "value": cpu_percent
            })
        
        # Memory kontrolü
        if memory_percent > self.analysis_rules["memory_threshold"]:
            alerts.append({
                "type": "warning",
                "message": f"Yüksek bellek kullanımı: {memory_percent:.1f}%",
                "metric": "memory_percent",
                "value": memory_percent
            })
        
        # Disk kontrolü
        if disk_usage.get("percent", 0) > self.analysis_rules["disk_threshold"]:
            alerts.append({
                "type": "critical",
                "message": f"Yüksek disk kullanımı: {disk_usage.get('percent', 0):.1f}%",
                "metric": "disk_percent",
                "value": disk_usage.get("percent", 0)
            })
        
        # Process kontrolü
        if process_count > self.analysis_rules["process_threshold"]:
            alerts.append({
                "type": "info",
                "message": f"Çok sayıda process: {process_count}",
                "metric": "process_count",
                "value": process_count
            })
        
        return {
            "summary": f"Sistem durumu: CPU {cpu_percent:.1f}%, RAM {memory_percent:.1f}%",
            "metrics": {
                "cpu_percent": cpu_percent,
                "memory_percent": memory_percent,
                "disk_percent": disk_usage.get("percent", 0),
                "process_count": process_count
            },
            "alerts": alerts,
            "trends": {
                "cpu_status": "high" if cpu_percent > 70 else "normal",
                "memory_status": "high" if memory_percent > 80 else "normal",
                "overall_health": "warning" if alerts else "good"
            }
        }
    
    async def _analyze_vision_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Görsel veriyi analiz et"""
        visual_objects = data.get("visual_objects", [])
        
        return {
            "summary": f"{len(visual_objects)} görsel nesne tespit edildi",
            "metrics": {
                "object_count": len(visual_objects),
                "object_types": list(set(obj.get("class", "unknown") for obj in visual_objects))
            },
            "alerts": [],
            "trends": {
                "complexity": "high" if len(visual_objects) > 10 else "normal"
            }
        }
    
    async def _analyze_text_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Metin verisini analiz et"""
        text = data.get("text", "")
        
        return {
            "summary": f"{len(text)} karakter metin analizi",
            "metrics": {
                "character_count": len(text),
                "word_count": len(text.split()) if text else 0
            },
            "alerts": [],
            "trends": {}
        }
    
    async def _analyze_file_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Dosya verisini analiz et"""
        files = data.get("files", [])
        
        return {
            "summary": f"{len(files)} dosya analizi",
            "metrics": {
                "file_count": len(files),
                "total_size": sum(f.get("size_bytes", 0) for f in files)
            },
            "alerts": [],
            "trends": {}
        }
    
    async def _analyze_generic_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Genel veri analizi"""
        return {
            "summary": "Genel veri analizi",
            "metrics": {
                "field_count": len(data),
                "data_keys": list(data.keys())
            },
            "alerts": [],
            "trends": {}
        }
    
    async def _build_context(self, data: Dict[str, Any], analysis: Dict[str, Any], data_type: str) -> Dict[str, Any]:
        """Bağlam oluştur"""
        template = self.context_templates.get(data_type, self.context_templates.get("default"))
        
        key_points = []
        relationships = []
        
        # Analiz sonuçlarından key point'ler çıkar
        if analysis.get("alerts"):
            key_points.extend([alert["message"] for alert in analysis["alerts"]])
        
        if analysis.get("metrics"):
            for metric, value in analysis["metrics"].items():
                key_points.append(f"{metric}: {value}")
        
        return {
            "description": template.format(
                summary=analysis.get("summary", ""),
                data_type=data_type
            ),
            "key_points": key_points,
            "relationships": relationships
        }
    
    async def _generate_suggestions(self, analysis: Dict[str, Any], data_type: str) -> List[Dict[str, Any]]:
        """Öneriler oluştur"""
        suggestions = []
        
        # Alert'lere göre öneriler
        for alert in analysis.get("alerts", []):
            if alert["type"] == "warning" and "cpu" in alert["message"].lower():
                suggestions.append({
                    "type": "optimization",
                    "message": "CPU kullanımını azaltmak için gereksiz process'leri kapatın",
                    "priority": "medium",
                    "action": "optimize_cpu"
                })
            elif alert["type"] == "warning" and "memory" in alert["message"].lower():
                suggestions.append({
                    "type": "optimization",
                    "message": "Bellek kullanımını azaltmak için uygulamaları yeniden başlatın",
                    "priority": "medium",
                    "action": "optimize_memory"
                })
            elif alert["type"] == "critical":
                suggestions.append({
                    "type": "urgent",
                    "message": "Kritik durum tespit edildi, hemen müdahale gerekli",
                    "priority": "high",
                    "action": "immediate_action"
                })
        
        return suggestions
    
    def _calculate_confidence(self, analysis: Dict[str, Any]) -> float:
        """Güven skorunu hesapla"""
        base_confidence = 0.8
        
        # Alert sayısına göre güven azalt
        alert_count = len(analysis.get("alerts", []))
        confidence_penalty = min(alert_count * 0.1, 0.3)
        
        return max(base_confidence - confidence_penalty, 0.1)
    
    async def _load_context_templates(self) -> None:
        """Context template'lerini yükle"""
        self.context_templates = {
            "system_metrics": "Sistem durumu analizi: {summary}. Veri tipi: {data_type}",
            "vision_data": "Görsel analiz sonucu: {summary}. Veri tipi: {data_type}",
            "text_data": "Metin analizi: {summary}. Veri tipi: {data_type}",
            "file_data": "Dosya analizi: {summary}. Veri tipi: {data_type}",
            "default": "Genel analiz: {summary}. Veri tipi: {data_type}"
        }
    
    def _generate_cache_key(self, data: Dict[str, Any]) -> str:
        """Cache key oluştur"""
        import hashlib
        data_str = str(sorted(data.items()))
        return hashlib.md5(data_str.encode()).hexdigest()
    
    def _get_from_cache(self, cache_key: str) -> Optional[PluginData]:
        """Cache'den veri al"""
        if cache_key in self.analysis_cache:
            cached_item = self.analysis_cache[cache_key]
            if time.time() - cached_item["timestamp"] < self.cache_ttl:
                return cached_item["data"]
            else:
                del self.analysis_cache[cache_key]
        return None
    
    def _add_to_cache(self, cache_key: str, data: PluginData) -> None:
        """Cache'e veri ekle"""
        # Cache boyutunu kontrol et
        if len(self.analysis_cache) >= self.max_cache_size:
            # En eski girişi kaldır
            oldest_key = min(self.analysis_cache.keys(), 
                           key=lambda k: self.analysis_cache[k]["timestamp"])
            del self.analysis_cache[oldest_key]
        
        self.analysis_cache[cache_key] = {
            "data": data,
            "timestamp": time.time()
        }
