"""
Hibrit hafıza sistemi - <PERSON> koor<PERSON>tör sınıfı
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass
from enum import Enum

from ..config import config
from ..utils.logger import get_logger
from ..utils.exceptions import LLMVisionError
from .redis_layer import RedisMemoryLayer
from .sqlite_layer import SQLiteMemoryLayer
from .lifecycle_manager import MemoryLifecycleManager

logger = get_logger(__name__)


class MemoryPriority(Enum):
    """Hafıza öncelik seviyeleri"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class MemoryLayer(Enum):
    """Hafıza katmanları"""
    L1_CACHE = "l1_cache"          # 5 dakika - Gerçek zamanlı context
    L2_SESSION = "l2_session"      # 24 saat - Oturum bazlı bağlam
    L3_SHORT_TERM = "l3_short"     # 7 gün - Kısa dönem pattern'ler
    L4_LONG_TERM = "l4_long"       # 90 gün - Uzun dönem öğrenme
    L5_ARCHIVE = "l5_archive"      # 1 yıl - Pasif analiz


@dataclass
class MemoryEntry:
    """Hafıza girişi"""
    id: str
    data: Dict[str, Any]
    priority: MemoryPriority
    layer: MemoryLayer
    timestamp: datetime
    ttl: Optional[int] = None
    tags: List[str] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None


class HybridMemorySystem:
    """Hibrit hafıza sistemi ana sınıfı"""
    
    def __init__(self):
        self.redis_layer = RedisMemoryLayer()
        self.sqlite_layer = SQLiteMemoryLayer()
        self.lifecycle_manager = MemoryLifecycleManager()
        
        self.is_initialized = False
        self.performance_metrics = {
            "total_entries": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "memory_usage_mb": 0
        }
        
        logger.info("Hibrit hafıza sistemi oluşturuldu")
    
    async def initialize(self) -> None:
        """Hafıza sistemini başlat"""
        try:
            if self.is_initialized:
                return
            
            logger.info("Hibrit hafıza sistemi başlatılıyor...")
            
            # Redis bağlantısını başlat
            await self.redis_layer.initialize()
            
            # SQLite veritabanını başlat
            await self.sqlite_layer.initialize()
            
            # Lifecycle manager'ı başlat
            await self.lifecycle_manager.initialize(
                redis_layer=self.redis_layer,
                sqlite_layer=self.sqlite_layer
            )
            
            # Temizleme görevini başlat
            asyncio.create_task(self._cleanup_task())
            
            self.is_initialized = True
            logger.info("Hibrit hafıza sistemi başarıyla başlatıldı")
            
        except Exception as e:
            logger.error(f"Hafıza sistemi başlatma hatası: {e}")
            raise LLMVisionError(f"Hafıza sistemi başlatma hatası: {str(e)}")
    
    async def store_context(self, 
                           data: Dict[str, Any],
                           priority: MemoryPriority = MemoryPriority.MEDIUM,
                           user_id: Optional[str] = None,
                           session_id: Optional[str] = None,
                           tags: List[str] = None) -> str:
        """Bağlam verisi sakla"""
        try:
            # Unique ID oluştur
            entry_id = f"ctx_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            
            # Katman ve TTL belirle
            layer, ttl = self._determine_storage_layer(priority)
            
            # Memory entry oluştur
            entry = MemoryEntry(
                id=entry_id,
                data=data,
                priority=priority,
                layer=layer,
                timestamp=datetime.now(),
                ttl=ttl,
                tags=tags or [],
                user_id=user_id,
                session_id=session_id
            )
            
            # Uygun katmana kaydet
            if layer in [MemoryLayer.L1_CACHE, MemoryLayer.L2_SESSION]:
                await self.redis_layer.store(entry)
            else:
                await self.sqlite_layer.store(entry)
            
            # Metrikleri güncelle
            self.performance_metrics["total_entries"] += 1
            
            logger.debug(f"Bağlam kaydedildi: {entry_id} -> {layer.value}")
            return entry_id
            
        except Exception as e:
            logger.error(f"Bağlam kaydetme hatası: {e}")
            raise LLMVisionError(f"Bağlam kaydetme hatası: {str(e)}")
    
    async def get_context(self, 
                         entry_id: str,
                         include_related: bool = True) -> Optional[MemoryEntry]:
        """Bağlam verisi al"""
        try:
            # Önce Redis'ten dene (L1/L2)
            entry = await self.redis_layer.get(entry_id)
            if entry:
                self.performance_metrics["cache_hits"] += 1
                return entry
            
            # SQLite'tan dene (L3/L4)
            entry = await self.sqlite_layer.get(entry_id)
            if entry:
                self.performance_metrics["cache_misses"] += 1
                
                # Sık kullanılan veriyi Redis'e taşı
                if entry.priority in [MemoryPriority.HIGH, MemoryPriority.CRITICAL]:
                    await self.redis_layer.store(entry)
                
                return entry
            
            # Bulunamadı
            self.performance_metrics["cache_misses"] += 1
            return None
            
        except Exception as e:
            logger.error(f"Bağlam alma hatası: {e}")
            return None
    
    async def search_context(self,
                           query: str,
                           tags: List[str] = None,
                           user_id: Optional[str] = None,
                           session_id: Optional[str] = None,
                           limit: int = 10) -> List[MemoryEntry]:
        """Bağlam arama"""
        try:
            results = []
            
            # Redis'te ara (son veriler)
            redis_results = await self.redis_layer.search(
                query=query, tags=tags, user_id=user_id, 
                session_id=session_id, limit=limit//2
            )
            results.extend(redis_results)
            
            # SQLite'ta ara (geçmiş veriler)
            sqlite_results = await self.sqlite_layer.search(
                query=query, tags=tags, user_id=user_id,
                session_id=session_id, limit=limit//2
            )
            results.extend(sqlite_results)
            
            # Timestamp'e göre sırala
            results.sort(key=lambda x: x.timestamp, reverse=True)
            
            return results[:limit]
            
        except Exception as e:
            logger.error(f"Bağlam arama hatası: {e}")
            return []
    
    async def get_session_context(self, 
                                 session_id: str,
                                 limit: int = 50) -> List[MemoryEntry]:
        """Oturum bağlamını al"""
        try:
            # Redis'ten oturum verilerini al
            session_data = await self.redis_layer.get_session_data(session_id, limit)
            
            # SQLite'tan eski oturum verilerini al
            if len(session_data) < limit:
                remaining = limit - len(session_data)
                old_data = await self.sqlite_layer.get_session_data(session_id, remaining)
                session_data.extend(old_data)
            
            return session_data
            
        except Exception as e:
            logger.error(f"Oturum bağlamı alma hatası: {e}")
            return []
    
    def _determine_storage_layer(self, priority: MemoryPriority) -> tuple[MemoryLayer, int]:
        """Önceliğe göre depolama katmanı ve TTL belirle"""
        if priority == MemoryPriority.CRITICAL:
            return MemoryLayer.L2_SESSION, config.memory.l2_session_ttl
        elif priority == MemoryPriority.HIGH:
            return MemoryLayer.L1_CACHE, config.memory.l1_cache_ttl
        elif priority == MemoryPriority.MEDIUM:
            return MemoryLayer.L3_SHORT_TERM, config.memory.l3_retention_days * 86400
        else:  # LOW
            return MemoryLayer.L4_LONG_TERM, config.memory.l4_retention_days * 86400
    
    async def _cleanup_task(self) -> None:
        """Periyodik temizleme görevi"""
        while True:
            try:
                await asyncio.sleep(config.memory.cleanup_interval_hours * 3600)
                await self.lifecycle_manager.cleanup_expired()
                logger.info("Hafıza temizleme tamamlandı")
            except Exception as e:
                logger.error(f"Hafıza temizleme hatası: {e}")
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Performans metriklerini al"""
        redis_metrics = await self.redis_layer.get_metrics()
        sqlite_metrics = await self.sqlite_layer.get_metrics()
        
        return {
            **self.performance_metrics,
            "redis": redis_metrics,
            "sqlite": sqlite_metrics,
            "lifecycle": await self.lifecycle_manager.get_metrics()
        }
    
    async def shutdown(self) -> None:
        """Hafıza sistemini kapat"""
        try:
            logger.info("Hibrit hafıza sistemi kapatılıyor...")
            
            await self.lifecycle_manager.shutdown()
            await self.sqlite_layer.shutdown()
            await self.redis_layer.shutdown()
            
            self.is_initialized = False
            logger.info("Hibrit hafıza sistemi kapatıldı")
            
        except Exception as e:
            logger.error(f"Hafıza sistemi kapatma hatası: {e}")
