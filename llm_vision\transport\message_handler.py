"""
<PERSON>j <PERSON> mod<PERSON>
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
import traceback

from ..utils.logger import get_logger
from ..utils.exceptions import LLMVisionError
from ..server.models import MCPRequest, MCPResponse, MCPError as MCPErrorModel
from .client_manager import ClientManager

logger = get_logger(__name__)


class MessageHandler:
    """Mesaj işleyici sınıfı"""
    
    def __init__(self, client_manager: ClientManager):
        self.client_manager = client_manager
        
        # Mesaj işleyicileri
        self.request_handlers: Dict[str, Callable] = {}
        self.response_handlers: Dict[str, Callable] = {}
        self.notification_handlers: Dict[str, Callable] = {}
        
        # Mesaj geçmişi
        self.message_history: List[Dict[str, Any]] = []
        self.max_history_size = 10000
        
        # <PERSON>statist<PERSON><PERSON>
        self.message_stats = {
            "total_requests": 0,
            "total_responses": 0,
            "total_notifications": 0,
            "total_errors": 0,
            "method_counts": {},
            "error_counts": {}
        }
        
        logger.info("Mesaj işleyici başlatıldı")
    
    def register_request_handler(self, 
                                method: str, 
                                handler: Callable) -> None:
        """İstek işleyici kaydet"""
        self.request_handlers[method] = handler
        logger.debug(f"İstek işleyici kaydedildi: {method}")
    
    def register_response_handler(self, 
                                 request_id: str, 
                                 handler: Callable) -> None:
        """Yanıt işleyici kaydet"""
        self.response_handlers[request_id] = handler
        logger.debug(f"Yanıt işleyici kaydedildi: {request_id}")
    
    def register_notification_handler(self, 
                                    method: str, 
                                    handler: Callable) -> None:
        """Bildirim işleyici kaydet"""
        self.notification_handlers[method] = handler
        logger.debug(f"Bildirim işleyici kaydedildi: {method}")
    
    async def handle_message(self, 
                           client_id: str, 
                           message: str) -> Optional[str]:
        """Gelen mesajı işle"""
        try:
            # İstemci aktivitesini güncelle
            self.client_manager.update_client_activity(client_id)
            
            # JSON parse et
            try:
                message_data = json.loads(message)
            except json.JSONDecodeError as e:
                logger.error(f"JSON parse hatası: {e}")
                return await self._create_error_response(
                    0, -32700, "Parse error", {"details": str(e)}
                )
            
            # Mesaj türünü belirle
            if "method" in message_data and "id" in message_data:
                # İstek mesajı
                return await self._handle_request(client_id, message_data)
            elif "result" in message_data or "error" in message_data:
                # Yanıt mesajı
                await self._handle_response(client_id, message_data)
                return None  # Yanıt mesajları için geri dönüş yok
            elif "method" in message_data and "id" not in message_data:
                # Bildirim mesajı
                await self._handle_notification(client_id, message_data)
                return None  # Bildirim mesajları için geri dönüş yok
            else:
                logger.error(f"Bilinmeyen mesaj formatı: {message_data}")
                return await self._create_error_response(
                    message_data.get("id", 0), -32600, "Invalid Request"
                )
                
        except Exception as e:
            logger.error(f"Mesaj işleme hatası: {e}")
            logger.error(traceback.format_exc())
            return await self._create_error_response(
                0, -32603, "Internal error", {"details": str(e)}
            )
    
    async def _handle_request(self, 
                            client_id: str, 
                            message_data: Dict[str, Any]) -> str:
        """İstek mesajını işle"""
        try:
            request = MCPRequest(**message_data)
            method = request.method
            
            # İstatistikleri güncelle
            self.message_stats["total_requests"] += 1
            self.message_stats["method_counts"][method] = \
                self.message_stats["method_counts"].get(method, 0) + 1
            
            # Mesaj geçmişine ekle
            self._add_to_history({
                "type": "request",
                "client_id": client_id,
                "method": method,
                "request_id": request.id,
                "timestamp": datetime.now().isoformat(),
                "data": message_data
            })
            
            # İşleyici bul ve çağır
            if method in self.request_handlers:
                handler = self.request_handlers[method]
                try:
                    response = await handler(client_id, request)
                    
                    # MCPResponse nesnesine çevir
                    if isinstance(response, dict):
                        response = MCPResponse(
                            id=request.id,
                            result=response
                        )
                    elif not isinstance(response, MCPResponse):
                        response = MCPResponse(
                            id=request.id,
                            result={"message": str(response)}
                        )
                    
                    self.message_stats["total_responses"] += 1
                    return response.model_dump_json()
                    
                except Exception as e:
                    logger.error(f"İstek işleyici hatası ({method}): {e}")
                    self.message_stats["total_errors"] += 1
                    self.message_stats["error_counts"][method] = \
                        self.message_stats["error_counts"].get(method, 0) + 1
                    
                    return await self._create_error_response(
                        request.id, -32603, "Internal error", {"details": str(e)}
                    )
            else:
                logger.warning(f"İşleyici bulunamadı: {method}")
                return await self._create_error_response(
                    request.id, -32601, "Method not found", {"method": method}
                )
                
        except Exception as e:
            logger.error(f"İstek işleme hatası: {e}")
            return await self._create_error_response(
                message_data.get("id", 0), -32602, "Invalid params", {"details": str(e)}
            )
    
    async def _handle_response(self, 
                             client_id: str, 
                             message_data: Dict[str, Any]) -> None:
        """Yanıt mesajını işle"""
        try:
            request_id = message_data.get("id")
            if not request_id:
                logger.warning("Yanıt mesajında ID yok")
                return
            
            # İstatistikleri güncelle
            self.message_stats["total_responses"] += 1
            
            # Mesaj geçmişine ekle
            self._add_to_history({
                "type": "response",
                "client_id": client_id,
                "request_id": request_id,
                "timestamp": datetime.now().isoformat(),
                "data": message_data
            })
            
            # Yanıt işleyici bul ve çağır
            if request_id in self.response_handlers:
                handler = self.response_handlers[request_id]
                try:
                    await handler(client_id, message_data)
                    # İşleyiciyi temizle
                    del self.response_handlers[request_id]
                except Exception as e:
                    logger.error(f"Yanıt işleyici hatası: {e}")
            else:
                logger.debug(f"Yanıt işleyici bulunamadı: {request_id}")
                
        except Exception as e:
            logger.error(f"Yanıt işleme hatası: {e}")
    
    async def _handle_notification(self, 
                                 client_id: str, 
                                 message_data: Dict[str, Any]) -> None:
        """Bildirim mesajını işle"""
        try:
            method = message_data.get("method")
            if not method:
                logger.warning("Bildirim mesajında method yok")
                return
            
            # İstatistikleri güncelle
            self.message_stats["total_notifications"] += 1
            
            # Mesaj geçmişine ekle
            self._add_to_history({
                "type": "notification",
                "client_id": client_id,
                "method": method,
                "timestamp": datetime.now().isoformat(),
                "data": message_data
            })
            
            # Bildirim işleyici bul ve çağır
            if method in self.notification_handlers:
                handler = self.notification_handlers[method]
                try:
                    await handler(client_id, message_data)
                except Exception as e:
                    logger.error(f"Bildirim işleyici hatası ({method}): {e}")
            else:
                logger.debug(f"Bildirim işleyici bulunamadı: {method}")
                
        except Exception as e:
            logger.error(f"Bildirim işleme hatası: {e}")
    
    async def _create_error_response(self, 
                                   request_id: Any, 
                                   error_code: int, 
                                   error_message: str,
                                   error_data: Dict[str, Any] = None) -> str:
        """Hata yanıtı oluştur"""
        try:
            error_response = MCPResponse(
                id=request_id,
                error=MCPErrorModel(
                    code=error_code,
                    message=error_message,
                    data=error_data
                ).model_dump()
            )
            
            return error_response.model_dump_json()
            
        except Exception as e:
            logger.error(f"Hata yanıtı oluşturma hatası: {e}")
            # Fallback basit JSON
            return json.dumps({
                "jsonrpc": "2.0",
                "id": request_id,
                "error": {
                    "code": -32603,
                    "message": "Internal error",
                    "data": {"details": str(e)}
                }
            })
    
    def _add_to_history(self, message_entry: Dict[str, Any]) -> None:
        """Mesajı geçmişe ekle"""
        try:
            self.message_history.append(message_entry)
            
            # Maksimum boyut kontrolü
            if len(self.message_history) > self.max_history_size:
                self.message_history.pop(0)
                
        except Exception as e:
            logger.error(f"Mesaj geçmişi ekleme hatası: {e}")
    
    def get_message_history(self, 
                           limit: int = 100,
                           client_id: str = None,
                           message_type: str = None) -> List[Dict[str, Any]]:
        """Mesaj geçmişini al"""
        try:
            history = self.message_history.copy()
            
            # İstemci filtresi
            if client_id:
                history = [h for h in history if h.get("client_id") == client_id]
            
            # Tür filtresi
            if message_type:
                history = [h for h in history if h.get("type") == message_type]
            
            # Sırala ve sınırla
            history.sort(key=lambda x: x["timestamp"], reverse=True)
            return history[:limit]
            
        except Exception as e:
            logger.error(f"Mesaj geçmişi alma hatası: {e}")
            return []
    
    def get_message_stats(self) -> Dict[str, Any]:
        """Mesaj istatistiklerini al"""
        try:
            return {
                **self.message_stats,
                "history_size": len(self.message_history),
                "registered_handlers": {
                    "requests": len(self.request_handlers),
                    "responses": len(self.response_handlers),
                    "notifications": len(self.notification_handlers)
                }
            }
            
        except Exception as e:
            logger.error(f"Mesaj istatistik alma hatası: {e}")
            return {}
    
    def clear_history(self) -> int:
        """Mesaj geçmişini temizle"""
        try:
            count = len(self.message_history)
            self.message_history.clear()
            logger.info(f"Mesaj geçmişi temizlendi ({count} mesaj)")
            return count
            
        except Exception as e:
            logger.error(f"Mesaj geçmişi temizleme hatası: {e}")
            return 0
    
    def clear_response_handlers(self) -> int:
        """Bekleyen yanıt işleyicilerini temizle"""
        try:
            count = len(self.response_handlers)
            self.response_handlers.clear()
            logger.info(f"Yanıt işleyicileri temizlendi ({count} işleyici)")
            return count
            
        except Exception as e:
            logger.error(f"Yanıt işleyici temizleme hatası: {e}")
            return 0
    
    def get_pending_responses(self) -> List[str]:
        """Bekleyen yanıt ID'lerini al"""
        return list(self.response_handlers.keys())
    
    def has_pending_response(self, request_id: str) -> bool:
        """Bekleyen yanıt var mı kontrol et"""
        return request_id in self.response_handlers
