"""
Bağlam oluşturucu modülü
"""

import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import json

from ..utils.logger import get_logger
from ..utils.exceptions import ContextError
from .prompt_manager import PromptManager

logger = get_logger(__name__)


class ContextBuilder:
    """Bağlam oluşturucu sınıfı"""
    
    def __init__(self):
        self.prompt_manager = PromptManager()
        self.context_cache = {}
        self.cache_ttl = 300  # 5 dakika
        
        logger.info("Bağlam oluşturucu başlatıldı")
    
    async def build_vision_context(self, 
                                  vision_data: Dict[str, Any]) -> Dict[str, Any]:
        """Görüntü verilerinden bağlam oluştur"""
        try:
            objects = vision_data.get("objects", [])
            texts = vision_data.get("text", [])
            image_info = vision_data.get("image_info", {})
            
            # Nesne analizi
            object_summary = self._analyze_objects(objects)
            
            # Metin analizi
            text_summary = self._analyze_texts(texts)
            
            # Genel görüntü analizi
            image_analysis = self._analyze_image_properties(image_info)
            
            # Bağlam oluştur
            context = {
                "type": "vision",
                "timestamp": datetime.now().isoformat(),
                "summary": self._create_vision_summary(object_summary, text_summary, image_analysis),
                "details": {
                    "objects": object_summary,
                    "texts": text_summary,
                    "image": image_analysis
                },
                "prompt": self.prompt_manager.create_vision_prompt(objects, texts, image_info),
                "confidence": self._calculate_vision_confidence(objects, texts)
            }
            
            return context
            
        except Exception as e:
            logger.error(f"Vision bağlam oluşturma hatası: {e}")
            raise ContextError(f"Vision bağlam oluşturma hatası: {str(e)}")
    
    def _analyze_objects(self, objects: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Nesneleri analiz et"""
        if not objects:
            return {
                "count": 0,
                "categories": [],
                "confidence_avg": 0,
                "description": "Görüntüde nesne tespit edilmedi"
            }
        
        # Kategori sayıları
        categories = {}
        confidences = []
        
        for obj in objects:
            class_name = obj.get("class_name", "unknown")
            confidence = obj.get("confidence", 0)
            
            categories[class_name] = categories.get(class_name, 0) + 1
            confidences.append(confidence)
        
        # En yaygın kategoriler
        top_categories = sorted(categories.items(), key=lambda x: x[1], reverse=True)[:5]
        
        return {
            "count": len(objects),
            "categories": dict(top_categories),
            "confidence_avg": sum(confidences) / len(confidences) if confidences else 0,
            "confidence_max": max(confidences) if confidences else 0,
            "confidence_min": min(confidences) if confidences else 0,
            "description": self._create_object_description(top_categories)
        }
    
    def _analyze_texts(self, texts: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Metinleri analiz et"""
        if not texts:
            return {
                "count": 0,
                "total_chars": 0,
                "languages": [],
                "description": "Görüntüde metin tespit edilmedi"
            }
        
        total_chars = 0
        all_text = []
        confidences = []
        
        for text in texts:
            text_content = text.get("text", "")
            confidence = text.get("confidence", 0)
            
            total_chars += len(text_content)
            all_text.append(text_content)
            confidences.append(confidence)
        
        # Birleşik metin
        combined_text = " ".join(all_text)
        
        return {
            "count": len(texts),
            "total_chars": total_chars,
            "confidence_avg": sum(confidences) / len(confidences) if confidences else 0,
            "preview": combined_text[:200] + "..." if len(combined_text) > 200 else combined_text,
            "full_text": combined_text,
            "description": f"{len(texts)} metin bloğu tespit edildi, toplam {total_chars} karakter"
        }
    
    def _analyze_image_properties(self, image_info: Dict[str, Any]) -> Dict[str, Any]:
        """Görüntü özelliklerini analiz et"""
        shape = image_info.get("shape", [0, 0, 0])
        size = image_info.get("size", 0)
        
        if len(shape) >= 2:
            height, width = shape[0], shape[1]
            channels = shape[2] if len(shape) > 2 else 1
        else:
            height = width = channels = 0
        
        # Görüntü kalitesi değerlendirmesi
        quality = "unknown"
        if width > 0 and height > 0:
            total_pixels = width * height
            if total_pixels > 2000000:  # 2MP+
                quality = "high"
            elif total_pixels > 500000:  # 0.5MP+
                quality = "medium"
            else:
                quality = "low"
        
        return {
            "width": width,
            "height": height,
            "channels": channels,
            "total_pixels": width * height,
            "aspect_ratio": width / height if height > 0 else 0,
            "quality": quality,
            "size_mb": size / (1024 * 1024) if size > 0 else 0,
            "description": f"{width}x{height} çözünürlükte {quality} kalite görüntü"
        }
    
    def _create_vision_summary(self, 
                              object_summary: Dict[str, Any],
                              text_summary: Dict[str, Any],
                              image_analysis: Dict[str, Any]) -> str:
        """Görüntü özeti oluştur"""
        summary_parts = []
        
        # Görüntü bilgisi
        summary_parts.append(image_analysis["description"])
        
        # Nesne bilgisi
        if object_summary["count"] > 0:
            summary_parts.append(object_summary["description"])
        
        # Metin bilgisi
        if text_summary["count"] > 0:
            summary_parts.append(text_summary["description"])
        
        return ". ".join(summary_parts)
    
    def _create_object_description(self, top_categories: List[tuple]) -> str:
        """Nesne açıklaması oluştur"""
        if not top_categories:
            return "Nesne bulunamadı"
        
        descriptions = []
        for category, count in top_categories[:3]:  # İlk 3 kategori
            if count == 1:
                descriptions.append(f"1 {category}")
            else:
                descriptions.append(f"{count} {category}")
        
        return f"Görüntüde {', '.join(descriptions)} tespit edildi"
    
    def _calculate_vision_confidence(self, 
                                   objects: List[Dict[str, Any]],
                                   texts: List[Dict[str, Any]]) -> float:
        """Görüntü analizi güven skoru hesapla"""
        confidences = []
        
        # Nesne güven skorları
        for obj in objects:
            confidences.append(obj.get("confidence", 0))
        
        # Metin güven skorları
        for text in texts:
            confidences.append(text.get("confidence", 0))
        
        return sum(confidences) / len(confidences) if confidences else 0
    
    async def build_system_context(self, 
                                  system_data: Dict[str, Any]) -> Dict[str, Any]:
        """Sistem verilerinden bağlam oluştur"""
        try:
            # Sistem analizi
            system_summary = self._analyze_system_performance(system_data)
            
            # Bağlam oluştur
            context = {
                "type": "system",
                "timestamp": datetime.now().isoformat(),
                "summary": system_summary["description"],
                "details": system_summary,
                "prompt": self.prompt_manager.create_system_prompt(system_data),
                "health_score": system_summary.get("health_score", 0)
            }
            
            return context
            
        except Exception as e:
            logger.error(f"System bağlam oluşturma hatası: {e}")
            raise ContextError(f"System bağlam oluşturma hatası: {str(e)}")
    
    def _analyze_system_performance(self, system_data: Dict[str, Any]) -> Dict[str, Any]:
        """Sistem performansını analiz et"""
        cpu_info = system_data.get("cpu", {})
        memory_info = system_data.get("memory", {})
        disk_info = system_data.get("disk", {})
        platform_info = system_data.get("platform", {})
        
        cpu_usage = cpu_info.get("usage", 0)
        memory_usage = memory_info.get("usage", 0)
        disk_usage = disk_info.get("usage", 0)
        
        # Sağlık skoru hesapla (0-100)
        health_score = 100
        if cpu_usage > 80:
            health_score -= 30
        elif cpu_usage > 60:
            health_score -= 15
        
        if memory_usage > 90:
            health_score -= 25
        elif memory_usage > 70:
            health_score -= 10
        
        if disk_usage > 95:
            health_score -= 20
        elif disk_usage > 80:
            health_score -= 10
        
        # Durum kategorisi
        if health_score >= 80:
            status = "excellent"
        elif health_score >= 60:
            status = "good"
        elif health_score >= 40:
            status = "fair"
        else:
            status = "poor"
        
        return {
            "cpu_usage": cpu_usage,
            "memory_usage": memory_usage,
            "disk_usage": disk_usage,
            "health_score": max(0, health_score),
            "status": status,
            "platform": platform_info.get("system", "unknown"),
            "warnings": self._generate_system_warnings(cpu_usage, memory_usage, disk_usage),
            "description": f"Sistem durumu {status}, sağlık skoru: {health_score}/100"
        }
    
    def _generate_system_warnings(self, cpu: float, memory: float, disk: float) -> List[str]:
        """Sistem uyarıları oluştur"""
        warnings = []
        
        if cpu > 80:
            warnings.append("Yüksek CPU kullanımı")
        if memory > 90:
            warnings.append("Kritik bellek kullanımı")
        if disk > 95:
            warnings.append("Disk alanı kritik seviyede")
        elif disk > 80:
            warnings.append("Disk alanı azalıyor")
        
        return warnings
    
    async def build_comprehensive_context(self, 
                                        multiple_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Çoklu veriden kapsamlı bağlam oluştur"""
        try:
            # Veri türlerini grupla
            grouped_data = self._group_data_by_type(multiple_data)
            
            # Her tür için özet oluştur
            summaries = {}
            for data_type, items in grouped_data.items():
                summaries[data_type] = await self._create_type_summary(data_type, items)
            
            # Genel özet
            general_summary = self._create_general_summary(summaries, multiple_data)
            
            # Bağlam oluştur
            context = {
                "type": "comprehensive",
                "timestamp": datetime.now().isoformat(),
                "summary": general_summary["description"],
                "details": {
                    "general": general_summary,
                    "by_type": summaries
                },
                "data_count": len(multiple_data),
                "data_types": list(grouped_data.keys()),
                "time_range": self._calculate_time_range(multiple_data)
            }
            
            return context
            
        except Exception as e:
            logger.error(f"Comprehensive bağlam oluşturma hatası: {e}")
            raise ContextError(f"Comprehensive bağlam oluşturma hatası: {str(e)}")
    
    def _group_data_by_type(self, data_list: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """Verileri türe göre grupla"""
        grouped = {}
        
        for item in data_list:
            data_type = item.get("type", "unknown")
            if data_type not in grouped:
                grouped[data_type] = []
            grouped[data_type].append(item)
        
        return grouped
    
    async def _create_type_summary(self, data_type: str, items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Belirli tür için özet oluştur"""
        count = len(items)
        latest_item = max(items, key=lambda x: x.get("timestamp", "")) if items else None
        
        summary = {
            "count": count,
            "latest_timestamp": latest_item.get("timestamp") if latest_item else None,
            "description": f"{count} {data_type} verisi"
        }
        
        # Türe özel analiz
        if data_type == "file_change":
            change_types = {}
            for item in items:
                change_type = item.get("data", {}).get("event_type", "unknown")
                change_types[change_type] = change_types.get(change_type, 0) + 1
            summary["change_types"] = change_types
            
        elif data_type == "vision":
            total_objects = sum(len(item.get("data", {}).get("objects", [])) for item in items)
            summary["total_objects_detected"] = total_objects
            
        elif data_type == "system_info":
            if latest_item:
                system_data = latest_item.get("data", {})
                summary["latest_cpu"] = system_data.get("cpu", {}).get("usage", 0)
                summary["latest_memory"] = system_data.get("memory", {}).get("usage", 0)
        
        return summary
    
    def _create_general_summary(self, 
                               summaries: Dict[str, Dict[str, Any]], 
                               all_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Genel özet oluştur"""
        total_items = len(all_data)
        data_types = list(summaries.keys())
        
        # En aktif veri türü
        most_active_type = max(summaries.items(), key=lambda x: x[1]["count"])[0] if summaries else "none"
        
        # Son aktivite
        latest_item = max(all_data, key=lambda x: x.get("timestamp", "")) if all_data else None
        latest_activity = latest_item.get("type", "unknown") if latest_item else "none"
        
        return {
            "total_items": total_items,
            "data_types": data_types,
            "most_active_type": most_active_type,
            "latest_activity": latest_activity,
            "latest_timestamp": latest_item.get("timestamp") if latest_item else None,
            "description": f"Toplam {total_items} veri girişi, {len(data_types)} farklı tür"
        }
    
    def _calculate_time_range(self, data_list: List[Dict[str, Any]]) -> Dict[str, str]:
        """Zaman aralığını hesapla"""
        if not data_list:
            return {"start": None, "end": None}
        
        timestamps = [item.get("timestamp") for item in data_list if item.get("timestamp")]
        
        if not timestamps:
            return {"start": None, "end": None}
        
        return {
            "start": min(timestamps),
            "end": max(timestamps)
        }
