"""
Bildirim yönetim<PERSON> modül<PERSON>
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Set, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from ..utils.logger import get_logger
from ..utils.exceptions import LLMVisionError
from .client_manager import ClientManager

logger = get_logger(__name__)


class NotificationType(str, Enum):
    """Bildirim türleri"""
    RESOURCE_CHANGED = "notifications/resources/list_changed"
    RESOURCE_UPDATED = "notifications/resources/updated"
    TOOL_CHANGED = "notifications/tools/list_changed"
    PROGRESS = "notifications/progress"
    LOG = "notifications/log"
    CUSTOM = "notifications/custom"


@dataclass
class NotificationMessage:
    """Bildirim mesajı sınıfı"""
    method: str
    params: Dict[str, Any]
    target_clients: Optional[List[str]] = None
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
    
    def to_dict(self) -> dict:
        """Sözlük formatına çevir"""
        return {
            "jsonrpc": "2.0",
            "method": self.method,
            "params": self.params
        }
    
    def to_json(self) -> str:
        """JSON formatına çevir"""
        return json.dumps(self.to_dict())


class NotificationManager:
    """Bildirim yöneticisi sınıfı"""
    
    def __init__(self, client_manager: ClientManager):
        self.client_manager = client_manager
        
        # Bildirim kuyruğu
        self.notification_queue: List[NotificationMessage] = []
        self.max_queue_size = 10000
        
        # Bildirim geçmişi
        self.notification_history: List[Dict[str, Any]] = []
        self.max_history_size = 5000
        
        # Filtreler ve abonelikler
        self.notification_filters: Dict[str, Callable] = {}
        self.client_preferences: Dict[str, Dict[str, Any]] = {}
        
        # İstatistikler
        self.notification_stats = {
            "total_sent": 0,
            "total_failed": 0,
            "type_counts": {},
            "client_counts": {}
        }
        
        # Batch işleme
        self.batch_notifications: Dict[str, List[NotificationMessage]] = {}
        self.batch_timeout = 1.0  # 1 saniye
        
        logger.info("Bildirim yöneticisi başlatıldı")
    
    async def send_notification(self, 
                              notification: NotificationMessage,
                              immediate: bool = True) -> Dict[str, bool]:
        """Bildirim gönder"""
        try:
            if immediate:
                return await self._send_immediate(notification)
            else:
                await self._add_to_batch(notification)
                return {"queued": True}
                
        except Exception as e:
            logger.error(f"Bildirim gönderme hatası: {e}")
            return {"error": str(e)}
    
    async def _send_immediate(self, 
                            notification: NotificationMessage) -> Dict[str, bool]:
        """Anında bildirim gönder"""
        try:
            results = {}
            
            # Hedef istemcileri belirle
            target_clients = notification.target_clients
            if target_clients is None:
                # Tüm aktif istemcilere gönder
                target_clients = [
                    client.client_id 
                    for client in self.client_manager.get_active_clients()
                ]
            
            # Her istemciye gönder
            for client_id in target_clients:
                try:
                    success = await self._send_to_client(client_id, notification)
                    results[client_id] = success
                    
                    # İstatistikleri güncelle
                    if success:
                        self.notification_stats["total_sent"] += 1
                        self.notification_stats["client_counts"][client_id] = \
                            self.notification_stats["client_counts"].get(client_id, 0) + 1
                    else:
                        self.notification_stats["total_failed"] += 1
                        
                except Exception as e:
                    logger.error(f"İstemciye bildirim gönderme hatası ({client_id}): {e}")
                    results[client_id] = False
                    self.notification_stats["total_failed"] += 1
            
            # Geçmişe ekle
            self._add_to_history(notification, results)
            
            # İstatistikleri güncelle
            notification_type = notification.method
            self.notification_stats["type_counts"][notification_type] = \
                self.notification_stats["type_counts"].get(notification_type, 0) + 1
            
            return results
            
        except Exception as e:
            logger.error(f"Anında bildirim gönderme hatası: {e}")
            return {"error": str(e)}
    
    async def _send_to_client(self, 
                            client_id: str, 
                            notification: NotificationMessage) -> bool:
        """Belirli istemciye bildirim gönder"""
        try:
            # İstemci bağlantısını al
            connection = self.client_manager.get_client_connection(client_id)
            if not connection:
                logger.warning(f"İstemci bağlantısı bulunamadı: {client_id}")
                return False
            
            # İstemci tercihlerini kontrol et
            if not self._should_send_to_client(client_id, notification):
                logger.debug(f"Bildirim filtre edildi: {client_id}")
                return True  # Filtrelenmiş olarak başarılı sayılır
            
            # Bildirim mesajını gönder
            message = notification.to_json()
            
            # WebSocket bağlantısı için
            if hasattr(connection, 'send_text'):
                await connection.send_text(message)
                return True
            # Diğer bağlantı türleri için
            elif hasattr(connection, 'send'):
                await connection.send(message)
                return True
            else:
                logger.error(f"Desteklenmeyen bağlantı türü: {type(connection)}")
                return False
                
        except Exception as e:
            logger.error(f"İstemciye bildirim gönderme hatası: {e}")
            return False
    
    def _should_send_to_client(self, 
                              client_id: str, 
                              notification: NotificationMessage) -> bool:
        """İstemciye bildirim gönderilmeli mi kontrol et"""
        try:
            # İstemci tercihleri
            preferences = self.client_preferences.get(client_id, {})
            
            # Bildirim türü filtresi
            disabled_types = preferences.get("disabled_types", [])
            if notification.method in disabled_types:
                return False
            
            # Özel filtreler
            for filter_name, filter_func in self.notification_filters.items():
                try:
                    if not filter_func(client_id, notification):
                        return False
                except Exception as e:
                    logger.error(f"Filtre hatası ({filter_name}): {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"İstemci filtre kontrolü hatası: {e}")
            return True  # Hata durumunda gönder
    
    async def _add_to_batch(self, notification: NotificationMessage) -> None:
        """Batch'e bildirim ekle"""
        try:
            batch_key = notification.method
            
            if batch_key not in self.batch_notifications:
                self.batch_notifications[batch_key] = []
                # Batch timeout başlat
                asyncio.create_task(self._process_batch_after_timeout(batch_key))
            
            self.batch_notifications[batch_key].append(notification)
            
            logger.debug(f"Bildirim batch'e eklendi: {batch_key}")
            
        except Exception as e:
            logger.error(f"Batch ekleme hatası: {e}")
    
    async def _process_batch_after_timeout(self, batch_key: str) -> None:
        """Timeout sonrası batch'i işle"""
        try:
            await asyncio.sleep(self.batch_timeout)
            
            if batch_key in self.batch_notifications:
                notifications = self.batch_notifications.pop(batch_key)
                
                if notifications:
                    logger.info(f"Batch işleniyor: {batch_key} ({len(notifications)} bildirim)")
                    
                    # Batch'teki tüm bildirimleri gönder
                    for notification in notifications:
                        await self._send_immediate(notification)
                        
        except Exception as e:
            logger.error(f"Batch işleme hatası: {e}")
    
    def _add_to_history(self, 
                       notification: NotificationMessage, 
                       results: Dict[str, bool]) -> None:
        """Bildirim geçmişine ekle"""
        try:
            history_entry = {
                "notification": {
                    "method": notification.method,
                    "params": notification.params,
                    "target_clients": notification.target_clients,
                    "created_at": notification.created_at.isoformat()
                },
                "results": results,
                "timestamp": datetime.now().isoformat(),
                "success_count": sum(1 for success in results.values() if success),
                "total_count": len(results)
            }
            
            self.notification_history.append(history_entry)
            
            # Maksimum boyut kontrolü
            if len(self.notification_history) > self.max_history_size:
                self.notification_history.pop(0)
                
        except Exception as e:
            logger.error(f"Geçmiş ekleme hatası: {e}")
    
    async def broadcast_resource_changed(self, 
                                       resource_uri: str,
                                       change_type: str = "updated") -> Dict[str, bool]:
        """Kaynak değişiklik bildirimi gönder"""
        try:
            notification = NotificationMessage(
                method=NotificationType.RESOURCE_CHANGED,
                params={
                    "uri": resource_uri,
                    "change_type": change_type,
                    "timestamp": datetime.now().isoformat()
                }
            )
            
            # Sadece bu kaynağa abone olan istemcilere gönder
            subscribers = self.client_manager.get_subscribers(resource_uri)
            if subscribers:
                notification.target_clients = subscribers
            
            return await self.send_notification(notification)
            
        except Exception as e:
            logger.error(f"Kaynak değişiklik bildirimi hatası: {e}")
            return {"error": str(e)}
    
    async def broadcast_tool_changed(self) -> Dict[str, bool]:
        """Araç listesi değişiklik bildirimi gönder"""
        try:
            notification = NotificationMessage(
                method=NotificationType.TOOL_CHANGED,
                params={
                    "timestamp": datetime.now().isoformat()
                }
            )
            
            return await self.send_notification(notification)
            
        except Exception as e:
            logger.error(f"Araç değişiklik bildirimi hatası: {e}")
            return {"error": str(e)}
    
    async def send_progress_notification(self, 
                                       client_id: str,
                                       progress_token: str,
                                       progress: float,
                                       message: str = None) -> bool:
        """İlerleme bildirimi gönder"""
        try:
            notification = NotificationMessage(
                method=NotificationType.PROGRESS,
                params={
                    "progressToken": progress_token,
                    "progress": progress,
                    "message": message,
                    "timestamp": datetime.now().isoformat()
                },
                target_clients=[client_id]
            )
            
            results = await self.send_notification(notification)
            return results.get(client_id, False)
            
        except Exception as e:
            logger.error(f"İlerleme bildirimi hatası: {e}")
            return False
    
    async def send_log_notification(self, 
                                  level: str,
                                  message: str,
                                  data: Dict[str, Any] = None) -> Dict[str, bool]:
        """Log bildirimi gönder"""
        try:
            notification = NotificationMessage(
                method=NotificationType.LOG,
                params={
                    "level": level,
                    "message": message,
                    "data": data or {},
                    "timestamp": datetime.now().isoformat()
                }
            )
            
            return await self.send_notification(notification)
            
        except Exception as e:
            logger.error(f"Log bildirimi hatası: {e}")
            return {"error": str(e)}
    
    def set_client_preferences(self, 
                              client_id: str, 
                              preferences: Dict[str, Any]) -> None:
        """İstemci tercihlerini ayarla"""
        try:
            self.client_preferences[client_id] = preferences
            logger.debug(f"İstemci tercihleri ayarlandı: {client_id}")
            
        except Exception as e:
            logger.error(f"İstemci tercih ayarlama hatası: {e}")
    
    def add_notification_filter(self, 
                               name: str, 
                               filter_func: Callable) -> None:
        """Bildirim filtresi ekle"""
        try:
            self.notification_filters[name] = filter_func
            logger.debug(f"Bildirim filtresi eklendi: {name}")
            
        except Exception as e:
            logger.error(f"Filtre ekleme hatası: {e}")
    
    def remove_notification_filter(self, name: str) -> bool:
        """Bildirim filtresini kaldır"""
        try:
            if name in self.notification_filters:
                del self.notification_filters[name]
                logger.debug(f"Bildirim filtresi kaldırıldı: {name}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Filtre kaldırma hatası: {e}")
            return False
    
    def get_notification_stats(self) -> Dict[str, Any]:
        """Bildirim istatistiklerini al"""
        try:
            return {
                **self.notification_stats,
                "queue_size": len(self.notification_queue),
                "history_size": len(self.notification_history),
                "batch_count": len(self.batch_notifications),
                "filter_count": len(self.notification_filters),
                "client_preferences_count": len(self.client_preferences)
            }
            
        except Exception as e:
            logger.error(f"Bildirim istatistik alma hatası: {e}")
            return {}
    
    def get_notification_history(self, 
                               limit: int = 100,
                               notification_type: str = None) -> List[Dict[str, Any]]:
        """Bildirim geçmişini al"""
        try:
            history = self.notification_history.copy()
            
            # Tür filtresi
            if notification_type:
                history = [
                    h for h in history 
                    if h["notification"]["method"] == notification_type
                ]
            
            # Sırala ve sınırla
            history.sort(key=lambda x: x["timestamp"], reverse=True)
            return history[:limit]
            
        except Exception as e:
            logger.error(f"Bildirim geçmişi alma hatası: {e}")
            return []
    
    def clear_history(self) -> int:
        """Bildirim geçmişini temizle"""
        try:
            count = len(self.notification_history)
            self.notification_history.clear()
            logger.info(f"Bildirim geçmişi temizlendi ({count} giriş)")
            return count
            
        except Exception as e:
            logger.error(f"Geçmiş temizleme hatası: {e}")
            return 0
