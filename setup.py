"""
LLM Vision System kurulum dosyası
"""

from setuptools import setup, find_packages
from pathlib import Path

# README dosyasını oku
this_directory = Path(__file__).parent
long_description = (this_directory / "README.md").read_text(encoding='utf-8') if (this_directory / "README.md").exists() else ""

setup(
    name="llm-vision-system",
    version="0.1.0",
    author="LLM Vision Team",
    author_email="<EMAIL>",
    description="Görüntü analizi ve bağlam oluşturma sistemi",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/llm-vision/llm-vision-system",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Scientific/Engineering :: Image Processing",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=[
        # Core dependencies
        "fastapi>=0.104.0",
        "uvicorn[standard]>=0.24.0",
        "pydantic>=2.5.0",
        "asyncio-mqtt>=0.16.0",
        
        # Vision processing
        "opencv-python>=4.8.0",
        "pillow>=10.0.0",
        "numpy>=1.24.0",
        
        # OCR
        "pytesseract>=0.3.10",
        "easyocr>=1.7.0",
        
        # Object detection (optional)
        "ultralytics>=8.0.0",  # YOLO
        
        # LLM integration
        "openai>=1.3.0",
        "aiohttp>=3.9.0",
        
        # File system monitoring
        "watchdog>=3.0.0",
        
        # System monitoring
        "psutil>=5.9.0",
        
        # Configuration
        "pyyaml>=6.0.0",
        "python-dotenv>=1.0.0",
        
        # Logging
        "colorlog>=6.7.0",
        
        # MCP protocol
        "websockets>=12.0",
        "jsonrpc-base>=2.2.0",
        
        # Utilities
        "click>=8.1.0",
        "rich>=13.0.0",
        "tqdm>=4.66.0",
    ],
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.1.0",
            "black>=23.0.0",
            "isort>=5.12.0",
            "flake8>=6.0.0",
            "mypy>=1.5.0",
        ],
        "gpu": [
            "torch>=2.0.0",
            "torchvision>=0.15.0",
            "onnxruntime-gpu>=1.16.0",
        ],
        "full": [
            "torch>=2.0.0",
            "torchvision>=0.15.0",
            "transformers>=4.35.0",
            "sentence-transformers>=2.2.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "llm-vision=llm_vision.__main__:run",
            "llm-vision-status=llm_vision.cli.status:main",
            "llm-vision-test=llm_vision.cli.test:main",
        ],
    },
    include_package_data=True,
    package_data={
        "llm_vision": [
            "config/*.yaml",
            "config/*.json",
            "templates/*.html",
            "static/*",
        ],
    },
    zip_safe=False,
    keywords="llm vision ai mcp claude desktop image processing context analysis",
    project_urls={
        "Bug Reports": "https://github.com/llm-vision/llm-vision-system/issues",
        "Source": "https://github.com/llm-vision/llm-vision-system",
        "Documentation": "https://llm-vision.readthedocs.io/",
    },
)
