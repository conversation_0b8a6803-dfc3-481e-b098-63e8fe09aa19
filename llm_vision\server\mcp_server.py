"""
Ana MCP sunucu sınıfı
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, Set
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from ..config import config
from ..utils.logger import get_logger
from ..utils.exceptions import MCPError
from .models import (
    MCPRequest, MCPResponse, MCPError as MCPErrorModel,
    InitializeRequest, InitializeResponse, ServerInfo,
    MCPMethod, MCPVersion
)
from .handlers import ResourceHandler, ToolHandler

logger = get_logger(__name__)


class MCPServer:
    """Ana MCP sunucu sınıfı"""
    
    def __init__(self):
        self.app = FastAPI(
            title="LLM Vision MCP Server",
            description="LLM'lere gerçek zamanlı veri sağlayan MCP sunucusu",
            version=config.mcp.server_version
        )
        
        # CORS middleware ekle
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Handler'ları başlat
        self.resource_handler = ResourceHandler()
        self.tool_handler = ToolHandler()
        
        # Bağlı istemciler
        self.connected_clients: Set[WebSocket] = set()
        
        # Sunucu durumu
        self.is_initialized = False
        self.server_info = ServerInfo(
            name=config.mcp.server_name,
            version=config.mcp.server_version,
            protocol_version=config.mcp.protocol_version
        )
        
        # Route'ları kaydet
        self._register_routes()
    
    def _register_routes(self) -> None:
        """HTTP ve WebSocket route'larını kaydet"""
        
        @self.app.get("/")
        async def root():
            """Ana sayfa"""
            return {
                "name": self.server_info.name,
                "version": self.server_info.version,
                "protocol_version": self.server_info.protocol_version,
                "status": "running"
            }
        
        @self.app.get("/health")
        async def health():
            """Sağlık kontrolü"""
            return {"status": "healthy", "initialized": self.is_initialized}
        
        @self.app.post("/mcp")
        async def handle_mcp_request(request: MCPRequest):
            """HTTP üzerinden MCP isteklerini işle"""
            try:
                response = await self._process_request(request)
                return response
            except Exception as e:
                logger.error(f"MCP istek işleme hatası: {e}")
                return MCPResponse(
                    id=request.id,
                    error=MCPErrorModel(
                        code=-32603,
                        message="Internal error",
                        data={"details": str(e)}
                    ).dict()
                )
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket bağlantısını işle"""
            await self._handle_websocket(websocket)
    
    async def _handle_websocket(self, websocket: WebSocket) -> None:
        """WebSocket bağlantısını yönet"""
        await websocket.accept()
        self.connected_clients.add(websocket)
        logger.info(f"Yeni WebSocket bağlantısı: {websocket.client}")
        
        try:
            while True:
                # Mesaj al
                data = await websocket.receive_text()
                
                try:
                    # JSON parse et
                    request_data = json.loads(data)
                    request = MCPRequest(**request_data)
                    
                    # İsteği işle
                    response = await self._process_request(request)
                    
                    # Yanıtı gönder
                    await websocket.send_text(response.json())
                    
                except json.JSONDecodeError as e:
                    logger.error(f"JSON parse hatası: {e}")
                    error_response = MCPResponse(
                        id=0,
                        error=MCPErrorModel(
                            code=-32700,
                            message="Parse error"
                        ).dict()
                    )
                    await websocket.send_text(error_response.json())
                
                except Exception as e:
                    logger.error(f"WebSocket istek işleme hatası: {e}")
                    error_response = MCPResponse(
                        id=getattr(request, 'id', 0),
                        error=MCPErrorModel(
                            code=-32603,
                            message="Internal error",
                            data={"details": str(e)}
                        ).dict()
                    )
                    await websocket.send_text(error_response.json())
        
        except WebSocketDisconnect:
            logger.info(f"WebSocket bağlantısı kesildi: {websocket.client}")
        
        finally:
            self.connected_clients.discard(websocket)
    
    async def _process_request(self, request: MCPRequest) -> MCPResponse:
        """MCP isteğini işle"""
        logger.info(f"MCP isteği işleniyor: {request.method}")
        
        try:
            if request.method == MCPMethod.INITIALIZE:
                return await self._handle_initialize(request)
            elif request.method == MCPMethod.LIST_RESOURCES:
                return await self._handle_list_resources(request)
            elif request.method == MCPMethod.READ_RESOURCE:
                return await self._handle_read_resource(request)
            elif request.method == MCPMethod.LIST_TOOLS:
                return await self._handle_list_tools(request)
            elif request.method == MCPMethod.CALL_TOOL:
                return await self._handle_call_tool(request)
            else:
                return MCPResponse(
                    id=request.id,
                    error=MCPErrorModel(
                        code=-32601,
                        message="Method not found",
                        data={"method": request.method}
                    ).dict()
                )
        
        except Exception as e:
            logger.error(f"İstek işleme hatası: {e}")
            return MCPResponse(
                id=request.id,
                error=MCPErrorModel(
                    code=-32603,
                    message="Internal error",
                    data={"details": str(e)}
                ).dict()
            )

    async def _handle_initialize(self, request: MCPRequest) -> MCPResponse:
        """Initialize isteğini işle"""
        try:
            if not request.params:
                raise MCPError("Initialize parametreleri eksik")

            init_request = InitializeRequest(**request.params)

            # Protokol versiyonu kontrolü
            if init_request.protocol_version != self.server_info.protocol_version:
                logger.warning(
                    f"Protokol versiyonu uyumsuzluğu: "
                    f"İstemci: {init_request.protocol_version}, "
                    f"Sunucu: {self.server_info.protocol_version}"
                )

            # Sunucu yeteneklerini tanımla
            server_capabilities = {
                "resources": {
                    "subscribe": True,
                    "list_changed": True
                },
                "tools": {
                    "list_changed": True
                }
            }

            # Initialize yanıtı oluştur
            init_response = InitializeResponse(
                protocol_version=self.server_info.protocol_version,
                server_info=self.server_info,
                capabilities=server_capabilities
            )

            self.is_initialized = True
            logger.info(f"Sunucu başlatıldı - İstemci: {init_request.client_info.name}")

            return MCPResponse(
                id=request.id,
                result=init_response.model_dump()
            )

        except Exception as e:
            logger.error(f"Initialize hatası: {e}")
            return MCPResponse(
                id=request.id,
                error=MCPErrorModel(
                    code=-32602,
                    message="Invalid params",
                    data={"details": str(e)}
                ).model_dump()
            )

    async def _handle_list_resources(self, request: MCPRequest) -> MCPResponse:
        """Kaynakları listele"""
        try:
            resources = await self.resource_handler.list_resources()
            return MCPResponse(
                id=request.id,
                result={
                    "resources": [resource.model_dump() for resource in resources]
                }
            )
        except Exception as e:
            logger.error(f"Kaynak listeleme hatası: {e}")
            return MCPResponse(
                id=request.id,
                error=MCPErrorModel(
                    code=-32603,
                    message="Internal error",
                    data={"details": str(e)}
                ).model_dump()
            )

    async def _handle_read_resource(self, request: MCPRequest) -> MCPResponse:
        """Kaynak oku"""
        try:
            if not request.params or "uri" not in request.params:
                raise MCPError("URI parametresi eksik")

            uri = request.params["uri"]
            content = await self.resource_handler.read_resource(uri)

            return MCPResponse(
                id=request.id,
                result={
                    "contents": [content.model_dump()]
                }
            )
        except Exception as e:
            logger.error(f"Kaynak okuma hatası: {e}")
            return MCPResponse(
                id=request.id,
                error=MCPErrorModel(
                    code=-32603,
                    message="Internal error",
                    data={"details": str(e)}
                ).model_dump()
            )

    async def _handle_list_tools(self, request: MCPRequest) -> MCPResponse:
        """Araçları listele"""
        try:
            tools = await self.tool_handler.list_tools()
            return MCPResponse(
                id=request.id,
                result={
                    "tools": [tool.model_dump() for tool in tools]
                }
            )
        except Exception as e:
            logger.error(f"Araç listeleme hatası: {e}")
            return MCPResponse(
                id=request.id,
                error=MCPErrorModel(
                    code=-32603,
                    message="Internal error",
                    data={"details": str(e)}
                ).model_dump()
            )

    async def _handle_call_tool(self, request: MCPRequest) -> MCPResponse:
        """Araç çağır"""
        try:
            if not request.params or "name" not in request.params:
                raise MCPError("Araç adı eksik")

            name = request.params["name"]
            arguments = request.params.get("arguments", {})

            result = await self.tool_handler.call_tool(name, arguments)

            return MCPResponse(
                id=request.id,
                result=result.model_dump()
            )
        except Exception as e:
            logger.error(f"Araç çağırma hatası: {e}")
            return MCPResponse(
                id=request.id,
                error=MCPErrorModel(
                    code=-32603,
                    message="Internal error",
                    data={"details": str(e)}
                ).model_dump()
            )

    async def broadcast_notification(self, method: str, params: Dict[str, Any]) -> None:
        """Tüm bağlı istemcilere bildirim gönder"""
        if not self.connected_clients:
            return

        notification = {
            "jsonrpc": "2.0",
            "method": method,
            "params": params
        }

        message = json.dumps(notification)
        disconnected_clients = set()

        for client in self.connected_clients:
            try:
                await client.send_text(message)
            except Exception as e:
                logger.warning(f"İstemciye bildirim gönderme hatası: {e}")
                disconnected_clients.add(client)

        # Bağlantısı kesilen istemcileri temizle
        self.connected_clients -= disconnected_clients

    async def start(self, host: str = None, port: int = None) -> None:
        """Sunucuyu başlat"""
        host = host or config.server.host
        port = port or config.server.port

        logger.info(f"MCP Sunucusu başlatılıyor: {host}:{port}")

        uvicorn_config = uvicorn.Config(
            self.app,
            host=host,
            port=port,
            log_level="info" if config.server.debug else "warning"
        )

        server = uvicorn.Server(uvicorn_config)
        await server.serve()

    def run(self, host: str = None, port: int = None) -> None:
        """Sunucuyu senkron olarak çalıştır"""
        asyncio.run(self.start(host, port))
