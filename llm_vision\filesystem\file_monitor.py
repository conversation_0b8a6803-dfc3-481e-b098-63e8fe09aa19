"""
Dosya sistemi izleme modülü
"""

import os
import asyncio
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
from pathlib import Path
import hashlib
import mimetypes
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileSystemEvent

from ..config import config
from ..utils.logger import get_logger
from ..utils.exceptions import FileSystemError

logger = get_logger(__name__)


class FileChangeEvent:
    """Dosya değişiklik olayı"""
    
    def __init__(self, 
                 event_type: str,
                 file_path: str,
                 timestamp: datetime = None,
                 file_info: Dict[str, Any] = None):
        self.event_type = event_type  # created, modified, deleted, moved
        self.file_path = file_path
        self.timestamp = timestamp or datetime.now()
        self.file_info = file_info or {}
    
    def to_dict(self) -> dict:
        """Sözlük formatına çevir"""
        return {
            "event_type": self.event_type,
            "file_path": self.file_path,
            "timestamp": self.timestamp.isoformat(),
            "file_info": self.file_info
        }


class FileSystemWatcher(FileSystemEventHandler):
    """Dosya sistemi izleyici"""
    
    def __init__(self, callback: Callable[[FileChangeEvent], None]):
        super().__init__()
        self.callback = callback
        self.last_events: Dict[str, datetime] = {}
        self.debounce_time = timedelta(seconds=1)  # Çift olay önleme
    
    def _should_process_event(self, file_path: str) -> bool:
        """Olayın işlenmesi gerekip gerekmediğini kontrol et"""
        now = datetime.now()
        last_time = self.last_events.get(file_path)
        
        if last_time and (now - last_time) < self.debounce_time:
            return False
        
        self.last_events[file_path] = now
        return True
    
    def _get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Dosya bilgilerini al"""
        try:
            if not os.path.exists(file_path):
                return {}
            
            stat = os.stat(file_path)
            mime_type, _ = mimetypes.guess_type(file_path)
            
            return {
                "size": stat.st_size,
                "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "created_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "mime_type": mime_type,
                "is_directory": os.path.isdir(file_path),
                "extension": Path(file_path).suffix.lower()
            }
        except Exception as e:
            logger.error(f"Dosya bilgisi alma hatası: {e}")
            return {}
    
    def on_created(self, event: FileSystemEvent):
        """Dosya oluşturuldu"""
        if not self._should_process_event(event.src_path):
            return
        
        file_info = self._get_file_info(event.src_path)
        change_event = FileChangeEvent("created", event.src_path, file_info=file_info)
        self.callback(change_event)
    
    def on_modified(self, event: FileSystemEvent):
        """Dosya değiştirildi"""
        if event.is_directory:
            return
        
        if not self._should_process_event(event.src_path):
            return
        
        file_info = self._get_file_info(event.src_path)
        change_event = FileChangeEvent("modified", event.src_path, file_info=file_info)
        self.callback(change_event)
    
    def on_deleted(self, event: FileSystemEvent):
        """Dosya silindi"""
        if not self._should_process_event(event.src_path):
            return
        
        change_event = FileChangeEvent("deleted", event.src_path)
        self.callback(change_event)
    
    def on_moved(self, event: FileSystemEvent):
        """Dosya taşındı"""
        if hasattr(event, 'dest_path'):
            if not self._should_process_event(event.dest_path):
                return
            
            file_info = self._get_file_info(event.dest_path)
            file_info["old_path"] = event.src_path
            change_event = FileChangeEvent("moved", event.dest_path, file_info=file_info)
            self.callback(change_event)


class FileMonitor:
    """Dosya sistemi izleyici sınıfı"""
    
    def __init__(self):
        self.watch_directories = config.filesystem.watch_directories
        self.max_file_size = config.filesystem.max_file_size_mb * 1024 * 1024
        
        self.observers: List[Observer] = []
        self.is_monitoring = False
        self.event_callbacks: List[Callable[[FileChangeEvent], None]] = []
        self.recent_events: List[FileChangeEvent] = []
        self.max_recent_events = 1000
        
        logger.info(f"Dosya izleyici başlatıldı - Dizinler: {self.watch_directories}")
    
    def add_callback(self, callback: Callable[[FileChangeEvent], None]) -> None:
        """Olay callback'i ekle"""
        self.event_callbacks.append(callback)
    
    def remove_callback(self, callback: Callable[[FileChangeEvent], None]) -> None:
        """Olay callback'ini kaldır"""
        if callback in self.event_callbacks:
            self.event_callbacks.remove(callback)
    
    def _handle_file_event(self, event: FileChangeEvent) -> None:
        """Dosya olayını işle"""
        try:
            # Son olayları sakla
            self.recent_events.append(event)
            if len(self.recent_events) > self.max_recent_events:
                self.recent_events.pop(0)
            
            # Callback'leri çağır
            for callback in self.event_callbacks:
                try:
                    callback(event)
                except Exception as e:
                    logger.error(f"Callback hatası: {e}")
            
            logger.debug(f"Dosya olayı: {event.event_type} - {event.file_path}")
            
        except Exception as e:
            logger.error(f"Dosya olayı işleme hatası: {e}")
    
    def start_monitoring(self) -> bool:
        """İzlemeyi başlat"""
        try:
            if self.is_monitoring:
                logger.warning("İzleme zaten aktif")
                return True
            
            for directory in self.watch_directories:
                # Dizin yolunu genişlet
                expanded_dir = os.path.expanduser(directory)
                
                if not os.path.exists(expanded_dir):
                    logger.warning(f"İzlenecek dizin bulunamadı: {expanded_dir}")
                    continue
                
                # Observer oluştur
                observer = Observer()
                event_handler = FileSystemWatcher(self._handle_file_event)
                
                observer.schedule(event_handler, expanded_dir, recursive=True)
                observer.start()
                
                self.observers.append(observer)
                logger.info(f"Dizin izlemeye alındı: {expanded_dir}")
            
            if self.observers:
                self.is_monitoring = True
                logger.info("Dosya sistemi izleme başlatıldı")
                return True
            else:
                logger.error("Hiçbir dizin izlemeye alınamadı")
                return False
                
        except Exception as e:
            logger.error(f"İzleme başlatma hatası: {e}")
            return False
    
    def stop_monitoring(self) -> None:
        """İzlemeyi durdur"""
        try:
            for observer in self.observers:
                observer.stop()
                observer.join(timeout=5)
            
            self.observers.clear()
            self.is_monitoring = False
            logger.info("Dosya sistemi izleme durduruldu")
            
        except Exception as e:
            logger.error(f"İzleme durdurma hatası: {e}")
    
    def get_recent_events(self, 
                         limit: int = 50,
                         event_type: str = None,
                         since: datetime = None) -> List[FileChangeEvent]:
        """Son olayları al"""
        events = self.recent_events.copy()
        
        # Filtrele
        if event_type:
            events = [e for e in events if e.event_type == event_type]
        
        if since:
            events = [e for e in events if e.timestamp >= since]
        
        # Sırala ve sınırla
        events.sort(key=lambda x: x.timestamp, reverse=True)
        return events[:limit]
    
    def get_file_changes_summary(self, 
                               since: datetime = None) -> Dict[str, Any]:
        """Dosya değişiklik özetini al"""
        if since is None:
            since = datetime.now() - timedelta(hours=1)
        
        events = self.get_recent_events(since=since)
        
        summary = {
            "total_events": len(events),
            "event_types": {},
            "file_types": {},
            "directories": {},
            "recent_files": []
        }
        
        for event in events:
            # Olay tipi sayısı
            event_type = event.event_type
            summary["event_types"][event_type] = summary["event_types"].get(event_type, 0) + 1
            
            # Dosya tipi sayısı
            if event.file_info.get("extension"):
                ext = event.file_info["extension"]
                summary["file_types"][ext] = summary["file_types"].get(ext, 0) + 1
            
            # Dizin sayısı
            directory = os.path.dirname(event.file_path)
            summary["directories"][directory] = summary["directories"].get(directory, 0) + 1
            
            # Son dosyalar
            if len(summary["recent_files"]) < 10:
                summary["recent_files"].append({
                    "path": event.file_path,
                    "event_type": event.event_type,
                    "timestamp": event.timestamp.isoformat()
                })
        
        return summary
    
    def scan_directory(self, 
                      directory: str,
                      recursive: bool = True,
                      include_hidden: bool = False) -> List[Dict[str, Any]]:
        """Dizini tara"""
        try:
            files = []
            directory = os.path.expanduser(directory)
            
            if not os.path.exists(directory):
                raise FileSystemError(f"Dizin bulunamadı: {directory}")
            
            if recursive:
                for root, dirs, filenames in os.walk(directory):
                    # Gizli dizinleri atla
                    if not include_hidden:
                        dirs[:] = [d for d in dirs if not d.startswith('.')]
                    
                    for filename in filenames:
                        # Gizli dosyaları atla
                        if not include_hidden and filename.startswith('.'):
                            continue
                        
                        filepath = os.path.join(root, filename)
                        file_info = self._get_file_info(filepath)
                        if file_info:
                            files.append(file_info)
            else:
                for item in os.listdir(directory):
                    if not include_hidden and item.startswith('.'):
                        continue
                    
                    filepath = os.path.join(directory, item)
                    if os.path.isfile(filepath):
                        file_info = self._get_file_info(filepath)
                        if file_info:
                            files.append(file_info)
            
            return files
            
        except Exception as e:
            logger.error(f"Dizin tarama hatası: {e}")
            raise FileSystemError(f"Dizin tarama hatası: {str(e)}")
    
    def _get_file_info(self, file_path: str) -> Dict[str, Any]:
        """Detaylı dosya bilgisi al"""
        try:
            if not os.path.exists(file_path):
                return {}
            
            stat = os.stat(file_path)
            mime_type, _ = mimetypes.guess_type(file_path)
            path_obj = Path(file_path)
            
            # Dosya hash'i (küçük dosyalar için)
            file_hash = None
            if stat.st_size < self.max_file_size and stat.st_size > 0:
                try:
                    with open(file_path, 'rb') as f:
                        file_hash = hashlib.md5(f.read()).hexdigest()
                except Exception:
                    pass
            
            return {
                "path": file_path,
                "name": path_obj.name,
                "size": stat.st_size,
                "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "created_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "accessed_time": datetime.fromtimestamp(stat.st_atime).isoformat(),
                "mime_type": mime_type,
                "extension": path_obj.suffix.lower(),
                "is_directory": os.path.isdir(file_path),
                "is_hidden": path_obj.name.startswith('.'),
                "hash": file_hash,
                "permissions": oct(stat.st_mode)[-3:]
            }
            
        except Exception as e:
            logger.error(f"Dosya bilgisi alma hatası: {e}")
            return {}
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """İzleme durumunu al"""
        return {
            "is_monitoring": self.is_monitoring,
            "watch_directories": self.watch_directories,
            "active_observers": len(self.observers),
            "recent_events_count": len(self.recent_events),
            "callbacks_count": len(self.event_callbacks)
        }
    
    def __enter__(self):
        """Context manager giriş"""
        self.start_monitoring()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager çıkış"""
        self.stop_monitoring()
