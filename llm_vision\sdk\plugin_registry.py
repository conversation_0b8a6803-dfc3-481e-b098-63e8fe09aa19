"""
Plugin Registry System

Plugin'le<PERSON> ka<PERSON>, yönetilmesi ve keşfedilmesi için merkezi sistem.
"""

from typing import Dict, List, Optional, Type, Any, Callable
import asyncio
from pathlib import Path
import json
import time
from dataclasses import dataclass, field

from .base_plugin import BasePlugin
from .plugin_manifest import PluginManifest, PluginStatus, PluginType
from .plugin_loader import PluginLoader
from .security_manager import SecurityManager
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class PluginRegistryEntry:
    """Plugin registry girişi"""
    plugin: BasePlugin
    manifest: PluginManifest
    registration_time: float = field(default_factory=time.time)
    last_health_check: Optional[float] = None
    health_status: bool = True
    load_path: Optional[Path] = None


class PluginRegistry:
    """Plugin registry ana sınıfı"""
    
    def __init__(self):
        self.plugins: Dict[str, PluginRegistryEntry] = {}
        self.plugin_loader = PluginLoader()
        self.security_manager = SecurityManager()
        
        # Plugin discovery paths
        self.discovery_paths: List[Path] = []
        
        # Event callbacks
        self.event_callbacks: Dict[str, List[Callable]] = {
            "plugin_registered": [],
            "plugin_unregistered": [],
            "plugin_activated": [],
            "plugin_deactivated": [],
            "plugin_error": []
        }
        
        # Registry durumu
        self.is_initialized = False
        self.auto_discovery_enabled = True
        self.health_check_interval = 60.0  # saniye
        
        logger.info("Plugin Registry oluşturuldu")
    
    async def initialize(self, discovery_paths: List[str] = None) -> None:
        """Registry'yi başlat"""
        try:
            if self.is_initialized:
                return
            
            logger.info("Plugin Registry başlatılıyor...")
            
            # Discovery path'leri ayarla
            if discovery_paths:
                self.discovery_paths = [Path(p) for p in discovery_paths]
            else:
                # Varsayılan path'ler
                self.discovery_paths = [
                    Path("plugins"),
                    Path("llm_vision/plugins"),
                    Path.home() / ".llm_vision" / "plugins"
                ]
            
            # Plugin loader'ı başlat
            await self.plugin_loader.initialize()
            
            # Security manager'ı başlat
            await self.security_manager.initialize()
            
            # Auto discovery başlat
            if self.auto_discovery_enabled:
                await self.discover_plugins()
            
            # Health check task'ını başlat
            asyncio.create_task(self._health_check_loop())
            
            self.is_initialized = True
            logger.info("Plugin Registry başarıyla başlatıldı")
            
        except Exception as e:
            logger.error(f"Plugin Registry başlatma hatası: {e}")
            raise
    
    async def register_plugin(self, plugin: BasePlugin, load_path: Path = None) -> bool:
        """
        Plugin'i registry'ye kaydet
        
        Args:
            plugin: Kaydedilecek plugin
            load_path: Plugin'in yüklendiği path
            
        Returns:
            bool: Kayıt başarılı mı
        """
        try:
            plugin_name = plugin.manifest.name
            
            # Aynı isimde plugin var mı kontrol et
            if plugin_name in self.plugins:
                logger.warning(f"Plugin zaten kayıtlı: {plugin_name}")
                return False
            
            # Güvenlik kontrolü
            if not await self.security_manager.validate_plugin(plugin):
                logger.error(f"Plugin güvenlik kontrolünden geçemedi: {plugin_name}")
                return False
            
            # Registry'ye ekle
            entry = PluginRegistryEntry(
                plugin=plugin,
                manifest=plugin.manifest,
                load_path=load_path
            )
            self.plugins[plugin_name] = entry
            
            # Event callback'lerini ayarla
            plugin.add_event_callback(self._handle_plugin_event)
            
            logger.info(f"Plugin kaydedildi: {plugin_name}")
            await self._emit_event("plugin_registered", {"plugin_name": plugin_name})
            
            return True
            
        except Exception as e:
            logger.error(f"Plugin kayıt hatası: {e}")
            return False
    
    async def unregister_plugin(self, plugin_name: str) -> bool:
        """
        Plugin'i registry'den kaldır
        
        Args:
            plugin_name: Kaldırılacak plugin adı
            
        Returns:
            bool: Kaldırma başarılı mı
        """
        try:
            if plugin_name not in self.plugins:
                logger.warning(f"Plugin bulunamadı: {plugin_name}")
                return False
            
            entry = self.plugins[plugin_name]
            
            # Plugin'i deaktive et
            if entry.plugin.status == PluginStatus.ACTIVE:
                await self.deactivate_plugin(plugin_name)
            
            # Registry'den kaldır
            del self.plugins[plugin_name]
            
            logger.info(f"Plugin kaydı kaldırıldı: {plugin_name}")
            await self._emit_event("plugin_unregistered", {"plugin_name": plugin_name})
            
            return True
            
        except Exception as e:
            logger.error(f"Plugin kayıt kaldırma hatası: {e}")
            return False
    
    async def activate_plugin(self, plugin_name: str, config: Dict[str, Any] = None) -> bool:
        """
        Plugin'i aktive et
        
        Args:
            plugin_name: Aktive edilecek plugin adı
            config: Plugin konfigürasyonu
            
        Returns:
            bool: Aktivasyon başarılı mı
        """
        try:
            if plugin_name not in self.plugins:
                logger.error(f"Plugin bulunamadı: {plugin_name}")
                return False
            
            entry = self.plugins[plugin_name]
            
            if entry.plugin.status == PluginStatus.ACTIVE:
                logger.warning(f"Plugin zaten aktif: {plugin_name}")
                return True
            
            # Plugin'i başlat
            success = await entry.plugin._safe_initialize(config)
            
            if success:
                logger.info(f"Plugin aktive edildi: {plugin_name}")
                await self._emit_event("plugin_activated", {"plugin_name": plugin_name})
            else:
                logger.error(f"Plugin aktivasyon başarısız: {plugin_name}")
                await self._emit_event("plugin_error", {
                    "plugin_name": plugin_name,
                    "error": entry.plugin.error_message
                })
            
            return success
            
        except Exception as e:
            logger.error(f"Plugin aktivasyon hatası: {e}")
            return False
    
    async def deactivate_plugin(self, plugin_name: str) -> bool:
        """
        Plugin'i deaktive et
        
        Args:
            plugin_name: Deaktive edilecek plugin adı
            
        Returns:
            bool: Deaktivasyon başarılı mı
        """
        try:
            if plugin_name not in self.plugins:
                logger.error(f"Plugin bulunamadı: {plugin_name}")
                return False
            
            entry = self.plugins[plugin_name]
            
            if entry.plugin.status != PluginStatus.ACTIVE:
                logger.warning(f"Plugin zaten aktif değil: {plugin_name}")
                return True
            
            # Plugin'i temizle
            await entry.plugin._safe_cleanup()
            
            logger.info(f"Plugin deaktive edildi: {plugin_name}")
            await self._emit_event("plugin_deactivated", {"plugin_name": plugin_name})
            
            return True
            
        except Exception as e:
            logger.error(f"Plugin deaktivasyon hatası: {e}")
            return False
    
    async def discover_plugins(self) -> List[str]:
        """
        Plugin'leri otomatik keşfet ve yükle
        
        Returns:
            List[str]: Keşfedilen plugin isimleri
        """
        discovered_plugins = []
        
        try:
            for discovery_path in self.discovery_paths:
                if not discovery_path.exists():
                    continue
                
                logger.info(f"Plugin'ler aranıyor: {discovery_path}")
                
                # Alt dizinleri tara
                for plugin_dir in discovery_path.iterdir():
                    if not plugin_dir.is_dir():
                        continue
                    
                    # Manifest dosyasını ara
                    manifest_files = list(plugin_dir.glob("manifest.*"))
                    if not manifest_files:
                        continue
                    
                    try:
                        # Plugin'i yükle
                        plugin = await self.plugin_loader.load_plugin_from_path(plugin_dir)
                        if plugin:
                            # Registry'ye kaydet
                            if await self.register_plugin(plugin, plugin_dir):
                                discovered_plugins.append(plugin.manifest.name)
                                logger.info(f"Plugin keşfedildi: {plugin.manifest.name}")
                    
                    except Exception as e:
                        logger.error(f"Plugin yükleme hatası ({plugin_dir}): {e}")
            
            logger.info(f"Toplam {len(discovered_plugins)} plugin keşfedildi")
            return discovered_plugins
            
        except Exception as e:
            logger.error(f"Plugin keşif hatası: {e}")
            return discovered_plugins
    
    def get_plugin(self, plugin_name: str) -> Optional[BasePlugin]:
        """Plugin'i al"""
        entry = self.plugins.get(plugin_name)
        return entry.plugin if entry else None
    
    def get_plugins_by_type(self, plugin_type: PluginType) -> List[BasePlugin]:
        """Tipe göre plugin'leri al"""
        return [
            entry.plugin for entry in self.plugins.values()
            if entry.manifest.type == plugin_type
        ]
    
    def get_active_plugins(self) -> List[BasePlugin]:
        """Aktif plugin'leri al"""
        return [
            entry.plugin for entry in self.plugins.values()
            if entry.plugin.status == PluginStatus.ACTIVE
        ]
    
    def list_plugins(self) -> Dict[str, Dict[str, Any]]:
        """Tüm plugin'leri listele"""
        return {
            name: {
                "manifest": entry.manifest.to_dict(),
                "status": entry.plugin.status.value,
                "registration_time": entry.registration_time,
                "last_health_check": entry.last_health_check,
                "health_status": entry.health_status,
                "load_path": str(entry.load_path) if entry.load_path else None
            }
            for name, entry in self.plugins.items()
        }
    
    async def _health_check_loop(self) -> None:
        """Periyodik sağlık kontrolü"""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._perform_health_checks()
            except Exception as e:
                logger.error(f"Health check döngü hatası: {e}")
    
    async def _perform_health_checks(self) -> None:
        """Tüm plugin'ler için sağlık kontrolü"""
        for plugin_name, entry in self.plugins.items():
            try:
                health_status = await entry.plugin.health_check()
                entry.last_health_check = time.time()
                entry.health_status = health_status
                
                if not health_status:
                    logger.warning(f"Plugin sağlık kontrolü başarısız: {plugin_name}")
                    await self._emit_event("plugin_error", {
                        "plugin_name": plugin_name,
                        "error": "Health check failed"
                    })
                    
            except Exception as e:
                logger.error(f"Plugin sağlık kontrolü hatası ({plugin_name}): {e}")
                entry.health_status = False
    
    async def _handle_plugin_event(self, event) -> None:
        """Plugin event'lerini işle"""
        logger.debug(f"Plugin event: {event.event_type} from {event.source_plugin}")
    
    def add_event_callback(self, event_type: str, callback: Callable) -> None:
        """Event callback ekle"""
        if event_type in self.event_callbacks:
            self.event_callbacks[event_type].append(callback)
    
    async def _emit_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """Event yayınla"""
        for callback in self.event_callbacks.get(event_type, []):
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(data)
                else:
                    callback(data)
            except Exception as e:
                logger.error(f"Event callback hatası: {e}")


# Global registry instance
_global_registry: Optional[PluginRegistry] = None


def get_global_registry() -> PluginRegistry:
    """Global plugin registry'yi al"""
    global _global_registry
    if _global_registry is None:
        _global_registry = PluginRegistry()
    return _global_registry
