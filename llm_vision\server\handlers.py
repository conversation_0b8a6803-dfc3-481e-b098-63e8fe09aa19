"""
MCP istek işleyicileri
"""

import asyncio
import os
from typing import Any, Dict, List, Optional
from datetime import datetime

from ..utils.logger import get_logger
from ..utils.exceptions import MCPError, VisionError, FileSystemError
from .models import Resource, ResourceContent, Tool, ToolResult

logger = get_logger(__name__)


class ResourceHandler:
    """Kaynak işleyici sınıfı"""
    
    def __init__(self):
        self.resources: Dict[str, Resource] = {}
        self._register_default_resources()
    
    def _register_default_resources(self) -> None:
        """Varsayılan kaynakları kaydet"""
        # Kamera kaynağı
        self.resources["camera://live"] = Resource(
            uri="camera://live",
            name="Live Camera Feed",
            description="Gerçek zamanlı kamera görüntüsü",
            mime_type="image/jpeg"
        )
        
        # Dosya sistemi kaynağı
        self.resources["file://last_modified"] = Resource(
            uri="file://last_modified",
            name="Last Modified File",
            description="En son değiştirilen dosya",
            mime_type="text/plain"
        )
        
        # Ekran görüntüsü kaynağı
        self.resources["screen://capture"] = Resource(
            uri="screen://capture",
            name="Screen Capture",
            description="Ekran görüntüsü",
            mime_type="image/png"
        )
    
    async def list_resources(self) -> List[Resource]:
        """Mevcut kaynakları listele"""
        logger.info("Kaynaklar listeleniyor")
        return list(self.resources.values())
    
    async def read_resource(self, uri: str) -> ResourceContent:
        """Kaynak içeriğini oku"""
        logger.info(f"Kaynak okunuyor: {uri}")
        
        if uri not in self.resources:
            raise MCPError(f"Kaynak bulunamadı: {uri}")
        
        resource = self.resources[uri]
        
        try:
            if uri == "camera://live":
                return await self._read_camera_feed()
            elif uri == "file://last_modified":
                return await self._read_last_modified_file()
            elif uri == "screen://capture":
                return await self._read_screen_capture()
            else:
                raise MCPError(f"Desteklenmeyen kaynak: {uri}")
                
        except Exception as e:
            logger.error(f"Kaynak okuma hatası: {e}")
            raise MCPError(f"Kaynak okuma hatası: {str(e)}")
    
    async def _read_camera_feed(self) -> ResourceContent:
        """Kamera görüntüsünü oku"""
        # Bu fonksiyon vision modülü ile entegre edilecek
        return ResourceContent(
            uri="camera://live",
            mime_type="image/jpeg",
            text="Kamera verisi - vision modülü ile entegre edilecek"
        )
    
    async def _read_last_modified_file(self) -> ResourceContent:
        """En son değiştirilen dosyayı oku"""
        try:
            # Mevcut dizindeki en son değiştirilen dosyayı bul
            current_dir = os.getcwd()
            files = []
            
            for root, dirs, filenames in os.walk(current_dir):
                for filename in filenames:
                    if not filename.startswith('.'):
                        filepath = os.path.join(root, filename)
                        try:
                            mtime = os.path.getmtime(filepath)
                            files.append((filepath, mtime))
                        except OSError:
                            continue
            
            if not files:
                return ResourceContent(
                    uri="file://last_modified",
                    mime_type="text/plain",
                    text="Dosya bulunamadı"
                )
            
            # En son değiştirilen dosyayı bul
            latest_file = max(files, key=lambda x: x[1])
            filepath, mtime = latest_file
            
            # Dosya içeriğini oku
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    content = f.read()
            except UnicodeDecodeError:
                content = f"Binary dosya: {os.path.basename(filepath)}"
            
            modified_time = datetime.fromtimestamp(mtime).isoformat()
            
            result_text = f"Dosya: {filepath}\n"
            result_text += f"Değiştirilme zamanı: {modified_time}\n"
            result_text += f"İçerik:\n{content[:1000]}..."  # İlk 1000 karakter
            
            return ResourceContent(
                uri="file://last_modified",
                mime_type="text/plain",
                text=result_text
            )
            
        except Exception as e:
            raise FileSystemError(f"Dosya okuma hatası: {str(e)}")
    
    async def _read_screen_capture(self) -> ResourceContent:
        """Ekran görüntüsünü al"""
        # Bu fonksiyon vision modülü ile entegre edilecek
        return ResourceContent(
            uri="screen://capture",
            mime_type="image/png",
            text="Ekran görüntüsü - vision modülü ile entegre edilecek"
        )


class ToolHandler:
    """Araç işleyici sınıfı"""
    
    def __init__(self):
        self.tools: Dict[str, Tool] = {}
        self._register_default_tools()
    
    def _register_default_tools(self) -> None:
        """Varsayılan araçları kaydet"""
        # Nesne tanıma aracı
        self.tools["detect_objects"] = Tool(
            name="detect_objects",
            description="Görüntüdeki nesneleri tanı",
            input_schema={
                "type": "object",
                "properties": {
                    "image_source": {
                        "type": "string",
                        "description": "Görüntü kaynağı (camera, screen, file)",
                        "enum": ["camera", "screen", "file"]
                    },
                    "confidence_threshold": {
                        "type": "number",
                        "description": "Güven eşiği (0.0-1.0)",
                        "minimum": 0.0,
                        "maximum": 1.0,
                        "default": 0.5
                    }
                },
                "required": ["image_source"]
            }
        )
        
        # Metin çıkarma aracı
        self.tools["extract_text"] = Tool(
            name="extract_text",
            description="Görüntüden metin çıkar (OCR)",
            input_schema={
                "type": "object",
                "properties": {
                    "image_source": {
                        "type": "string",
                        "description": "Görüntü kaynağı",
                        "enum": ["camera", "screen", "file"]
                    },
                    "language": {
                        "type": "string",
                        "description": "OCR dili",
                        "default": "eng"
                    }
                },
                "required": ["image_source"]
            }
        )
        
        # Dosya analizi aracı
        self.tools["analyze_file"] = Tool(
            name="analyze_file",
            description="Dosya içeriğini analiz et",
            input_schema={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "Dosya yolu"
                    },
                    "analysis_type": {
                        "type": "string",
                        "description": "Analiz tipi",
                        "enum": ["content", "metadata", "both"],
                        "default": "both"
                    }
                },
                "required": ["file_path"]
            }
        )
    
    async def list_tools(self) -> List[Tool]:
        """Mevcut araçları listele"""
        logger.info("Araçlar listeleniyor")
        return list(self.tools.values())
    
    async def call_tool(self, name: str, arguments: Dict[str, Any]) -> ToolResult:
        """Araç çağır"""
        logger.info(f"Araç çağrılıyor: {name} - {arguments}")
        
        if name not in self.tools:
            return ToolResult(
                content=[{"type": "text", "text": f"Araç bulunamadı: {name}"}],
                is_error=True
            )
        
        try:
            if name == "detect_objects":
                return await self._detect_objects(arguments)
            elif name == "extract_text":
                return await self._extract_text(arguments)
            elif name == "analyze_file":
                return await self._analyze_file(arguments)
            else:
                return ToolResult(
                    content=[{"type": "text", "text": f"Desteklenmeyen araç: {name}"}],
                    is_error=True
                )
                
        except Exception as e:
            logger.error(f"Araç çağırma hatası: {e}")
            return ToolResult(
                content=[{"type": "text", "text": f"Araç hatası: {str(e)}"}],
                is_error=True
            )
    
    async def _detect_objects(self, arguments: Dict[str, Any]) -> ToolResult:
        """Nesne tanıma"""
        # Bu fonksiyon vision modülü ile entegre edilecek
        image_source = arguments.get("image_source", "camera")
        confidence = arguments.get("confidence_threshold", 0.5)
        
        result_text = f"Nesne tanıma - Kaynak: {image_source}, Güven: {confidence}\n"
        result_text += "Vision modülü ile entegre edilecek"
        
        return ToolResult(
            content=[{"type": "text", "text": result_text}]
        )
    
    async def _extract_text(self, arguments: Dict[str, Any]) -> ToolResult:
        """Metin çıkarma"""
        # Bu fonksiyon vision modülü ile entegre edilecek
        image_source = arguments.get("image_source", "camera")
        language = arguments.get("language", "eng")
        
        result_text = f"OCR - Kaynak: {image_source}, Dil: {language}\n"
        result_text += "Vision modülü ile entegre edilecek"
        
        return ToolResult(
            content=[{"type": "text", "text": result_text}]
        )
    
    async def _analyze_file(self, arguments: Dict[str, Any]) -> ToolResult:
        """Dosya analizi"""
        file_path = arguments.get("file_path")
        analysis_type = arguments.get("analysis_type", "both")
        
        if not file_path:
            return ToolResult(
                content=[{"type": "text", "text": "Dosya yolu belirtilmedi"}],
                is_error=True
            )
        
        try:
            if not os.path.exists(file_path):
                return ToolResult(
                    content=[{"type": "text", "text": f"Dosya bulunamadı: {file_path}"}],
                    is_error=True
                )
            
            result_text = f"Dosya analizi: {file_path}\n"
            
            # Metadata analizi
            if analysis_type in ["metadata", "both"]:
                stat = os.stat(file_path)
                result_text += f"Boyut: {stat.st_size} bytes\n"
                result_text += f"Değiştirilme: {datetime.fromtimestamp(stat.st_mtime)}\n"
            
            # İçerik analizi
            if analysis_type in ["content", "both"]:
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    result_text += f"İçerik (ilk 500 karakter):\n{content[:500]}..."
                except UnicodeDecodeError:
                    result_text += "Binary dosya - içerik okunamadı"
            
            return ToolResult(
                content=[{"type": "text", "text": result_text}]
            )
            
        except Exception as e:
            return ToolResult(
                content=[{"type": "text", "text": f"Dosya analiz hatası: {str(e)}"}],
                is_error=True
            )
