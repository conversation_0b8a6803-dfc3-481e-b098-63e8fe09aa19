"""
Loglama sistemi
"""

import os
import sys
from typing import Optional
from loguru import logger
from ..config import config


def setup_logger(
    log_file: Optional[str] = None,
    log_level: str = "INFO",
    rotation: str = "10 MB",
    retention: str = "7 days"
) -> None:
    """
    Logger'ı yapılandır
    
    Args:
        log_file: Log dosyası yolu
        log_level: Log seviyesi
        rotation: Log dosyası rotasyon boyutu
        retention: Log dosyası saklama süresi
    """
    # Mevcut handler'ları temizle
    logger.remove()
    
    # Console handler ekle
    logger.add(
        sys.stderr,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True
    )
    
    # <PERSON>sya handler ekle
    if log_file:
        # Log dizinini oluştur
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
            
        logger.add(
            log_file,
            level=log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | "
                   "{level: <8} | "
                   "{name}:{function}:{line} | "
                   "{message}",
            rotation=rotation,
            retention=retention,
            compression="zip"
        )


def get_logger(name: str = "llm_vision"):
    """
    Logger instance'ı al
    
    Args:
        name: Logger adı
        
    Returns:
        Logger instance
    """
    return logger.bind(name=name)


# Logger'ı başlat
setup_logger(
    log_file=config.logging.file,
    log_level=config.logging.level
)
