"""
Ana sistem koordinatörü
"""

import asyncio
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
import signal
import sys

from ..config import config
from ..utils.logger import get_logger
from ..utils.exceptions import LLMVisionError
from ..server import MCPServer
from ..vision import VisionProcessor
from ..context import ContextEngine
from ..filesystem import DataCollector
from ..transport import MCPTransport
from .integration_manager import IntegrationManager
from .event_dispatcher import EventDispatcher
from .health_monitor import HealthMonitor

logger = get_logger(__name__)


class SystemCoordinator:
    """Ana sistem koordinatörü sınıfı"""
    
    def __init__(self):
        # Ana bileşenler
        self.mcp_server = MCPServer()
        self.vision_processor = VisionProcessor()
        self.context_engine = ContextEngine()
        self.data_collector = DataCollector()
        self.mcp_transport = MCPTransport()
        
        # Koordinasyon bileşenleri
        self.integration_manager = IntegrationManager()
        self.event_dispatcher = EventDispatcher()
        self.health_monitor = HealthMonitor()
        
        # Durum
        self.is_running = False
        self.is_initialized = False
        self.startup_time = None
        self.shutdown_handlers: List[Callable] = []
        
        # Performans metrikleri
        self.performance_metrics = {
            "requests_processed": 0,
            "errors_occurred": 0,
            "average_response_time": 0.0,
            "uptime_seconds": 0
        }
        
        logger.info("Sistem koordinatörü oluşturuldu")
    
    async def initialize(self) -> None:
        """Sistemi başlat"""
        try:
            if self.is_initialized:
                logger.warning("Sistem zaten başlatılmış")
                return
            
            logger.info("LLM Vision System başlatılıyor...")
            self.startup_time = datetime.now()
            
            # 1. Event dispatcher'ı başlat
            await self.event_dispatcher.initialize()
            
            # 2. Health monitor'u başlat
            await self.health_monitor.initialize()
            
            # 3. Ana bileşenleri başlat
            await self._initialize_core_components()
            
            # 4. Entegrasyon manager'ı başlat
            await self.integration_manager.initialize(
                vision_processor=self.vision_processor,
                context_engine=self.context_engine,
                data_collector=self.data_collector,
                mcp_transport=self.mcp_transport,
                event_dispatcher=self.event_dispatcher
            )
            
            # 5. MCP transport'u başlat
            await self.mcp_transport.initialize(
                vision_processor=self.vision_processor,
                context_engine=self.context_engine,
                data_collector=self.data_collector
            )
            
            # 6. Event handler'ları kaydet
            self._register_event_handlers()
            
            # 7. Health check'leri başlat
            await self._start_health_checks()
            
            # 8. Signal handler'ları kaydet
            self._register_signal_handlers()
            
            self.is_initialized = True
            logger.info("LLM Vision System başarıyla başlatıldı")
            
            # Başlatma olayını gönder
            await self.event_dispatcher.dispatch_event(
                "system.initialized",
                {"timestamp": datetime.now().isoformat()}
            )
            
        except Exception as e:
            logger.error(f"Sistem başlatma hatası: {e}")
            await self.shutdown()
            raise LLMVisionError(f"Sistem başlatma hatası: {str(e)}")
    
    async def _initialize_core_components(self) -> None:
        """Ana bileşenleri başlat"""
        try:
            # Vision processor'ı başlat
            logger.info("Vision processor başlatılıyor...")
            await self.vision_processor.initialize(
                enable_camera=config.vision.enable_camera,
                enable_yolo=config.vision.enable_yolo
            )
            
            # Context engine'i başlat
            logger.info("Context engine başlatılıyor...")
            await self.context_engine.initialize()
            
            # Data collector'ı başlat
            logger.info("Data collector başlatılıyor...")
            await self.data_collector.initialize()
            
            # Data collection'ı başlat
            await self.data_collector.start_collection()
            
            logger.info("Ana bileşenler başlatıldı")
            
        except Exception as e:
            logger.error(f"Ana bileşen başlatma hatası: {e}")
            raise
    
    def _register_event_handlers(self) -> None:
        """Event handler'ları kaydet"""
        try:
            # Sistem olayları
            self.event_dispatcher.register_handler(
                "system.error", self._handle_system_error
            )
            self.event_dispatcher.register_handler(
                "system.warning", self._handle_system_warning
            )
            
            # Vision olayları
            self.event_dispatcher.register_handler(
                "vision.analysis_complete", self._handle_vision_analysis
            )
            
            # Context olayları
            self.event_dispatcher.register_handler(
                "context.created", self._handle_context_created
            )
            
            # Data collection olayları
            self.event_dispatcher.register_handler(
                "data.collected", self._handle_data_collected
            )
            
            # MCP olayları
            self.event_dispatcher.register_handler(
                "mcp.client_connected", self._handle_client_connected
            )
            self.event_dispatcher.register_handler(
                "mcp.client_disconnected", self._handle_client_disconnected
            )
            
            logger.debug("Event handler'lar kaydedildi")
            
        except Exception as e:
            logger.error(f"Event handler kaydetme hatası: {e}")
    
    async def _start_health_checks(self) -> None:
        """Health check'leri başlat"""
        try:
            # Bileşenleri health monitor'a kaydet
            self.health_monitor.register_component(
                "vision_processor", self.vision_processor
            )
            self.health_monitor.register_component(
                "context_engine", self.context_engine
            )
            self.health_monitor.register_component(
                "data_collector", self.data_collector
            )
            self.health_monitor.register_component(
                "mcp_transport", self.mcp_transport
            )
            
            # Health check'leri başlat
            await self.health_monitor.start_monitoring()
            
            logger.info("Health monitoring başlatıldı")
            
        except Exception as e:
            logger.error(f"Health monitoring başlatma hatası: {e}")
    
    def _register_signal_handlers(self) -> None:
        """Signal handler'ları kaydet"""
        try:
            def signal_handler(signum, frame):
                logger.info(f"Signal alındı: {signum}")
                asyncio.create_task(self.shutdown())
            
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
            
            logger.debug("Signal handler'lar kaydedildi")
            
        except Exception as e:
            logger.error(f"Signal handler kaydetme hatası: {e}")
    
    async def run(self) -> None:
        """Sistemi çalıştır"""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            self.is_running = True
            logger.info("LLM Vision System çalışıyor...")
            
            # MCP sunucusunu başlat
            await self.mcp_server.start()
            
        except KeyboardInterrupt:
            logger.info("Kullanıcı tarafından durduruldu")
        except Exception as e:
            logger.error(f"Sistem çalıştırma hatası: {e}")
        finally:
            await self.shutdown()
    
    async def shutdown(self) -> None:
        """Sistemi kapat"""
        try:
            if not self.is_running and not self.is_initialized:
                return
            
            logger.info("LLM Vision System kapatılıyor...")
            self.is_running = False
            
            # Shutdown handler'ları çağır
            for handler in self.shutdown_handlers:
                try:
                    await handler()
                except Exception as e:
                    logger.error(f"Shutdown handler hatası: {e}")
            
            # Bileşenleri kapat
            await self._shutdown_components()
            
            self.is_initialized = False
            logger.info("LLM Vision System kapatıldı")
            
        except Exception as e:
            logger.error(f"Sistem kapatma hatası: {e}")
    
    async def _shutdown_components(self) -> None:
        """Bileşenleri kapat"""
        try:
            # Ters sırada kapat
            components = [
                ("Health Monitor", self.health_monitor.shutdown),
                ("Integration Manager", self.integration_manager.shutdown),
                ("MCP Transport", self.mcp_transport.client_manager.cleanup_inactive_clients),
                ("Data Collector", self.data_collector.shutdown),
                ("Context Engine", self.context_engine.shutdown),
                ("Vision Processor", self.vision_processor.shutdown),
                ("Event Dispatcher", self.event_dispatcher.shutdown)
            ]
            
            for name, shutdown_func in components:
                try:
                    logger.debug(f"{name} kapatılıyor...")
                    await shutdown_func()
                except Exception as e:
                    logger.error(f"{name} kapatma hatası: {e}")
            
        except Exception as e:
            logger.error(f"Bileşen kapatma hatası: {e}")
    
    # Event handler metodları
    async def _handle_system_error(self, event_data: Dict[str, Any]) -> None:
        """Sistem hatası olayını işle"""
        try:
            logger.error(f"Sistem hatası: {event_data}")
            self.performance_metrics["errors_occurred"] += 1
            
        except Exception as e:
            logger.error(f"Sistem hatası işleme hatası: {e}")
    
    async def _handle_system_warning(self, event_data: Dict[str, Any]) -> None:
        """Sistem uyarısı olayını işle"""
        try:
            logger.warning(f"Sistem uyarısı: {event_data}")
            
        except Exception as e:
            logger.error(f"Sistem uyarısı işleme hatası: {e}")
    
    async def _handle_vision_analysis(self, event_data: Dict[str, Any]) -> None:
        """Vision analizi olayını işle"""
        try:
            logger.debug("Vision analizi tamamlandı")
            
            # MCP istemcilerine bildirim gönder
            await self.mcp_transport.notification_manager.broadcast_resource_changed(
                "camera://live", "updated"
            )
            
        except Exception as e:
            logger.error(f"Vision analizi işleme hatası: {e}")
    
    async def _handle_context_created(self, event_data: Dict[str, Any]) -> None:
        """Context oluşturma olayını işle"""
        try:
            logger.debug("Context oluşturuldu")
            
            # MCP istemcilerine bildirim gönder
            await self.mcp_transport.notification_manager.broadcast_resource_changed(
                "context://recent", "updated"
            )
            
        except Exception as e:
            logger.error(f"Context oluşturma işleme hatası: {e}")
    
    async def _handle_data_collected(self, event_data: Dict[str, Any]) -> None:
        """Veri toplama olayını işle"""
        try:
            logger.debug("Veri toplandı")
            self.performance_metrics["requests_processed"] += 1
            
        except Exception as e:
            logger.error(f"Veri toplama işleme hatası: {e}")
    
    async def _handle_client_connected(self, event_data: Dict[str, Any]) -> None:
        """İstemci bağlantı olayını işle"""
        try:
            client_id = event_data.get("client_id")
            logger.info(f"İstemci bağlandı: {client_id}")
            
        except Exception as e:
            logger.error(f"İstemci bağlantı işleme hatası: {e}")
    
    async def _handle_client_disconnected(self, event_data: Dict[str, Any]) -> None:
        """İstemci bağlantı kesme olayını işle"""
        try:
            client_id = event_data.get("client_id")
            logger.info(f"İstemci bağlantısı kesildi: {client_id}")
            
        except Exception as e:
            logger.error(f"İstemci bağlantı kesme işleme hatası: {e}")
    
    def add_shutdown_handler(self, handler: Callable) -> None:
        """Shutdown handler ekle"""
        self.shutdown_handlers.append(handler)
    
    def get_system_status(self) -> Dict[str, Any]:
        """Sistem durumunu al"""
        try:
            uptime = (datetime.now() - self.startup_time).total_seconds() if self.startup_time else 0
            self.performance_metrics["uptime_seconds"] = uptime
            
            return {
                "is_running": self.is_running,
                "is_initialized": self.is_initialized,
                "startup_time": self.startup_time.isoformat() if self.startup_time else None,
                "uptime_seconds": uptime,
                "performance_metrics": self.performance_metrics,
                "components": {
                    "vision_processor": self.vision_processor.is_initialized if hasattr(self.vision_processor, 'is_initialized') else False,
                    "context_engine": self.context_engine.is_initialized if hasattr(self.context_engine, 'is_initialized') else False,
                    "data_collector": self.data_collector.is_collecting if hasattr(self.data_collector, 'is_collecting') else False,
                    "mcp_transport": self.mcp_transport.is_initialized if hasattr(self.mcp_transport, 'is_initialized') else False
                },
                "health": self.health_monitor.get_overall_health() if hasattr(self.health_monitor, 'get_overall_health') else "unknown"
            }
            
        except Exception as e:
            logger.error(f"Sistem durumu alma hatası: {e}")
            return {"error": str(e)}
