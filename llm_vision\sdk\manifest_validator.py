"""
Manifest Validator System

Plugin manifest dosyalarının doğrulanması için sistem.
"""

from typing import List, Dict, Any, Optional
import re
import semver
from pathlib import Path

from .plugin_manifest import PluginManifest, PluginType
from ..utils.logger import get_logger

logger = get_logger(__name__)


class ManifestValidator:
    """Plugin manifest doğrulayıcısı"""
    
    def __init__(self):
        self.is_initialized = False
        
        # Validation rules
        self.name_pattern = re.compile(r'^[a-zA-Z0-9_-]+$')
        self.version_pattern = re.compile(r'^\d+\.\d+\.\d+$')
        self.permission_patterns = {
            'camera_access': r'^camera_access$',
            'file_read': r'^file_read$',
            'file_write': r'^file_write$',
            'network_access': r'^network_access$',
            'system_info': r'^system_info$'
        }
        
        # Güvenlik kuralları
        self.forbidden_permissions = {
            'system_admin',
            'root_access',
            'kernel_access'
        }
        
        self.max_dependencies = 50
        self.max_permissions = 20
        self.max_endpoints = 100
        
        logger.info("Manifest Validator oluşturuldu")
    
    async def initialize(self) -> None:
        """Validator'ı başlat"""
        try:
            if self.is_initialized:
                return
            
            logger.info("Manifest Validator başlatılıyor...")
            
            self.is_initialized = True
            logger.info("Manifest Validator başarıyla başlatıldı")
            
        except Exception as e:
            logger.error(f"Manifest Validator başlatma hatası: {e}")
            raise
    
    async def validate(self, manifest: PluginManifest) -> List[str]:
        """
        Manifest'i doğrula
        
        Args:
            manifest: Doğrulanacak manifest
            
        Returns:
            List[str]: Doğrulama hataları
        """
        errors = []
        
        try:
            # Temel doğrulamalar
            errors.extend(self._validate_basic_fields(manifest))
            
            # İsim doğrulaması
            errors.extend(self._validate_name(manifest.name))
            
            # Versiyon doğrulaması
            errors.extend(self._validate_version(manifest.version))
            
            # Tip doğrulaması
            errors.extend(self._validate_type(manifest.type))
            
            # Bağımlılık doğrulaması
            errors.extend(self._validate_dependencies(manifest.dependencies))
            
            # İzin doğrulaması
            errors.extend(self._validate_permissions(manifest.permissions))
            
            # Endpoint doğrulaması
            errors.extend(self._validate_endpoints(manifest.endpoints))
            
            # Config schema doğrulaması
            errors.extend(self._validate_config_schema(manifest.config_schema))
            
            # Platform doğrulaması
            errors.extend(self._validate_platforms(manifest.supported_platforms))
            
            # Güvenlik doğrulaması
            errors.extend(self._validate_security(manifest))
            
            if errors:
                logger.warning(f"Manifest doğrulama hataları ({manifest.name}): {len(errors)} hata")
            else:
                logger.info(f"Manifest doğrulama başarılı: {manifest.name}")
            
            return errors
            
        except Exception as e:
            logger.error(f"Manifest doğrulama hatası: {e}")
            return [f"Doğrulama hatası: {str(e)}"]
    
    def _validate_basic_fields(self, manifest: PluginManifest) -> List[str]:
        """Temel alanları doğrula"""
        errors = []
        
        if not manifest.name:
            errors.append("Plugin adı boş olamaz")
        
        if not manifest.version:
            errors.append("Plugin versiyonu boş olamaz")
        
        if not manifest.description:
            errors.append("Plugin açıklaması boş olamaz")
        
        if not manifest.author:
            errors.append("Plugin yazarı boş olamaz")
        
        return errors
    
    def _validate_name(self, name: str) -> List[str]:
        """Plugin adını doğrula"""
        errors = []
        
        if not name:
            return errors
        
        # Uzunluk kontrolü
        if len(name) < 3:
            errors.append("Plugin adı en az 3 karakter olmalıdır")
        elif len(name) > 50:
            errors.append("Plugin adı en fazla 50 karakter olmalıdır")
        
        # Format kontrolü
        if not self.name_pattern.match(name):
            errors.append("Plugin adı sadece harf, rakam, tire ve alt çizgi içerebilir")
        
        # Rezerve kelimeler
        reserved_names = {'system', 'core', 'admin', 'root', 'plugin'}
        if name.lower() in reserved_names:
            errors.append(f"'{name}' rezerve edilmiş bir isimdir")
        
        return errors
    
    def _validate_version(self, version: str) -> List[str]:
        """Versiyon formatını doğrula"""
        errors = []
        
        if not version:
            return errors
        
        # Semantic versioning kontrolü
        try:
            semver.VersionInfo.parse(version)
        except ValueError:
            errors.append("Versiyon semantic versioning formatında olmalıdır (X.Y.Z)")
        
        return errors
    
    def _validate_type(self, plugin_type: PluginType) -> List[str]:
        """Plugin tipini doğrula"""
        errors = []
        
        if plugin_type not in PluginType:
            errors.append(f"Geçersiz plugin tipi: {plugin_type}")
        
        return errors
    
    def _validate_dependencies(self, dependencies: List) -> List[str]:
        """Bağımlılıkları doğrula"""
        errors = []
        
        if len(dependencies) > self.max_dependencies:
            errors.append(f"Çok fazla bağımlılık (max {self.max_dependencies})")
        
        for dep in dependencies:
            if not dep.name:
                errors.append("Bağımlılık adı boş olamaz")
            
            if not dep.version:
                errors.append(f"Bağımlılık versiyonu boş olamaz: {dep.name}")
            
            # Güvenlik kontrolü
            dangerous_packages = {
                'os', 'subprocess', 'sys', 'eval', 'exec'
            }
            if dep.name in dangerous_packages:
                errors.append(f"Güvenlik riski: {dep.name} bağımlılığı izin verilmiyor")
        
        return errors
    
    def _validate_permissions(self, permissions: List) -> List[str]:
        """İzinleri doğrula"""
        errors = []
        
        if len(permissions) > self.max_permissions:
            errors.append(f"Çok fazla izin (max {self.max_permissions})")
        
        for perm in permissions:
            if not perm.name:
                errors.append("İzin adı boş olamaz")
            
            # Yasaklı izinler
            if perm.name in self.forbidden_permissions:
                errors.append(f"Yasaklı izin: {perm.name}")
            
            # İzin formatı kontrolü
            if not re.match(r'^[a-z_]+$', perm.name):
                errors.append(f"Geçersiz izin formatı: {perm.name}")
        
        return errors
    
    def _validate_endpoints(self, endpoints: List) -> List[str]:
        """Endpoint'leri doğrula"""
        errors = []
        
        if len(endpoints) > self.max_endpoints:
            errors.append(f"Çok fazla endpoint (max {self.max_endpoints})")
        
        paths = set()
        for endpoint in endpoints:
            if not endpoint.path:
                errors.append("Endpoint path'i boş olamaz")
                continue
            
            # Path formatı kontrolü
            if not endpoint.path.startswith('/'):
                errors.append(f"Endpoint path '/' ile başlamalıdır: {endpoint.path}")
            
            # Duplicate path kontrolü
            if endpoint.path in paths:
                errors.append(f"Duplicate endpoint path: {endpoint.path}")
            paths.add(endpoint.path)
            
            # HTTP method kontrolü
            valid_methods = {'GET', 'POST', 'PUT', 'DELETE', 'PATCH'}
            if endpoint.method.upper() not in valid_methods:
                errors.append(f"Geçersiz HTTP method: {endpoint.method}")
        
        return errors
    
    def _validate_config_schema(self, config_schema: Dict[str, Any]) -> List[str]:
        """Config schema'yı doğrula"""
        errors = []
        
        if not config_schema:
            return errors
        
        # JSON Schema formatı kontrolü
        if 'type' not in config_schema:
            errors.append("Config schema 'type' alanı içermelidir")
        
        # Güvenlik kontrolü
        if self._has_dangerous_schema_properties(config_schema):
            errors.append("Config schema güvenlik riski içeriyor")
        
        return errors
    
    def _validate_platforms(self, platforms: List[str]) -> List[str]:
        """Desteklenen platformları doğrula"""
        errors = []
        
        valid_platforms = {'windows', 'linux', 'macos', 'android', 'ios'}
        for platform in platforms:
            if platform not in valid_platforms:
                errors.append(f"Geçersiz platform: {platform}")
        
        return errors
    
    def _validate_security(self, manifest: PluginManifest) -> List[str]:
        """Güvenlik kontrolü"""
        errors = []
        
        # Yüksek riskli izin kombinasyonları
        permission_names = {perm.name for perm in manifest.permissions}
        
        if 'file_write' in permission_names and 'network_access' in permission_names:
            errors.append("Güvenlik uyarısı: file_write + network_access kombinasyonu riskli")
        
        if 'system_info' in permission_names and len(permission_names) > 5:
            errors.append("Güvenlik uyarısı: system_info ile çok fazla izin")
        
        # Endpoint güvenlik kontrolü
        for endpoint in manifest.endpoints:
            if endpoint.path.startswith('/admin') or endpoint.path.startswith('/system'):
                errors.append(f"Güvenlik uyarısı: Hassas endpoint path: {endpoint.path}")
        
        return errors
    
    def _has_dangerous_schema_properties(self, schema: Dict[str, Any]) -> bool:
        """Schema'da tehlikeli özellikler var mı kontrol et"""
        dangerous_keys = {
            'eval', 'exec', 'import', '__import__', 'subprocess'
        }
        
        def check_recursive(obj):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if key in dangerous_keys:
                        return True
                    if check_recursive(value):
                        return True
            elif isinstance(obj, list):
                for item in obj:
                    if check_recursive(item):
                        return True
            elif isinstance(obj, str):
                for dangerous in dangerous_keys:
                    if dangerous in obj.lower():
                        return True
            return False
        
        return check_recursive(schema)
