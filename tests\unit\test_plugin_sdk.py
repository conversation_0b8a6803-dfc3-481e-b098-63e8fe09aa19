"""
Plugin SDK Unit Tests

Plugin SDK bileşenlerinin unit testleri.
"""

import pytest
import asyncio
import tempfile
import json
from pathlib import Path
from unittest.mock import Mock, patch

from llm_vision.sdk import (
    PluginRegistry, PluginLoader, ManifestValidator, SecurityManager,
    PluginManifest, PluginType, PluginStatus, PluginData, PluginResponse
)
from llm_vision.sdk.examples import ExampleSensorPlugin, ExampleActionPlugin, ExampleContextPlugin


class TestPluginManifest:
    """PluginManifest testleri"""
    
    def test_manifest_creation(self):
        """Manifest oluşturma testi"""
        manifest = PluginManifest(
            name="test_plugin",
            version="1.0.0",
            type=PluginType.SENSOR,
            description="Test plugin",
            author="Test Author"
        )
        
        assert manifest.name == "test_plugin"
        assert manifest.version == "1.0.0"
        assert manifest.type == PluginType.SENSOR
        assert manifest.description == "Test plugin"
        assert manifest.author == "Test Author"
    
    def test_manifest_validation(self):
        """Manifest doğrulama testi"""
        manifest = PluginManifest(
            name="test_plugin",
            version="1.0.0",
            type=PluginType.SENSOR,
            description="Test plugin",
            author="Test Author"
        )
        
        errors = manifest.validate()
        assert len(errors) == 0
    
    def test_manifest_validation_errors(self):
        """Manifest doğrulama hataları testi"""
        manifest = PluginManifest(
            name="",  # Boş isim
            version="invalid",  # Geçersiz versiyon
            type=PluginType.SENSOR,
            description="",  # Boş açıklama
            author=""  # Boş yazar
        )
        
        errors = manifest.validate()
        assert len(errors) > 0
    
    def test_manifest_to_dict(self):
        """Manifest dictionary dönüşümü testi"""
        manifest = PluginManifest(
            name="test_plugin",
            version="1.0.0",
            type=PluginType.SENSOR,
            description="Test plugin",
            author="Test Author"
        )
        
        manifest_dict = manifest.to_dict()
        assert manifest_dict["plugin"]["name"] == "test_plugin"
        assert manifest_dict["plugin"]["version"] == "1.0.0"
        assert manifest_dict["plugin"]["type"] == "sensor"


class TestPluginData:
    """PluginData testleri"""
    
    def test_plugin_data_creation(self):
        """PluginData oluşturma testi"""
        data = PluginData(
            source="test_plugin",
            data={"key": "value"},
            confidence=0.9,
            tags=["test"]
        )
        
        assert data.source == "test_plugin"
        assert data.data == {"key": "value"}
        assert data.confidence == 0.9
        assert data.tags == ["test"]
    
    def test_plugin_data_validation(self):
        """PluginData doğrulama testi"""
        # Geçersiz confidence değeri
        with pytest.raises(ValueError):
            PluginData(
                source="test",
                data={},
                confidence=1.5  # 1.0'dan büyük
            )
        
        # Boş source
        with pytest.raises(ValueError):
            PluginData(
                source="",
                data={}
            )
    
    def test_plugin_data_methods(self):
        """PluginData metodları testi"""
        data = PluginData(
            source="test",
            data={"key": "value"},
            tags=["tag1"]
        )
        
        # Tag ekleme
        data.add_tag("tag2")
        assert "tag2" in data.tags
        
        # Tag kontrolü
        assert data.has_tag("tag1")
        assert not data.has_tag("tag3")
        
        # Metadata
        data.set_metadata("key", "value")
        assert data.get_metadata("key") == "value"


class TestPluginResponse:
    """PluginResponse testleri"""
    
    def test_success_response(self):
        """Başarılı yanıt testi"""
        data = PluginData(source="test", data={"result": "success"})
        response = PluginResponse.success_response(data, 100.0)
        
        assert response.success is True
        assert response.data == data
        assert response.execution_time_ms == 100.0
        assert response.error_message is None
    
    def test_error_response(self):
        """Hata yanıtı testi"""
        response = PluginResponse.error_response(
            "Test error", "TEST_ERROR", 50.0
        )
        
        assert response.success is False
        assert response.error_message == "Test error"
        assert response.error_code == "TEST_ERROR"
        assert response.execution_time_ms == 50.0
        assert response.data is None


@pytest.mark.asyncio
class TestManifestValidator:
    """ManifestValidator testleri"""
    
    async def test_validator_initialization(self):
        """Validator başlatma testi"""
        validator = ManifestValidator()
        await validator.initialize()
        assert validator.is_initialized is True
    
    async def test_valid_manifest_validation(self):
        """Geçerli manifest doğrulama testi"""
        validator = ManifestValidator()
        await validator.initialize()
        
        manifest = PluginManifest(
            name="valid_plugin",
            version="1.0.0",
            type=PluginType.SENSOR,
            description="Valid test plugin",
            author="Test Author"
        )
        
        errors = await validator.validate(manifest)
        assert len(errors) == 0
    
    async def test_invalid_manifest_validation(self):
        """Geçersiz manifest doğrulama testi"""
        validator = ManifestValidator()
        await validator.initialize()
        
        manifest = PluginManifest(
            name="invalid-name-with-special-chars!",
            version="invalid-version",
            type=PluginType.SENSOR,
            description="",
            author=""
        )
        
        errors = await validator.validate(manifest)
        assert len(errors) > 0


@pytest.mark.asyncio
class TestSecurityManager:
    """SecurityManager testleri"""
    
    async def test_security_manager_initialization(self):
        """Security Manager başlatma testi"""
        security_manager = SecurityManager()
        await security_manager.initialize()
        assert security_manager.is_initialized is True
    
    async def test_plugin_validation(self):
        """Plugin güvenlik doğrulama testi"""
        security_manager = SecurityManager()
        await security_manager.initialize()
        
        # Güvenli plugin
        safe_plugin = ExampleSensorPlugin()
        result = await security_manager.validate_plugin(safe_plugin)
        assert result is True
    
    async def test_permission_check(self):
        """İzin kontrolü testi"""
        security_manager = SecurityManager()
        await security_manager.initialize()
        
        plugin = ExampleSensorPlugin()
        await security_manager.validate_plugin(plugin)
        
        # Mevcut izin
        has_permission = await security_manager.check_permission(
            plugin.manifest.name, "system_info"
        )
        assert has_permission is True
        
        # Olmayan izin
        has_permission = await security_manager.check_permission(
            plugin.manifest.name, "nonexistent_permission"
        )
        assert has_permission is False


@pytest.mark.asyncio
class TestPluginLoader:
    """PluginLoader testleri"""
    
    async def test_plugin_loader_initialization(self):
        """Plugin Loader başlatma testi"""
        loader = PluginLoader()
        await loader.initialize()
        assert loader.is_initialized is True
    
    async def test_load_plugin_from_path(self):
        """Plugin yükleme testi"""
        loader = PluginLoader()
        await loader.initialize()
        
        # Geçici plugin dizini oluştur
        with tempfile.TemporaryDirectory() as temp_dir:
            plugin_dir = Path(temp_dir) / "test_plugin"
            plugin_dir.mkdir()
            
            # Manifest dosyası oluştur
            manifest_data = {
                "plugin": {
                    "name": "test_plugin",
                    "version": "1.0.0",
                    "type": "sensor",
                    "description": "Test plugin",
                    "author": "Test Author"
                }
            }
            
            manifest_file = plugin_dir / "manifest.json"
            with open(manifest_file, 'w') as f:
                json.dump(manifest_data, f)
            
            # Plugin dosyası oluştur
            plugin_code = '''
from llm_vision.sdk import BaseSensorPlugin, PluginResponse, PluginData

class TestPlugin(BaseSensorPlugin):
    async def initialize(self, config=None):
        return True
    
    async def cleanup(self):
        pass
    
    async def collect_data(self):
        data = PluginData(source="test", data={"test": True})
        return PluginResponse.success_response(data)
    
    def get_schema(self):
        return {"type": "object"}
'''
            
            plugin_file = plugin_dir / "plugin.py"
            with open(plugin_file, 'w') as f:
                f.write(plugin_code)
            
            # Plugin'i yükle
            plugin = await loader.load_plugin_from_path(plugin_dir)
            assert plugin is not None
            assert plugin.manifest.name == "test_plugin"


@pytest.mark.asyncio
class TestPluginRegistry:
    """PluginRegistry testleri"""
    
    async def test_registry_initialization(self):
        """Registry başlatma testi"""
        registry = PluginRegistry()
        await registry.initialize()
        assert registry.is_initialized is True
    
    async def test_plugin_registration(self):
        """Plugin kayıt testi"""
        registry = PluginRegistry()
        await registry.initialize()
        
        plugin = ExampleSensorPlugin()
        result = await registry.register_plugin(plugin)
        assert result is True
        assert plugin.manifest.name in registry.plugins
    
    async def test_plugin_activation(self):
        """Plugin aktivasyon testi"""
        registry = PluginRegistry()
        await registry.initialize()
        
        plugin = ExampleSensorPlugin()
        await registry.register_plugin(plugin)
        
        result = await registry.activate_plugin(plugin.manifest.name)
        assert result is True
        assert plugin.status == PluginStatus.ACTIVE
    
    async def test_plugin_deactivation(self):
        """Plugin deaktivasyon testi"""
        registry = PluginRegistry()
        await registry.initialize()
        
        plugin = ExampleSensorPlugin()
        await registry.register_plugin(plugin)
        await registry.activate_plugin(plugin.manifest.name)
        
        result = await registry.deactivate_plugin(plugin.manifest.name)
        assert result is True
        assert plugin.status == PluginStatus.INACTIVE
    
    async def test_get_plugins_by_type(self):
        """Tipe göre plugin alma testi"""
        registry = PluginRegistry()
        await registry.initialize()
        
        sensor_plugin = ExampleSensorPlugin()
        action_plugin = ExampleActionPlugin()
        
        await registry.register_plugin(sensor_plugin)
        await registry.register_plugin(action_plugin)
        
        sensor_plugins = registry.get_plugins_by_type(PluginType.SENSOR)
        action_plugins = registry.get_plugins_by_type(PluginType.ACTION)
        
        assert len(sensor_plugins) == 1
        assert len(action_plugins) == 1
        assert sensor_plugins[0].manifest.name == sensor_plugin.manifest.name


@pytest.mark.asyncio
class TestExamplePlugins:
    """Örnek plugin'ler testleri"""
    
    async def test_sensor_plugin(self):
        """Sensor plugin testi"""
        plugin = ExampleSensorPlugin()
        
        # Başlatma
        result = await plugin._safe_initialize()
        assert result is True
        assert plugin.status == PluginStatus.ACTIVE
        
        # Veri toplama
        response = await plugin.collect_data()
        assert response.success is True
        assert response.data is not None
        
        # Temizleme
        await plugin._safe_cleanup()
        assert plugin.status == PluginStatus.INACTIVE
    
    async def test_action_plugin(self):
        """Action plugin testi"""
        plugin = ExampleActionPlugin()
        
        # Başlatma
        result = await plugin._safe_initialize()
        assert result is True
        
        # Test aksiyonu
        response = await plugin.execute_action("test", {})
        assert response.success is True
        
        # Mevcut aksiyonları al
        actions = plugin.get_available_actions()
        assert len(actions) > 0
        
        # Temizleme
        await plugin._safe_cleanup()
    
    async def test_context_plugin(self):
        """Context plugin testi"""
        plugin = ExampleContextPlugin()
        
        # Başlatma
        result = await plugin._safe_initialize()
        assert result is True
        
        # Bağlam analizi
        test_data = {
            "cpu_percent": 50.0,
            "memory_percent": 60.0,
            "timestamp": 1234567890
        }
        
        response = await plugin.analyze_context(test_data)
        assert response.success is True
        assert response.data is not None
        
        # Schema al
        schema = plugin.get_context_schema()
        assert "type" in schema
        assert schema["type"] == "object"
        
        # Temizleme
        await plugin._safe_cleanup()


if __name__ == "__main__":
    pytest.main([__file__])
