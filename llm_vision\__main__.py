"""
LLM Vision System ana çalıştırma modülü
"""

import asyncio
import argparse
import sys
from pathlib import Path

# Proje kök dizinini sys.path'e ekle
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from llm_vision.core.system_coordinator import SystemCoordinator
from llm_vision.config import config
from llm_vision.utils.logger import get_logger, setup_logging

logger = get_logger(__name__)


def create_parser() -> argparse.ArgumentParser:
    """Komut satırı argüman parser'ı oluştur"""
    parser = argparse.ArgumentParser(
        description="LLM Vision System - MCP Sunucusu",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Örnekler:
  python -m llm_vision                    # Varsayılan ayarlarla başlat
  python -m llm_vision --host 0.0.0.0    # Tüm arayüzlerde dinle
  python -m llm_vision --port 8080       # Farklı port kullan
  python -m llm_vision --debug           # Debug modunda çalıştır
        """
    )
    
    parser.add_argument(
        "--host",
        type=str,
        default=config.server.host,
        help=f"Sunucu host adresi (varsayılan: {config.server.host})"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=config.server.port,
        help=f"Sunucu port numarası (varsayılan: {config.server.port})"
    )
    
    parser.add_argument(
        "--log-level", "-l",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Log seviyesi"
    )

    parser.add_argument(
        "--enable-camera",
        action="store_true",
        help="Kamera desteğini etkinleştir"
    )

    parser.add_argument(
        "--enable-yolo",
        action="store_true",
        help="YOLO nesne tanıma modelini etkinleştir"
    )

    parser.add_argument(
        "--config", "-c",
        type=str,
        help="Konfigürasyon dosyası yolu"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version=f"LLM Vision System {config.mcp.server_version}"
    )
    
    return parser


async def main() -> None:
    """Ana fonksiyon"""
    parser = create_parser()
    args = parser.parse_args()

    # Logging'i kur
    setup_logging(level=args.log_level)

    # Konfigürasyonu güncelle
    if args.config:
        config.load_from_file(args.config)

    if args.enable_camera:
        config.vision.enable_camera = True

    if args.enable_yolo:
        config.vision.enable_yolo = True

    config.server.host = args.host
    config.server.port = args.port

    logger.info("LLM Vision System başlatılıyor...")
    logger.info(f"Konfigürasyon: Camera={config.vision.enable_camera}, YOLO={config.vision.enable_yolo}")
    logger.info(f"Sunucu: {config.server.host}:{config.server.port}")

    # Sistem koordinatörünü oluştur ve başlat
    coordinator = SystemCoordinator()

    try:
        await coordinator.run()
    except KeyboardInterrupt:
        logger.info("Kullanıcı tarafından durduruldu")
    except Exception as e:
        logger.error(f"Sistem çalıştırma hatası: {e}")
        return 1
    finally:
        await coordinator.shutdown()

    logger.info("LLM Vision System kapatıldı")
    return 0


def run() -> None:
    """Senkron çalıştırma fonksiyonu"""
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code or 0)
    except KeyboardInterrupt:
        print("\nKullanıcı tarafından durduruldu")
        sys.exit(0)
    except Exception as e:
        print(f"Kritik hata: {e}")
        sys.exit(1)


if __name__ == "__main__":
    run()
