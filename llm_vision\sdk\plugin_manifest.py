"""
Plugin Manifest System

Plugin'lerin metadata'sını ve konfigürasyonunu yöneten sınıflar.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional
from enum import Enum
import json
import yaml
from pathlib import Path

from ..utils.logger import get_logger

logger = get_logger(__name__)


class PluginType(Enum):
    """Plugin tipleri"""
    SENSOR = "sensor"
    CONTEXT = "context"
    ACTION = "action"
    MEMORY = "memory"


class PluginStatus(Enum):
    """Plugin durumları"""
    INACTIVE = "inactive"
    INITIALIZING = "initializing"
    ACTIVE = "active"
    ERROR = "error"
    DISABLED = "disabled"


@dataclass
class PluginEndpoint:
    """Plugin endpoint tanımı"""
    path: str
    method: str
    description: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    response_schema: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PluginPermission:
    """Plugin izin tanımı"""
    name: str
    description: str
    required: bool = True


@dataclass
class PluginDependency:
    """Plugin bağımlılık tanımı"""
    name: str
    version: str
    optional: bool = False


@dataclass
class PluginManifest:
    """Plugin manifest bilgileri"""
    name: str
    version: str
    type: PluginType
    description: str
    author: str
    
    # Opsiyonel alanlar
    dependencies: List[PluginDependency] = field(default_factory=list)
    permissions: List[PluginPermission] = field(default_factory=list)
    endpoints: List[PluginEndpoint] = field(default_factory=list)
    config_schema: Dict[str, Any] = field(default_factory=dict)
    
    # Metadata
    homepage: Optional[str] = None
    repository: Optional[str] = None
    license: Optional[str] = None
    keywords: List[str] = field(default_factory=list)
    
    # Sistem gereksinimleri
    min_python_version: str = "3.9"
    min_system_version: Optional[str] = None
    supported_platforms: List[str] = field(default_factory=lambda: ["windows", "linux", "macos"])
    
    @classmethod
    def from_file(cls, manifest_path: Path) -> "PluginManifest":
        """Dosyadan manifest yükle"""
        try:
            if not manifest_path.exists():
                raise FileNotFoundError(f"Manifest dosyası bulunamadı: {manifest_path}")
            
            # YAML veya JSON formatını destekle
            if manifest_path.suffix.lower() in ['.yml', '.yaml']:
                with open(manifest_path, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
            elif manifest_path.suffix.lower() == '.json':
                with open(manifest_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            else:
                raise ValueError(f"Desteklenmeyen manifest formatı: {manifest_path.suffix}")
            
            return cls.from_dict(data)
            
        except Exception as e:
            logger.error(f"Manifest yükleme hatası: {e}")
            raise
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "PluginManifest":
        """Dictionary'den manifest oluştur"""
        try:
            # Plugin temel bilgileri
            plugin_data = data.get("plugin", {})
            
            # Dependencies'i parse et
            dependencies = []
            for dep in data.get("dependencies", []):
                if isinstance(dep, str):
                    # Basit string format: "package>=1.0.0"
                    name = dep.split(">=")[0].split("==")[0].split(">")[0].split("<")[0]
                    version = dep.replace(name, "").lstrip(">=<!")
                    dependencies.append(PluginDependency(name=name, version=version))
                elif isinstance(dep, dict):
                    dependencies.append(PluginDependency(**dep))
            
            # Permissions'ı parse et
            permissions = []
            for perm in data.get("permissions", []):
                if isinstance(perm, str):
                    permissions.append(PluginPermission(name=perm, description=f"Permission: {perm}"))
                elif isinstance(perm, dict):
                    permissions.append(PluginPermission(**perm))
            
            # Endpoints'i parse et
            endpoints = []
            for endpoint in data.get("endpoints", []):
                if isinstance(endpoint, dict):
                    endpoints.append(PluginEndpoint(**endpoint))
            
            return cls(
                name=plugin_data.get("name"),
                version=plugin_data.get("version"),
                type=PluginType(plugin_data.get("type")),
                description=plugin_data.get("description", ""),
                author=plugin_data.get("author", "Unknown"),
                dependencies=dependencies,
                permissions=permissions,
                endpoints=endpoints,
                config_schema=data.get("config_schema", {}),
                homepage=plugin_data.get("homepage"),
                repository=plugin_data.get("repository"),
                license=plugin_data.get("license"),
                keywords=plugin_data.get("keywords", []),
                min_python_version=plugin_data.get("min_python_version", "3.9"),
                min_system_version=plugin_data.get("min_system_version"),
                supported_platforms=plugin_data.get("supported_platforms", ["windows", "linux", "macos"])
            )
            
        except Exception as e:
            logger.error(f"Manifest parsing hatası: {e}")
            raise ValueError(f"Geçersiz manifest formatı: {str(e)}")
    
    def to_dict(self) -> Dict[str, Any]:
        """Manifest'i dictionary'e çevir"""
        return {
            "plugin": {
                "name": self.name,
                "version": self.version,
                "type": self.type.value,
                "description": self.description,
                "author": self.author,
                "homepage": self.homepage,
                "repository": self.repository,
                "license": self.license,
                "keywords": self.keywords,
                "min_python_version": self.min_python_version,
                "min_system_version": self.min_system_version,
                "supported_platforms": self.supported_platforms
            },
            "dependencies": [
                {
                    "name": dep.name,
                    "version": dep.version,
                    "optional": dep.optional
                } for dep in self.dependencies
            ],
            "permissions": [
                {
                    "name": perm.name,
                    "description": perm.description,
                    "required": perm.required
                } for perm in self.permissions
            ],
            "endpoints": [
                {
                    "path": endpoint.path,
                    "method": endpoint.method,
                    "description": endpoint.description,
                    "parameters": endpoint.parameters,
                    "response_schema": endpoint.response_schema
                } for endpoint in self.endpoints
            ],
            "config_schema": self.config_schema
        }
    
    def save_to_file(self, manifest_path: Path) -> None:
        """Manifest'i dosyaya kaydet"""
        try:
            manifest_path.parent.mkdir(parents=True, exist_ok=True)
            
            if manifest_path.suffix.lower() in ['.yml', '.yaml']:
                with open(manifest_path, 'w', encoding='utf-8') as f:
                    yaml.dump(self.to_dict(), f, default_flow_style=False, allow_unicode=True)
            elif manifest_path.suffix.lower() == '.json':
                with open(manifest_path, 'w', encoding='utf-8') as f:
                    json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
            else:
                raise ValueError(f"Desteklenmeyen format: {manifest_path.suffix}")
                
            logger.info(f"Manifest kaydedildi: {manifest_path}")
            
        except Exception as e:
            logger.error(f"Manifest kaydetme hatası: {e}")
            raise
    
    def validate(self) -> List[str]:
        """Manifest'i doğrula ve hataları döndür"""
        errors = []
        
        # Zorunlu alanları kontrol et
        if not self.name:
            errors.append("Plugin adı boş olamaz")
        if not self.version:
            errors.append("Plugin versiyonu boş olamaz")
        if not self.description:
            errors.append("Plugin açıklaması boş olamaz")
        if not self.author:
            errors.append("Plugin yazarı boş olamaz")
        
        # Versiyon formatını kontrol et
        try:
            version_parts = self.version.split('.')
            if len(version_parts) != 3:
                errors.append("Versiyon formatı X.Y.Z olmalıdır")
            else:
                for part in version_parts:
                    int(part)  # Sayı olup olmadığını kontrol et
        except ValueError:
            errors.append("Geçersiz versiyon formatı")
        
        # Plugin tipini kontrol et
        if self.type not in PluginType:
            errors.append(f"Geçersiz plugin tipi: {self.type}")
        
        return errors
