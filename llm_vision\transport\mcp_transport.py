"""
Ana MCP transport modülü
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
import uuid

from ..utils.logger import get_logger
from ..utils.exceptions import LLMVisionError
from ..server.models import MCPRequest, MCPResponse
from .client_manager import ClientManager, ClientInfo
from .message_handler import MessageHandler
from .notification_manager import NotificationManager

logger = get_logger(__name__)


class MCPTransport:
    """Ana MCP transport sınıfı"""
    
    def __init__(self):
        self.client_manager = ClientManager()
        self.message_handler = MessageHandler(self.client_manager)
        self.notification_manager = NotificationManager(self.client_manager)
        
        self.is_initialized = False
        
        # Entegre edilecek bileşenler
        self.vision_processor = None
        self.context_engine = None
        self.data_collector = None
        
        logger.info("MCP Transport başlatıldı")
    
    async def initialize(self, 
                        vision_processor=None,
                        context_engine=None,
                        data_collector=None) -> None:
        """MCP Transport'u başlat"""
        try:
            # Bileşenleri kaydet
            self.vision_processor = vision_processor
            self.context_engine = context_engine
            self.data_collector = data_collector
            
            # Mesaj işleyicilerini kaydet
            await self._register_message_handlers()
            
            # Bildirim filtrelerini ayarla
            self._setup_notification_filters()
            
            self.is_initialized = True
            logger.info("MCP Transport başarıyla başlatıldı")
            
        except Exception as e:
            logger.error(f"MCP Transport başlatma hatası: {e}")
            raise LLMVisionError(f"MCP Transport başlatma hatası: {str(e)}")
    
    async def _register_message_handlers(self) -> None:
        """Mesaj işleyicilerini kaydet"""
        try:
            # İstek işleyicileri
            self.message_handler.register_request_handler(
                "initialize", self._handle_initialize
            )
            self.message_handler.register_request_handler(
                "resources/list", self._handle_list_resources
            )
            self.message_handler.register_request_handler(
                "resources/read", self._handle_read_resource
            )
            self.message_handler.register_request_handler(
                "tools/list", self._handle_list_tools
            )
            self.message_handler.register_request_handler(
                "tools/call", self._handle_call_tool
            )
            self.message_handler.register_request_handler(
                "resources/subscribe", self._handle_subscribe
            )
            self.message_handler.register_request_handler(
                "resources/unsubscribe", self._handle_unsubscribe
            )
            
            # Bildirim işleyicileri
            self.message_handler.register_notification_handler(
                "notifications/initialized", self._handle_client_initialized
            )
            
            logger.debug("Mesaj işleyicileri kaydedildi")
            
        except Exception as e:
            logger.error(f"Mesaj işleyici kaydetme hatası: {e}")
            raise
    
    def _setup_notification_filters(self) -> None:
        """Bildirim filtrelerini ayarla"""
        try:
            # Yetenek tabanlı filtre
            def capability_filter(client_id: str, notification) -> bool:
                client = self.client_manager.get_client(client_id)
                if not client:
                    return False
                
                # Belirli bildirimlerin belirli yetenekleri gerektirmesi
                required_caps = {
                    "notifications/resources/list_changed": "resources",
                    "notifications/tools/list_changed": "tools"
                }
                
                required_cap = required_caps.get(notification.method)
                if required_cap:
                    return required_cap in client.capabilities
                
                return True
            
            self.notification_manager.add_notification_filter(
                "capability_filter", capability_filter
            )
            
            logger.debug("Bildirim filtreleri ayarlandı")
            
        except Exception as e:
            logger.error(f"Bildirim filtre ayarlama hatası: {e}")
    
    async def handle_client_connection(self, 
                                     client_id: str,
                                     connection: Any,
                                     transport_type: str = "websocket") -> ClientInfo:
        """Yeni istemci bağlantısını işle"""
        try:
            # Geçici istemci bilgisi (initialize ile güncellenecek)
            temp_client_info = {
                "name": "Unknown Client",
                "version": "0.0.0",
                "capabilities": {},
                "protocol_version": "2024-11-05"
            }
            
            # İstemciyi kaydet
            client = self.client_manager.register_client(
                client_id, temp_client_info, connection, transport_type
            )
            
            logger.info(f"Yeni istemci bağlantısı: {client_id}")
            return client
            
        except Exception as e:
            logger.error(f"İstemci bağlantı işleme hatası: {e}")
            raise LLMVisionError(f"İstemci bağlantı işleme hatası: {str(e)}")
    
    async def handle_client_disconnection(self, client_id: str) -> bool:
        """İstemci bağlantı kesme işlemi"""
        try:
            success = self.client_manager.unregister_client(client_id)
            
            if success:
                logger.info(f"İstemci bağlantısı kesildi: {client_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"İstemci bağlantı kesme hatası: {e}")
            return False
    
    async def handle_client_message(self, 
                                  client_id: str, 
                                  message: str) -> Optional[str]:
        """İstemci mesajını işle"""
        try:
            return await self.message_handler.handle_message(client_id, message)
            
        except Exception as e:
            logger.error(f"İstemci mesaj işleme hatası: {e}")
            return None
    
    async def _handle_initialize(self, 
                               client_id: str, 
                               request: MCPRequest) -> MCPResponse:
        """Initialize isteğini işle"""
        try:
            if not request.params:
                raise ValueError("Initialize parametreleri eksik")
            
            # İstemci bilgilerini güncelle
            client_info = request.params.get("client_info", {})
            capabilities = request.params.get("capabilities", {})
            protocol_version = request.params.get("protocol_version", "2024-11-05")
            
            # Mevcut istemciyi güncelle
            client = self.client_manager.get_client(client_id)
            if client:
                client.name = client_info.get("name", client.name)
                client.version = client_info.get("version", client.version)
                client.capabilities = capabilities
                client.protocol_version = protocol_version
            
            # Sunucu yeteneklerini tanımla
            server_capabilities = {
                "resources": {
                    "subscribe": True,
                    "list_changed": True
                },
                "tools": {
                    "list_changed": True
                },
                "logging": {},
                "prompts": {}
            }
            
            # Sunucu bilgileri
            server_info = {
                "name": "LLM Vision MCP Server",
                "version": "0.1.0"
            }
            
            response_data = {
                "protocol_version": "2024-11-05",
                "capabilities": server_capabilities,
                "server_info": server_info
            }
            
            logger.info(f"İstemci başlatıldı: {client.name} ({client_id})")
            
            return MCPResponse(
                id=request.id,
                result=response_data
            )
            
        except Exception as e:
            logger.error(f"Initialize işleme hatası: {e}")
            return MCPResponse(
                id=request.id,
                error={
                    "code": -32602,
                    "message": "Invalid params",
                    "data": {"details": str(e)}
                }
            )
    
    async def _handle_list_resources(self, 
                                   client_id: str, 
                                   request: MCPRequest) -> MCPResponse:
        """Kaynakları listele"""
        try:
            resources = [
                {
                    "uri": "camera://live",
                    "name": "Live Camera Feed",
                    "description": "Gerçek zamanlı kamera görüntüsü",
                    "mimeType": "image/jpeg"
                },
                {
                    "uri": "file://last_modified",
                    "name": "Last Modified File",
                    "description": "En son değiştirilen dosya",
                    "mimeType": "text/plain"
                },
                {
                    "uri": "screen://capture",
                    "name": "Screen Capture",
                    "description": "Ekran görüntüsü",
                    "mimeType": "image/png"
                },
                {
                    "uri": "system://status",
                    "name": "System Status",
                    "description": "Sistem durumu bilgileri",
                    "mimeType": "application/json"
                },
                {
                    "uri": "context://recent",
                    "name": "Recent Context",
                    "description": "Son bağlam analizi",
                    "mimeType": "application/json"
                }
            ]
            
            return MCPResponse(
                id=request.id,
                result={"resources": resources}
            )
            
        except Exception as e:
            logger.error(f"Kaynak listeleme hatası: {e}")
            return MCPResponse(
                id=request.id,
                error={
                    "code": -32603,
                    "message": "Internal error",
                    "data": {"details": str(e)}
                }
            )

    async def _handle_read_resource(self,
                                  client_id: str,
                                  request: MCPRequest) -> MCPResponse:
        """Kaynak oku"""
        try:
            if not request.params or "uri" not in request.params:
                raise ValueError("URI parametresi eksik")

            uri = request.params["uri"]

            # URI'ye göre kaynak oku
            if uri == "camera://live":
                content = await self._read_camera_resource()
            elif uri == "file://last_modified":
                content = await self._read_file_resource()
            elif uri == "screen://capture":
                content = await self._read_screen_resource()
            elif uri == "system://status":
                content = await self._read_system_resource()
            elif uri == "context://recent":
                content = await self._read_context_resource()
            else:
                raise ValueError(f"Desteklenmeyen URI: {uri}")

            return MCPResponse(
                id=request.id,
                result={"contents": [content]}
            )

        except Exception as e:
            logger.error(f"Kaynak okuma hatası: {e}")
            return MCPResponse(
                id=request.id,
                error={
                    "code": -32603,
                    "message": "Internal error",
                    "data": {"details": str(e)}
                }
            )

    async def _handle_list_tools(self,
                               client_id: str,
                               request: MCPRequest) -> MCPResponse:
        """Araçları listele"""
        try:
            tools = [
                {
                    "name": "analyze_camera",
                    "description": "Kamera görüntüsünü analiz et",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "detect_objects": {
                                "type": "boolean",
                                "description": "Nesne tanıma yap",
                                "default": True
                            },
                            "extract_text": {
                                "type": "boolean",
                                "description": "Metin çıkar",
                                "default": False
                            },
                            "confidence_threshold": {
                                "type": "number",
                                "description": "Güven eşiği",
                                "minimum": 0.0,
                                "maximum": 1.0,
                                "default": 0.5
                            }
                        }
                    }
                },
                {
                    "name": "analyze_screen",
                    "description": "Ekran görüntüsünü analiz et",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "region": {
                                "type": "object",
                                "description": "Analiz edilecek bölge",
                                "properties": {
                                    "x": {"type": "integer"},
                                    "y": {"type": "integer"},
                                    "width": {"type": "integer"},
                                    "height": {"type": "integer"}
                                }
                            },
                            "detect_objects": {
                                "type": "boolean",
                                "default": True
                            },
                            "extract_text": {
                                "type": "boolean",
                                "default": True
                            }
                        }
                    }
                },
                {
                    "name": "analyze_file",
                    "description": "Dosyayı analiz et",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "file_path": {
                                "type": "string",
                                "description": "Dosya yolu"
                            },
                            "analysis_type": {
                                "type": "string",
                                "enum": ["content", "metadata", "both"],
                                "default": "both"
                            }
                        },
                        "required": ["file_path"]
                    }
                },
                {
                    "name": "get_system_status",
                    "description": "Sistem durumunu al",
                    "inputSchema": {
                        "type": "object",
                        "properties": {}
                    }
                },
                {
                    "name": "create_context",
                    "description": "Kapsamlı bağlam oluştur",
                    "inputSchema": {
                        "type": "object",
                        "properties": {
                            "query": {
                                "type": "string",
                                "description": "Kullanıcı sorusu"
                            },
                            "include_recent": {
                                "type": "boolean",
                                "description": "Son verileri dahil et",
                                "default": True
                            }
                        }
                    }
                }
            ]

            return MCPResponse(
                id=request.id,
                result={"tools": tools}
            )

        except Exception as e:
            logger.error(f"Araç listeleme hatası: {e}")
            return MCPResponse(
                id=request.id,
                error={
                    "code": -32603,
                    "message": "Internal error",
                    "data": {"details": str(e)}
                }
            )

    async def _handle_call_tool(self,
                              client_id: str,
                              request: MCPRequest) -> MCPResponse:
        """Araç çağır"""
        try:
            if not request.params or "name" not in request.params:
                raise ValueError("Araç adı eksik")

            tool_name = request.params["name"]
            arguments = request.params.get("arguments", {})

            # Araç çağır
            if tool_name == "analyze_camera":
                result = await self._call_analyze_camera(arguments)
            elif tool_name == "analyze_screen":
                result = await self._call_analyze_screen(arguments)
            elif tool_name == "analyze_file":
                result = await self._call_analyze_file(arguments)
            elif tool_name == "get_system_status":
                result = await self._call_get_system_status(arguments)
            elif tool_name == "create_context":
                result = await self._call_create_context(arguments)
            else:
                raise ValueError(f"Desteklenmeyen araç: {tool_name}")

            return MCPResponse(
                id=request.id,
                result=result
            )

        except Exception as e:
            logger.error(f"Araç çağırma hatası: {e}")
            return MCPResponse(
                id=request.id,
                error={
                    "code": -32603,
                    "message": "Internal error",
                    "data": {"details": str(e)}
                }
            )

    async def _handle_subscribe(self,
                              client_id: str,
                              request: MCPRequest) -> MCPResponse:
        """Kaynak aboneliği"""
        try:
            if not request.params or "uri" not in request.params:
                raise ValueError("URI parametresi eksik")

            uri = request.params["uri"]

            # Abonelik ekle
            success = self.client_manager.add_subscription(client_id, uri)

            if success:
                logger.info(f"Abonelik eklendi: {client_id} -> {uri}")
                return MCPResponse(
                    id=request.id,
                    result={"subscribed": True}
                )
            else:
                raise ValueError("Abonelik eklenemedi")

        except Exception as e:
            logger.error(f"Abonelik hatası: {e}")
            return MCPResponse(
                id=request.id,
                error={
                    "code": -32603,
                    "message": "Internal error",
                    "data": {"details": str(e)}
                }
            )

    async def _handle_unsubscribe(self,
                                client_id: str,
                                request: MCPRequest) -> MCPResponse:
        """Kaynak abonelik iptali"""
        try:
            if not request.params or "uri" not in request.params:
                raise ValueError("URI parametresi eksik")

            uri = request.params["uri"]

            # Abonelik kaldır
            success = self.client_manager.remove_subscription(client_id, uri)

            if success:
                logger.info(f"Abonelik kaldırıldı: {client_id} -> {uri}")
                return MCPResponse(
                    id=request.id,
                    result={"unsubscribed": True}
                )
            else:
                raise ValueError("Abonelik kaldırılamadı")

        except Exception as e:
            logger.error(f"Abonelik iptali hatası: {e}")
            return MCPResponse(
                id=request.id,
                error={
                    "code": -32603,
                    "message": "Internal error",
                    "data": {"details": str(e)}
                }
            )

    async def _handle_client_initialized(self,
                                       client_id: str,
                                       notification: Dict[str, Any]) -> None:
        """İstemci başlatma bildirimi"""
        try:
            logger.info(f"İstemci başlatma bildirimi alındı: {client_id}")

        except Exception as e:
            logger.error(f"İstemci başlatma bildirim işleme hatası: {e}")

    # Kaynak okuma metodları
    async def _read_camera_resource(self) -> Dict[str, Any]:
        """Kamera kaynağını oku"""
        try:
            if self.vision_processor:
                # Kamera görüntüsünü işle
                result = await self.vision_processor.process_camera_feed()

                return {
                    "uri": "camera://live",
                    "mimeType": "application/json",
                    "text": json.dumps(result, ensure_ascii=False, indent=2)
                }
            else:
                return {
                    "uri": "camera://live",
                    "mimeType": "text/plain",
                    "text": "Vision processor mevcut değil"
                }

        except Exception as e:
            logger.error(f"Kamera kaynak okuma hatası: {e}")
            return {
                "uri": "camera://live",
                "mimeType": "text/plain",
                "text": f"Kamera okuma hatası: {str(e)}"
            }

    async def _read_file_resource(self) -> Dict[str, Any]:
        """Dosya kaynağını oku"""
        try:
            if self.data_collector:
                # En son değiştirilen dosyayı al
                recent_events = self.data_collector.get_collected_data(
                    data_type="file_change", limit=1
                )

                if recent_events:
                    event_data = recent_events[0]
                    return {
                        "uri": "file://last_modified",
                        "mimeType": "application/json",
                        "text": json.dumps(event_data, ensure_ascii=False, indent=2)
                    }
                else:
                    return {
                        "uri": "file://last_modified",
                        "mimeType": "text/plain",
                        "text": "Son dosya değişikliği bulunamadı"
                    }
            else:
                return {
                    "uri": "file://last_modified",
                    "mimeType": "text/plain",
                    "text": "Data collector mevcut değil"
                }

        except Exception as e:
            logger.error(f"Dosya kaynak okuma hatası: {e}")
            return {
                "uri": "file://last_modified",
                "mimeType": "text/plain",
                "text": f"Dosya okuma hatası: {str(e)}"
            }

    async def _read_screen_resource(self) -> Dict[str, Any]:
        """Ekran kaynağını oku"""
        try:
            if self.vision_processor:
                # Ekran görüntüsünü işle
                result = await self.vision_processor.process_screen_capture()

                return {
                    "uri": "screen://capture",
                    "mimeType": "application/json",
                    "text": json.dumps(result, ensure_ascii=False, indent=2)
                }
            else:
                return {
                    "uri": "screen://capture",
                    "mimeType": "text/plain",
                    "text": "Vision processor mevcut değil"
                }

        except Exception as e:
            logger.error(f"Ekran kaynak okuma hatası: {e}")
            return {
                "uri": "screen://capture",
                "mimeType": "text/plain",
                "text": f"Ekran okuma hatası: {str(e)}"
            }

    async def _read_system_resource(self) -> Dict[str, Any]:
        """Sistem kaynağını oku"""
        try:
            if self.data_collector:
                # Sistem bilgilerini al
                system_data = await self.data_collector.collect_system_info()

                return {
                    "uri": "system://status",
                    "mimeType": "application/json",
                    "text": json.dumps(system_data, ensure_ascii=False, indent=2)
                }
            else:
                return {
                    "uri": "system://status",
                    "mimeType": "text/plain",
                    "text": "Data collector mevcut değil"
                }

        except Exception as e:
            logger.error(f"Sistem kaynak okuma hatası: {e}")
            return {
                "uri": "system://status",
                "mimeType": "text/plain",
                "text": f"Sistem okuma hatası: {str(e)}"
            }

    async def _read_context_resource(self) -> Dict[str, Any]:
        """Bağlam kaynağını oku"""
        try:
            if self.context_engine:
                # Son bağlam özetini al
                summary = self.context_engine.get_recent_context_summary()

                return {
                    "uri": "context://recent",
                    "mimeType": "application/json",
                    "text": json.dumps(summary, ensure_ascii=False, indent=2)
                }
            else:
                return {
                    "uri": "context://recent",
                    "mimeType": "text/plain",
                    "text": "Context engine mevcut değil"
                }

        except Exception as e:
            logger.error(f"Bağlam kaynak okuma hatası: {e}")
            return {
                "uri": "context://recent",
                "mimeType": "text/plain",
                "text": f"Bağlam okuma hatası: {str(e)}"
            }

    # Araç çağırma metodları
    async def _call_analyze_camera(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Kamera analizi aracını çağır"""
        try:
            if not self.vision_processor:
                return {"error": "Vision processor mevcut değil"}

            detect_objects = arguments.get("detect_objects", True)
            extract_text = arguments.get("extract_text", False)
            confidence_threshold = arguments.get("confidence_threshold", 0.5)

            result = await self.vision_processor.process_camera_feed(
                detect_objects=detect_objects,
                extract_text=extract_text,
                confidence_threshold=confidence_threshold
            )

            # Context engine ile analiz et
            if self.context_engine:
                analysis = await self.context_engine.analyze_vision_data(result)
                return {
                    "content": [{"type": "text", "text": analysis["analysis"]["analysis"]}],
                    "raw_data": result
                }
            else:
                return {
                    "content": [{"type": "text", "text": result["summary"]}],
                    "raw_data": result
                }

        except Exception as e:
            logger.error(f"Kamera analizi hatası: {e}")
            return {"error": str(e)}

    async def _call_analyze_screen(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Ekran analizi aracını çağır"""
        try:
            if not self.vision_processor:
                return {"error": "Vision processor mevcut değil"}

            region = arguments.get("region")
            detect_objects = arguments.get("detect_objects", True)
            extract_text = arguments.get("extract_text", True)

            # Region tuple'a çevir
            region_tuple = None
            if region:
                region_tuple = (
                    region["x"], region["y"],
                    region["width"], region["height"]
                )

            result = await self.vision_processor.process_screen_capture(
                region=region_tuple,
                detect_objects=detect_objects,
                extract_text=extract_text
            )

            # Context engine ile analiz et
            if self.context_engine:
                analysis = await self.context_engine.analyze_vision_data(result)
                return {
                    "content": [{"type": "text", "text": analysis["analysis"]["analysis"]}],
                    "raw_data": result
                }
            else:
                return {
                    "content": [{"type": "text", "text": result["summary"]}],
                    "raw_data": result
                }

        except Exception as e:
            logger.error(f"Ekran analizi hatası: {e}")
            return {"error": str(e)}

    async def _call_analyze_file(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Dosya analizi aracını çağır"""
        try:
            file_path = arguments.get("file_path")
            if not file_path:
                return {"error": "Dosya yolu belirtilmedi"}

            if not self.data_collector:
                return {"error": "Data collector mevcut değil"}

            # Dosya analizi yap
            from ..filesystem.file_analyzer import FileAnalyzer
            analyzer = FileAnalyzer()
            result = await analyzer.analyze_file(file_path)

            # Context engine ile analiz et
            if self.context_engine:
                analysis = await self.context_engine.analyze_file_data(result)
                return {
                    "content": [{"type": "text", "text": analysis["analysis"]["analysis"]}],
                    "raw_data": result
                }
            else:
                return {
                    "content": [{"type": "text", "text": "Dosya analizi tamamlandı"}],
                    "raw_data": result
                }

        except Exception as e:
            logger.error(f"Dosya analizi hatası: {e}")
            return {"error": str(e)}

    async def _call_get_system_status(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Sistem durumu aracını çağır"""
        try:
            if not self.data_collector:
                return {"error": "Data collector mevcut değil"}

            # Sistem bilgilerini al
            system_data = await self.data_collector.collect_system_info()

            # Context engine ile analiz et
            if self.context_engine:
                analysis = await self.context_engine.analyze_system_data(system_data)
                return {
                    "content": [{"type": "text", "text": analysis["analysis"]["analysis"]}],
                    "raw_data": system_data
                }
            else:
                return {
                    "content": [{"type": "text", "text": "Sistem durumu alındı"}],
                    "raw_data": system_data
                }

        except Exception as e:
            logger.error(f"Sistem durumu hatası: {e}")
            return {"error": str(e)}

    async def _call_create_context(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Bağlam oluşturma aracını çağır"""
        try:
            if not self.context_engine:
                return {"error": "Context engine mevcut değil"}

            query = arguments.get("query")
            include_recent = arguments.get("include_recent", True)

            # Son verileri al
            recent_data = []
            if include_recent and self.data_collector:
                recent_data = self.data_collector.get_collected_data(limit=20)

            # Kapsamlı bağlam oluştur
            context = await self.context_engine.create_comprehensive_context(
                recent_data, query
            )

            return {
                "content": [{"type": "text", "text": context["summary"]}],
                "context_data": context
            }

        except Exception as e:
            logger.error(f"Bağlam oluşturma hatası: {e}")
            return {"error": str(e)}

    def get_transport_status(self) -> Dict[str, Any]:
        """Transport durumunu al"""
        try:
            return {
                "initialized": self.is_initialized,
                "client_stats": self.client_manager.get_client_stats(),
                "message_stats": self.message_handler.get_message_stats(),
                "notification_stats": self.notification_manager.get_notification_stats(),
                "components": {
                    "vision_processor": self.vision_processor is not None,
                    "context_engine": self.context_engine is not None,
                    "data_collector": self.data_collector is not None
                }
            }
        except Exception as e:
            logger.error(f"Transport status alma hatası: {e}")
            return {"error": str(e)}
