# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Environment Variables
.env
.env.local
.env.production

# Logs
logs/
*.log

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/

# Documentation
docs/_build/

# OS
.DS_Store
Thumbs.db

# Project Specific
temp/
cache/
models/
*.mp4
*.avi
*.jpg
*.png
*.jpeg
*.gif
*.bmp
*.tiff
*.webp

# Jupyter
.ipynb_checkpoints/

# MyPy
.mypy_cache/
.dmypy.json
dmypy.json
