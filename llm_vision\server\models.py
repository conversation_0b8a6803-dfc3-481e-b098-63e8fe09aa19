"""
MCP protokol modelleri
"""

from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field
from enum import Enum


class MCPVersion(str, Enum):
    """MCP protokol versiyonları"""
    V2024_11_05 = "2024-11-05"


class MCPMethod(str, Enum):
    """MCP metodları"""
    INITIALIZE = "initialize"
    LIST_RESOURCES = "resources/list"
    READ_RESOURCE = "resources/read"
    LIST_TOOLS = "tools/list"
    CALL_TOOL = "tools/call"
    SUBSCRIBE = "resources/subscribe"
    UNSUBSCRIBE = "resources/unsubscribe"


class MCPRequest(BaseModel):
    """MCP istek modeli"""
    jsonrpc: str = Field(default="2.0", description="JSON-RPC versiyonu")
    id: Union[str, int] = Field(description="İstek ID'si")
    method: str = Field(description="Çağrılacak metod")
    params: Optional[Dict[str, Any]] = Field(default=None, description="Metod parametreleri")


class MCPResponse(BaseModel):
    """MCP yanıt modeli"""
    jsonrpc: str = Field(default="2.0", description="JSON-RPC versiyonu")
    id: Union[str, int] = Field(description="İstek ID'si")
    result: Optional[Dict[str, Any]] = Field(default=None, description="Başarılı yanıt")
    error: Optional[Dict[str, Any]] = Field(default=None, description="Hata bilgisi")


class MCPError(BaseModel):
    """MCP hata modeli"""
    code: int = Field(description="Hata kodu")
    message: str = Field(description="Hata mesajı")
    data: Optional[Dict[str, Any]] = Field(default=None, description="Ek hata verisi")


class ServerInfo(BaseModel):
    """Sunucu bilgi modeli"""
    name: str = Field(description="Sunucu adı")
    version: str = Field(description="Sunucu versiyonu")
    protocol_version: str = Field(description="MCP protokol versiyonu")


class ClientInfo(BaseModel):
    """İstemci bilgi modeli"""
    name: str = Field(description="İstemci adı")
    version: str = Field(description="İstemci versiyonu")


class InitializeRequest(BaseModel):
    """Initialize istek modeli"""
    protocol_version: str = Field(description="MCP protokol versiyonu")
    client_info: ClientInfo = Field(description="İstemci bilgileri")
    capabilities: Dict[str, Any] = Field(default_factory=dict, description="İstemci yetenekleri")


class InitializeResponse(BaseModel):
    """Initialize yanıt modeli"""
    protocol_version: str = Field(description="MCP protokol versiyonu")
    server_info: ServerInfo = Field(description="Sunucu bilgileri")
    capabilities: Dict[str, Any] = Field(default_factory=dict, description="Sunucu yetenekleri")


class Resource(BaseModel):
    """Kaynak modeli"""
    uri: str = Field(description="Kaynak URI'si")
    name: str = Field(description="Kaynak adı")
    description: Optional[str] = Field(default=None, description="Kaynak açıklaması")
    mime_type: Optional[str] = Field(default=None, description="MIME tipi")


class ResourceContent(BaseModel):
    """Kaynak içerik modeli"""
    uri: str = Field(description="Kaynak URI'si")
    mime_type: str = Field(description="MIME tipi")
    text: Optional[str] = Field(default=None, description="Metin içerik")
    blob: Optional[str] = Field(default=None, description="Binary içerik (base64)")


class Tool(BaseModel):
    """Araç modeli"""
    name: str = Field(description="Araç adı")
    description: str = Field(description="Araç açıklaması")
    input_schema: Dict[str, Any] = Field(description="Giriş şeması")


class ToolCall(BaseModel):
    """Araç çağrısı modeli"""
    name: str = Field(description="Araç adı")
    arguments: Dict[str, Any] = Field(default_factory=dict, description="Araç argümanları")


class ToolResult(BaseModel):
    """Araç sonuç modeli"""
    content: List[Dict[str, Any]] = Field(description="Sonuç içeriği")
    is_error: bool = Field(default=False, description="Hata durumu")


class SubscriptionRequest(BaseModel):
    """Abonelik istek modeli"""
    uri: str = Field(description="Kaynak URI'si")


class NotificationMessage(BaseModel):
    """Bildirim mesaj modeli"""
    jsonrpc: str = Field(default="2.0", description="JSON-RPC versiyonu")
    method: str = Field(description="Bildirim metodu")
    params: Dict[str, Any] = Field(description="Bildirim parametreleri")
