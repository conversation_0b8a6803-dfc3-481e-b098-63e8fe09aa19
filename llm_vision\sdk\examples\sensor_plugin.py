"""
Example Sensor Plugin

Sensor plugin geliştirme için örnek implementasyon.
"""

import time
import asyncio
from typing import Dict, Any

from ..base_plugin import BaseSensorPlugin
from ..plugin_manifest import PluginManifest, PluginType, PluginDependency, PluginPermission
from ..plugin_data import PluginData, PluginResponse
from ...utils.logger import get_logger

logger = get_logger(__name__)


class ExampleSensorPlugin(BaseSensorPlugin):
    """Örnek sensor plugin implementasyonu"""
    
    def __init__(self, manifest: PluginManifest = None):
        # Manifest oluştur (eğer verilmemişse)
        if manifest is None:
            manifest = PluginManifest(
                name="example_sensor",
                version="1.0.0",
                type=PluginType.SENSOR,
                description="Örnek sensor plugin - sistem bilgilerini toplar",
                author="LLM Vision Team",
                dependencies=[
                    PluginDependency(name="psutil", version=">=5.9.0")
                ],
                permissions=[
                    PluginPermission(name="system_info", description="Sistem bilgilerine erişim")
                ]
            )
        
        super().__init__(manifest)
        
        # Plugin özel ayarları
        self.collection_interval = 1.0
        self.data_buffer = []
        self.max_buffer_size = 100
    
    async def initialize(self, config: Dict[str, Any] = None) -> bool:
        """Plugin'i başlat"""
        try:
            logger.info(f"Example Sensor Plugin başlatılıyor...")
            
            # Konfigürasyonu uygula
            if config:
                self.collection_interval = config.get("collection_interval", 1.0)
                self.max_buffer_size = config.get("max_buffer_size", 100)
            
            # Bağımlılıkları kontrol et
            try:
                import psutil
                self.psutil = psutil
            except ImportError:
                logger.error("psutil bağımlılığı bulunamadı")
                return False
            
            # Test verisi topla
            test_data = await self.collect_data()
            if not test_data.success:
                logger.error("Test veri toplama başarısız")
                return False
            
            logger.info("Example Sensor Plugin başarıyla başlatıldı")
            return True
            
        except Exception as e:
            logger.error(f"Plugin başlatma hatası: {e}")
            return False
    
    async def cleanup(self) -> None:
        """Plugin'i temizle"""
        try:
            logger.info("Example Sensor Plugin temizleniyor...")
            
            # Sürekli veri toplamayı durdur
            await self.stop_continuous_collection()
            
            # Buffer'ı temizle
            self.data_buffer.clear()
            
            logger.info("Example Sensor Plugin temizlendi")
            
        except Exception as e:
            logger.error(f"Plugin temizleme hatası: {e}")
    
    async def collect_data(self) -> PluginResponse:
        """Sistem verilerini topla"""
        try:
            start_time = time.time()
            
            # Sistem bilgilerini topla
            system_data = {
                "timestamp": time.time(),
                "cpu_percent": self.psutil.cpu_percent(interval=0.1),
                "memory_percent": self.psutil.virtual_memory().percent,
                "disk_usage": {
                    "total": self.psutil.disk_usage('/').total,
                    "used": self.psutil.disk_usage('/').used,
                    "free": self.psutil.disk_usage('/').free,
                    "percent": self.psutil.disk_usage('/').percent
                },
                "network_io": self.psutil.net_io_counters()._asdict(),
                "boot_time": self.psutil.boot_time(),
                "process_count": len(self.psutil.pids())
            }
            
            # Plugin data oluştur
            plugin_data = PluginData(
                source=self.manifest.name,
                data=system_data,
                confidence=0.95,
                tags=["system", "monitoring", "sensor"]
            )
            
            # Buffer'a ekle
            self._add_to_buffer(plugin_data)
            
            execution_time = (time.time() - start_time) * 1000
            
            return PluginResponse.success_response(plugin_data, execution_time)
            
        except Exception as e:
            logger.error(f"Veri toplama hatası: {e}")
            execution_time = (time.time() - start_time) * 1000 if 'start_time' in locals() else 0
            return PluginResponse.error_response(
                error_message=str(e),
                error_code="DATA_COLLECTION_ERROR",
                execution_time_ms=execution_time
            )
    
    def get_schema(self) -> Dict[str, Any]:
        """Plugin veri şemasını döndür"""
        return {
            "type": "object",
            "properties": {
                "timestamp": {"type": "number"},
                "cpu_percent": {"type": "number", "minimum": 0, "maximum": 100},
                "memory_percent": {"type": "number", "minimum": 0, "maximum": 100},
                "disk_usage": {
                    "type": "object",
                    "properties": {
                        "total": {"type": "integer"},
                        "used": {"type": "integer"},
                        "free": {"type": "integer"},
                        "percent": {"type": "number"}
                    }
                },
                "network_io": {
                    "type": "object",
                    "properties": {
                        "bytes_sent": {"type": "integer"},
                        "bytes_recv": {"type": "integer"},
                        "packets_sent": {"type": "integer"},
                        "packets_recv": {"type": "integer"}
                    }
                },
                "boot_time": {"type": "number"},
                "process_count": {"type": "integer"}
            },
            "required": ["timestamp", "cpu_percent", "memory_percent"]
        }
    
    def _add_to_buffer(self, data: PluginData) -> None:
        """Veriyi buffer'a ekle"""
        self.data_buffer.append(data)
        
        # Buffer boyutunu kontrol et
        if len(self.data_buffer) > self.max_buffer_size:
            self.data_buffer.pop(0)  # En eski veriyi kaldır
    
    def get_buffered_data(self, limit: int = None) -> list:
        """Buffer'daki verileri al"""
        if limit:
            return self.data_buffer[-limit:]
        return self.data_buffer.copy()
    
    def get_statistics(self) -> Dict[str, Any]:
        """Plugin istatistiklerini al"""
        if not self.data_buffer:
            return {"error": "No data available"}
        
        # Son 10 veri noktasından istatistik hesapla
        recent_data = self.data_buffer[-10:]
        
        cpu_values = [d.data.get("cpu_percent", 0) for d in recent_data]
        memory_values = [d.data.get("memory_percent", 0) for d in recent_data]
        
        return {
            "buffer_size": len(self.data_buffer),
            "data_points": len(recent_data),
            "cpu_stats": {
                "avg": sum(cpu_values) / len(cpu_values) if cpu_values else 0,
                "min": min(cpu_values) if cpu_values else 0,
                "max": max(cpu_values) if cpu_values else 0
            },
            "memory_stats": {
                "avg": sum(memory_values) / len(memory_values) if memory_values else 0,
                "min": min(memory_values) if memory_values else 0,
                "max": max(memory_values) if memory_values else 0
            },
            "collection_interval": self.collection_interval
        }
