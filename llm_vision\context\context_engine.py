"""
Ana Context Engine modülü
"""

import asyncio
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
import json

from ..config import config
from ..utils.logger import get_logger
from ..utils.exceptions import ContextError
from .llm_client import LLMClient
from .prompt_manager import PromptManager
from .context_builder import ContextBuilder
from ..memory.hybrid_memory import HybridMemorySystem, MemoryPriority

logger = get_logger(__name__)


class ContextEngine:
    """Ana Context Engine sınıfı"""
    
    def __init__(self):
        self.llm_client = LLMClient()
        self.prompt_manager = PromptManager()
        self.context_builder = ContextBuilder()

        # Hibrit hafıza sistemi
        self.memory_system = HybridMemorySystem()

        self.is_initialized = False
        self.context_history: List[Dict[str, Any]] = []
        self.max_history_size = 1000

        # Callback'ler
        self.context_callbacks: List[Callable[[Dict[str, Any]], None]] = []

        # Cache (artık hibrit hafıza sistemi kullan<PERSON>k)
        self.analysis_cache = {}
        self.cache_ttl = 300  # 5 dakika

        logger.info("Context Engine başlatıldı")
    
    async def initialize(self) -> None:
        """Context Engine'i başlat"""
        try:
            # Hibrit hafıza sistemini başlat
            await self.memory_system.initialize()

            # LLM istemciyi başlat
            await self.llm_client.initialize()

            self.is_initialized = True
            logger.info("Context Engine başarıyla başlatıldı")

        except Exception as e:
            logger.error(f"Context Engine başlatma hatası: {e}")
            raise ContextError(f"Context Engine başlatma hatası: {str(e)}")
    
    async def shutdown(self) -> None:
        """Context Engine'i kapat"""
        try:
            # Hibrit hafıza sistemini kapat
            await self.memory_system.shutdown()

            # LLM istemciyi kapat
            await self.llm_client.shutdown()

            self.is_initialized = False

            logger.info("Context Engine kapatıldı")

        except Exception as e:
            logger.error(f"Context Engine kapatma hatası: {e}")
    
    def add_context_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """Context callback'i ekle"""
        self.context_callbacks.append(callback)
    
    def remove_context_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """Context callback'ini kaldır"""
        if callback in self.context_callbacks:
            self.context_callbacks.remove(callback)
    
    async def analyze_vision_data(self, 
                                 vision_data: Dict[str, Any],
                                 custom_prompt: str = None) -> Dict[str, Any]:
        """Görüntü verilerini analiz et"""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            # Cache kontrolü
            cache_key = f"vision_{hash(str(vision_data))}"
            if cache_key in self.analysis_cache:
                cache_entry = self.analysis_cache[cache_key]
                if datetime.now() - cache_entry["timestamp"] < timedelta(seconds=self.cache_ttl):
                    logger.debug("Vision analizi cache'den döndürülüyor")
                    return cache_entry["result"]
            
            # Bağlam oluştur
            context = await self.context_builder.build_vision_context(vision_data)
            
            # LLM analizi
            if custom_prompt:
                analysis_result = await self.llm_client.analyze_data(
                    vision_data, "vision", custom_prompt
                )
            else:
                analysis_result = await self.llm_client.analyze_data(
                    vision_data, "vision"
                )
            
            # Sonucu birleştir
            result = {
                "context": context,
                "analysis": analysis_result,
                "timestamp": datetime.now().isoformat(),
                "data_type": "vision"
            }
            
            # Hibrit hafıza sistemine kaydet
            await self.memory_system.store_context(
                data=result,
                priority=MemoryPriority.HIGH,
                tags=["vision", "analysis"]
            )

            # Cache'e kaydet (geçici uyumluluk için)
            self.analysis_cache[cache_key] = {
                "result": result,
                "timestamp": datetime.now()
            }

            # History'e ekle
            self._add_to_history(result)

            # Callback'leri çağır
            self._trigger_callbacks(result)
            
            logger.info("Vision verisi analiz edildi")
            return result
            
        except Exception as e:
            logger.error(f"Vision analizi hatası: {e}")
            raise ContextError(f"Vision analizi hatası: {str(e)}")
    
    async def analyze_file_data(self, 
                               file_data: Dict[str, Any],
                               custom_prompt: str = None) -> Dict[str, Any]:
        """Dosya verilerini analiz et"""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            # Cache kontrolü
            cache_key = f"file_{hash(str(file_data))}"
            if cache_key in self.analysis_cache:
                cache_entry = self.analysis_cache[cache_key]
                if datetime.now() - cache_entry["timestamp"] < timedelta(seconds=self.cache_ttl):
                    logger.debug("File analizi cache'den döndürülüyor")
                    return cache_entry["result"]
            
            # Bağlam oluştur
            context = await self.context_builder.build_file_context(file_data)
            
            # LLM analizi
            if custom_prompt:
                analysis_result = await self.llm_client.analyze_data(
                    file_data, "file", custom_prompt
                )
            else:
                analysis_result = await self.llm_client.analyze_data(
                    file_data, "file"
                )
            
            # Sonucu birleştir
            result = {
                "context": context,
                "analysis": analysis_result,
                "timestamp": datetime.now().isoformat(),
                "data_type": "file"
            }
            
            # Cache'e kaydet
            self.analysis_cache[cache_key] = {
                "result": result,
                "timestamp": datetime.now()
            }
            
            # History'e ekle
            self._add_to_history(result)
            
            # Callback'leri çağır
            self._trigger_callbacks(result)
            
            logger.info("File verisi analiz edildi")
            return result
            
        except Exception as e:
            logger.error(f"File analizi hatası: {e}")
            raise ContextError(f"File analizi hatası: {str(e)}")
    
    async def analyze_system_data(self, 
                                 system_data: Dict[str, Any],
                                 custom_prompt: str = None) -> Dict[str, Any]:
        """Sistem verilerini analiz et"""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            # Bağlam oluştur
            context = await self.context_builder.build_system_context(system_data)
            
            # LLM analizi
            if custom_prompt:
                analysis_result = await self.llm_client.analyze_data(
                    system_data, "system", custom_prompt
                )
            else:
                analysis_result = await self.llm_client.analyze_data(
                    system_data, "system"
                )
            
            # Sonucu birleştir
            result = {
                "context": context,
                "analysis": analysis_result,
                "timestamp": datetime.now().isoformat(),
                "data_type": "system"
            }
            
            # History'e ekle
            self._add_to_history(result)
            
            # Callback'leri çağır
            self._trigger_callbacks(result)
            
            logger.info("System verisi analiz edildi")
            return result
            
        except Exception as e:
            logger.error(f"System analizi hatası: {e}")
            raise ContextError(f"System analizi hatası: {str(e)}")
    
    async def create_comprehensive_context(self, 
                                         multiple_data: List[Dict[str, Any]],
                                         user_query: str = None) -> Dict[str, Any]:
        """Çoklu veriden kapsamlı bağlam oluştur"""
        try:
            if not self.is_initialized:
                await self.initialize()
            
            # Kapsamlı bağlam oluştur
            context = await self.context_builder.build_comprehensive_context(multiple_data)
            
            # LLM ile özet oluştur
            context_summary = await self.llm_client.create_context_summary(
                multiple_data, "comprehensive"
            )
            
            # Kullanıcı sorgusu varsa özel analiz
            query_analysis = None
            if user_query:
                query_prompt = f"""
Kullanıcı sorusu: {user_query}

Mevcut bağlam: {context_summary}

Bu soruyu yanıtlamak için mevcut bağlamı kullan ve kapsamlı bir yanıt oluştur.
"""
                query_analysis = await self.llm_client.generate_completion(
                    query_prompt, 
                    "Sen LLM Vision System'in Context Engine'isin. Kullanıcı sorularını mevcut bağlam ile yanıtlıyorsun."
                )
            
            # Sonucu birleştir
            result = {
                "context": context,
                "summary": context_summary,
                "query_analysis": query_analysis,
                "user_query": user_query,
                "timestamp": datetime.now().isoformat(),
                "data_type": "comprehensive"
            }
            
            # History'e ekle
            self._add_to_history(result)
            
            # Callback'leri çağır
            self._trigger_callbacks(result)
            
            logger.info("Kapsamlı bağlam oluşturuldu")
            return result
            
        except Exception as e:
            logger.error(f"Kapsamlı bağlam oluşturma hatası: {e}")
            raise ContextError(f"Kapsamlı bağlam oluşturma hatası: {str(e)}")
    
    def _add_to_history(self, result: Dict[str, Any]) -> None:
        """Sonucu history'e ekle"""
        try:
            self.context_history.append(result)
            
            # Maksimum boyut kontrolü
            if len(self.context_history) > self.max_history_size:
                self.context_history.pop(0)
            
            logger.debug("Context history'e eklendi")
            
        except Exception as e:
            logger.error(f"History ekleme hatası: {e}")
    
    def _trigger_callbacks(self, result: Dict[str, Any]) -> None:
        """Callback'leri tetikle"""
        try:
            for callback in self.context_callbacks:
                try:
                    callback(result)
                except Exception as e:
                    logger.error(f"Context callback hatası: {e}")
                    
        except Exception as e:
            logger.error(f"Callback tetikleme hatası: {e}")
    
    def get_context_history(self, 
                           limit: int = 50,
                           data_type: str = None,
                           since: datetime = None) -> List[Dict[str, Any]]:
        """Context history'sini al"""
        try:
            history = self.context_history.copy()
            
            # Tip filtresi
            if data_type:
                history = [h for h in history if h.get("data_type") == data_type]
            
            # Zaman filtresi
            if since:
                history = [
                    h for h in history 
                    if datetime.fromisoformat(h["timestamp"]) >= since
                ]
            
            # Sırala ve sınırla
            history.sort(key=lambda x: x["timestamp"], reverse=True)
            return history[:limit]
            
        except Exception as e:
            logger.error(f"History alma hatası: {e}")
            return []
    
    def get_recent_context_summary(self, 
                                  hours: int = 1) -> Dict[str, Any]:
        """Son bağlam özetini al"""
        try:
            since = datetime.now() - timedelta(hours=hours)
            recent_history = self.get_context_history(since=since)
            
            if not recent_history:
                return {
                    "summary": "Son saatte analiz edilen veri yok",
                    "count": 0,
                    "types": []
                }
            
            # Tip sayıları
            type_counts = {}
            for item in recent_history:
                data_type = item.get("data_type", "unknown")
                type_counts[data_type] = type_counts.get(data_type, 0) + 1
            
            # Özet oluştur
            summary_parts = []
            for data_type, count in type_counts.items():
                summary_parts.append(f"{count} {data_type}")
            
            return {
                "summary": f"Son {hours} saatte {len(recent_history)} analiz: {', '.join(summary_parts)}",
                "count": len(recent_history),
                "types": type_counts,
                "latest_timestamp": recent_history[0]["timestamp"] if recent_history else None
            }
            
        except Exception as e:
            logger.error(f"Recent context summary hatası: {e}")
            return {"summary": "Özet oluşturulamadı", "count": 0, "types": {}}
    
    def clear_cache(self) -> None:
        """Cache'i temizle"""
        try:
            self.analysis_cache.clear()
            logger.info("Context Engine cache'i temizlendi")
        except Exception as e:
            logger.error(f"Cache temizleme hatası: {e}")
    
    def clear_history(self) -> int:
        """History'yi temizle"""
        try:
            count = len(self.context_history)
            self.context_history.clear()
            logger.info(f"Context history temizlendi ({count} giriş)")
            return count
        except Exception as e:
            logger.error(f"History temizleme hatası: {e}")
            return 0
    
    async def get_contextual_memory(self,
                                   query: str,
                                   session_id: Optional[str] = None,
                                   limit: int = 10) -> List[Dict[str, Any]]:
        """Bağlamsal hafıza arama"""
        try:
            if not self.is_initialized:
                await self.initialize()

            # Hibrit hafıza sisteminden ara
            memory_entries = await self.memory_system.search_context(
                query=query,
                session_id=session_id,
                limit=limit
            )

            # Memory entry'leri dict'e çevir
            results = []
            for entry in memory_entries:
                results.append({
                    "id": entry.id,
                    "data": entry.data,
                    "timestamp": entry.timestamp.isoformat(),
                    "priority": entry.priority.value,
                    "layer": entry.layer.value,
                    "tags": entry.tags
                })

            logger.info(f"Bağlamsal hafıza arama tamamlandı: {len(results)} sonuç")
            return results

        except Exception as e:
            logger.error(f"Bağlamsal hafıza arama hatası: {e}")
            return []

    async def get_session_context(self,
                                 session_id: str,
                                 limit: int = 50) -> Dict[str, Any]:
        """Oturum bağlamını al"""
        try:
            if not self.is_initialized:
                await self.initialize()

            # Hibrit hafıza sisteminden oturum verilerini al
            session_entries = await self.memory_system.get_session_context(
                session_id=session_id,
                limit=limit
            )

            # Oturum özetini oluştur
            context_summary = {
                "session_id": session_id,
                "total_entries": len(session_entries),
                "time_range": {
                    "start": session_entries[-1].timestamp.isoformat() if session_entries else None,
                    "end": session_entries[0].timestamp.isoformat() if session_entries else None
                },
                "entries": []
            }

            for entry in session_entries:
                context_summary["entries"].append({
                    "id": entry.id,
                    "data": entry.data,
                    "timestamp": entry.timestamp.isoformat(),
                    "priority": entry.priority.value,
                    "tags": entry.tags
                })

            logger.info(f"Oturum bağlamı alındı: {session_id} ({len(session_entries)} giriş)")
            return context_summary

        except Exception as e:
            logger.error(f"Oturum bağlamı alma hatası: {e}")
            return {"error": str(e)}

    async def get_memory_metrics(self) -> Dict[str, Any]:
        """Hafıza sistemi metriklerini al"""
        try:
            if not self.is_initialized:
                await self.initialize()

            return await self.memory_system.get_performance_metrics()

        except Exception as e:
            logger.error(f"Hafıza metrikleri alma hatası: {e}")
            return {"error": str(e)}

    def get_engine_status(self) -> Dict[str, Any]:
        """Engine durumunu al"""
        try:
            return {
                "initialized": self.is_initialized,
                "llm_client": self.llm_client.get_model_info(),
                "prompt_templates": self.prompt_manager.get_template_stats(),
                "history_count": len(self.context_history),
                "cache_count": len(self.analysis_cache),
                "callbacks_count": len(self.context_callbacks),
                "recent_summary": self.get_recent_context_summary(),
                "memory_system": self.memory_system.is_initialized if hasattr(self.memory_system, 'is_initialized') else False
            }
        except Exception as e:
            logger.error(f"Status alma hatası: {e}")
            return {"error": str(e)}
    
    async def __aenter__(self):
        """Async context manager giriş"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager çıkış"""
        await self.shutdown()
