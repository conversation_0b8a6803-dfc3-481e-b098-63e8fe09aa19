"""
Test komutları CLI modülü
"""

import asyncio
import argparse
import sys
from pathlib import Path

# Proje kök dizinini sys.path'e ekle
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from llm_vision.cli.commands import (
    run_test_camera, run_test_screen, run_test_context,
    run_analyze_file, run_config, run_health
)
from llm_vision.utils.logger import setup_logging


class TestCommand:
    """Test komutu sınıfı"""
    
    @staticmethod
    def create_parser() -> argparse.ArgumentParser:
        """Argument parser oluştur"""
        parser = argparse.ArgumentParser(
            description="LLM Vision System test komutları"
        )
        
        parser.add_argument(
            "--log-level", "-l",
            choices=["DEBUG", "INFO", "WARNING", "ERROR"],
            default="WARNING",
            help="Log seviyesi"
        )
        
        # Subcommand'lar
        subparsers = parser.add_subparsers(dest="command", help="Test komutları")
        
        # Camera test
        camera_parser = subparsers.add_parser("camera", help="Kamera testi")
        
        # Screen test
        screen_parser = subparsers.add_parser("screen", help="Ekran görüntüsü testi")
        
        # Context test
        context_parser = subparsers.add_parser("context", help="Context engine testi")
        context_parser.add_argument(
            "--query", "-q",
            type=str,
            help="Test sorgusu"
        )
        
        # File analysis
        file_parser = subparsers.add_parser("file", help="Dosya analizi")
        file_parser.add_argument(
            "file_path",
            type=str,
            help="Analiz edilecek dosya yolu"
        )
        
        # Config
        config_parser = subparsers.add_parser("config", help="Konfigürasyon")
        config_parser.add_argument(
            "action",
            choices=["show", "get", "set"],
            help="Konfigürasyon işlemi"
        )
        config_parser.add_argument(
            "--key", "-k",
            type=str,
            help="Konfigürasyon anahtarı"
        )
        config_parser.add_argument(
            "--value", "-v",
            type=str,
            help="Konfigürasyon değeri"
        )
        
        # Health
        health_parser = subparsers.add_parser("health", help="Sistem sağlığı")
        
        return parser
    
    @staticmethod
    async def main():
        """Ana fonksiyon"""
        parser = TestCommand.create_parser()
        args = parser.parse_args()
        
        if not args.command:
            parser.print_help()
            return
        
        # Logging'i kur
        setup_logging(level=args.log_level)
        
        # Komutu çalıştır
        if args.command == "camera":
            await run_test_camera()
        elif args.command == "screen":
            await run_test_screen()
        elif args.command == "context":
            await run_test_context(args.query)
        elif args.command == "file":
            await run_analyze_file(args.file_path)
        elif args.command == "config":
            await run_config(args.action, args.key, args.value)
        elif args.command == "health":
            await run_health()
        else:
            print(f"Bilinmeyen komut: {args.command}")
            sys.exit(1)


def main():
    """Senkron main fonksiyonu"""
    try:
        asyncio.run(TestCommand.main())
    except KeyboardInterrupt:
        print("\nİptal edildi")
        sys.exit(0)
    except Exception as e:
        print(f"Hata: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
