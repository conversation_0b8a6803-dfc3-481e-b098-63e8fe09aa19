"""
İstemci yönetimi modülü
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
import uuid

from ..utils.logger import get_logger
from ..utils.exceptions import LLMVisionError

logger = get_logger(__name__)


@dataclass
class ClientInfo:
    """İstemci bilgi sınıfı"""
    client_id: str
    name: str
    version: str
    capabilities: Dict[str, Any]
    connected_at: datetime
    last_activity: datetime
    protocol_version: str
    transport_type: str  # websocket, stdio, http
    
    def to_dict(self) -> dict:
        """Sözlük formatına çevir"""
        return {
            "client_id": self.client_id,
            "name": self.name,
            "version": self.version,
            "capabilities": self.capabilities,
            "connected_at": self.connected_at.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "protocol_version": self.protocol_version,
            "transport_type": self.transport_type
        }


class ClientManager:
    """İstemci yönetici sınıfı"""
    
    def __init__(self):
        self.clients: Dict[str, ClientInfo] = {}
        self.client_connections: Dict[str, Any] = {}  # WebSocket veya diğer bağlantı nesneleri
        self.client_subscriptions: Dict[str, Set[str]] = {}  # İstemci abonelikleri
        
        # İstatistikler
        self.total_connections = 0
        self.total_disconnections = 0
        self.message_counts: Dict[str, int] = {}
        
        logger.info("İstemci yöneticisi başlatıldı")
    
    def register_client(self, 
                       client_id: str,
                       client_info: Dict[str, Any],
                       connection: Any,
                       transport_type: str = "websocket") -> ClientInfo:
        """Yeni istemci kaydet"""
        try:
            # İstemci bilgilerini oluştur
            client = ClientInfo(
                client_id=client_id,
                name=client_info.get("name", "Unknown Client"),
                version=client_info.get("version", "0.0.0"),
                capabilities=client_info.get("capabilities", {}),
                connected_at=datetime.now(),
                last_activity=datetime.now(),
                protocol_version=client_info.get("protocol_version", "2024-11-05"),
                transport_type=transport_type
            )
            
            # Kaydet
            self.clients[client_id] = client
            self.client_connections[client_id] = connection
            self.client_subscriptions[client_id] = set()
            
            # İstatistikleri güncelle
            self.total_connections += 1
            self.message_counts[client_id] = 0
            
            logger.info(f"İstemci kaydedildi: {client.name} ({client_id})")
            return client
            
        except Exception as e:
            logger.error(f"İstemci kaydetme hatası: {e}")
            raise LLMVisionError(f"İstemci kaydetme hatası: {str(e)}")
    
    def unregister_client(self, client_id: str) -> bool:
        """İstemci kaydını sil"""
        try:
            if client_id in self.clients:
                client = self.clients[client_id]
                
                # Temizle
                del self.clients[client_id]
                self.client_connections.pop(client_id, None)
                self.client_subscriptions.pop(client_id, None)
                
                # İstatistikleri güncelle
                self.total_disconnections += 1
                
                logger.info(f"İstemci kaydı silindi: {client.name} ({client_id})")
                return True
            else:
                logger.warning(f"Silinecek istemci bulunamadı: {client_id}")
                return False
                
        except Exception as e:
            logger.error(f"İstemci kayıt silme hatası: {e}")
            return False
    
    def get_client(self, client_id: str) -> Optional[ClientInfo]:
        """İstemci bilgilerini al"""
        return self.clients.get(client_id)
    
    def get_all_clients(self) -> List[ClientInfo]:
        """Tüm istemcileri al"""
        return list(self.clients.values())
    
    def get_active_clients(self, 
                          timeout_minutes: int = 30) -> List[ClientInfo]:
        """Aktif istemcileri al"""
        try:
            cutoff_time = datetime.now() - timedelta(minutes=timeout_minutes)
            active_clients = []
            
            for client in self.clients.values():
                if client.last_activity >= cutoff_time:
                    active_clients.append(client)
            
            return active_clients
            
        except Exception as e:
            logger.error(f"Aktif istemci alma hatası: {e}")
            return []
    
    def update_client_activity(self, client_id: str) -> None:
        """İstemci aktivitesini güncelle"""
        try:
            if client_id in self.clients:
                self.clients[client_id].last_activity = datetime.now()
                self.message_counts[client_id] = self.message_counts.get(client_id, 0) + 1
                
        except Exception as e:
            logger.error(f"İstemci aktivite güncelleme hatası: {e}")
    
    def get_client_connection(self, client_id: str) -> Optional[Any]:
        """İstemci bağlantısını al"""
        return self.client_connections.get(client_id)
    
    def add_subscription(self, client_id: str, resource_uri: str) -> bool:
        """İstemci aboneliği ekle"""
        try:
            if client_id in self.client_subscriptions:
                self.client_subscriptions[client_id].add(resource_uri)
                logger.debug(f"Abonelik eklendi: {client_id} -> {resource_uri}")
                return True
            else:
                logger.warning(f"Abonelik eklenecek istemci bulunamadı: {client_id}")
                return False
                
        except Exception as e:
            logger.error(f"Abonelik ekleme hatası: {e}")
            return False
    
    def remove_subscription(self, client_id: str, resource_uri: str) -> bool:
        """İstemci aboneliğini kaldır"""
        try:
            if client_id in self.client_subscriptions:
                self.client_subscriptions[client_id].discard(resource_uri)
                logger.debug(f"Abonelik kaldırıldı: {client_id} -> {resource_uri}")
                return True
            else:
                logger.warning(f"Abonelik kaldırılacak istemci bulunamadı: {client_id}")
                return False
                
        except Exception as e:
            logger.error(f"Abonelik kaldırma hatası: {e}")
            return False
    
    def get_client_subscriptions(self, client_id: str) -> Set[str]:
        """İstemci aboneliklerini al"""
        return self.client_subscriptions.get(client_id, set())
    
    def get_subscribers(self, resource_uri: str) -> List[str]:
        """Kaynağa abone olan istemcileri al"""
        try:
            subscribers = []
            
            for client_id, subscriptions in self.client_subscriptions.items():
                if resource_uri in subscriptions:
                    subscribers.append(client_id)
            
            return subscribers
            
        except Exception as e:
            logger.error(f"Abone alma hatası: {e}")
            return []
    
    def cleanup_inactive_clients(self, timeout_minutes: int = 60) -> int:
        """Aktif olmayan istemcileri temizle"""
        try:
            cutoff_time = datetime.now() - timedelta(minutes=timeout_minutes)
            inactive_clients = []
            
            for client_id, client in self.clients.items():
                if client.last_activity < cutoff_time:
                    inactive_clients.append(client_id)
            
            # Temizle
            cleaned_count = 0
            for client_id in inactive_clients:
                if self.unregister_client(client_id):
                    cleaned_count += 1
            
            if cleaned_count > 0:
                logger.info(f"{cleaned_count} aktif olmayan istemci temizlendi")
            
            return cleaned_count
            
        except Exception as e:
            logger.error(f"İstemci temizleme hatası: {e}")
            return 0
    
    def get_client_stats(self) -> Dict[str, Any]:
        """İstemci istatistiklerini al"""
        try:
            active_clients = self.get_active_clients()
            
            # Transport türü sayıları
            transport_counts = {}
            for client in self.clients.values():
                transport_type = client.transport_type
                transport_counts[transport_type] = transport_counts.get(transport_type, 0) + 1
            
            # Capability analizi
            capabilities = set()
            for client in self.clients.values():
                for cap in client.capabilities.keys():
                    capabilities.add(cap)
            
            return {
                "total_clients": len(self.clients),
                "active_clients": len(active_clients),
                "total_connections": self.total_connections,
                "total_disconnections": self.total_disconnections,
                "transport_types": transport_counts,
                "available_capabilities": list(capabilities),
                "total_subscriptions": sum(len(subs) for subs in self.client_subscriptions.values()),
                "message_counts": dict(self.message_counts)
            }
            
        except Exception as e:
            logger.error(f"İstemci istatistik alma hatası: {e}")
            return {}
    
    def get_client_by_name(self, name: str) -> Optional[ClientInfo]:
        """İsim ile istemci bul"""
        try:
            for client in self.clients.values():
                if client.name.lower() == name.lower():
                    return client
            return None
            
        except Exception as e:
            logger.error(f"İstemci arama hatası: {e}")
            return None
    
    def has_capability(self, client_id: str, capability: str) -> bool:
        """İstemcinin belirli yeteneği var mı kontrol et"""
        try:
            client = self.get_client(client_id)
            if client:
                return capability in client.capabilities
            return False
            
        except Exception as e:
            logger.error(f"Yetenek kontrolü hatası: {e}")
            return False
    
    def get_clients_with_capability(self, capability: str) -> List[ClientInfo]:
        """Belirli yeteneğe sahip istemcileri al"""
        try:
            capable_clients = []
            
            for client in self.clients.values():
                if capability in client.capabilities:
                    capable_clients.append(client)
            
            return capable_clients
            
        except Exception as e:
            logger.error(f"Yetenekli istemci alma hatası: {e}")
            return []
    
    def export_client_data(self) -> Dict[str, Any]:
        """İstemci verilerini dışa aktar"""
        try:
            return {
                "clients": [client.to_dict() for client in self.clients.values()],
                "subscriptions": {
                    client_id: list(subs) 
                    for client_id, subs in self.client_subscriptions.items()
                },
                "stats": self.get_client_stats(),
                "export_timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"İstemci veri dışa aktarma hatası: {e}")
            return {}
    
    def generate_client_id(self) -> str:
        """Yeni istemci ID'si oluştur"""
        return str(uuid.uuid4())
    
    def is_client_connected(self, client_id: str) -> bool:
        """İstemci bağlı mı kontrol et"""
        return client_id in self.clients and client_id in self.client_connections
