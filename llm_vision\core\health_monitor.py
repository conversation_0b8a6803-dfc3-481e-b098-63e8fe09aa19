"""
Sistem sağlık izleme modülü
"""

import asyncio
import psutil
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

from ..utils.logger import get_logger
from ..utils.exceptions import LLMVisionError

logger = get_logger(__name__)


class HealthStatus(str, Enum):
    """Sağlık durumu seviyeleri"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


@dataclass
class HealthMetric:
    """Sağlık metriği sınıfı"""
    name: str
    value: float
    unit: str
    status: HealthStatus
    threshold_warning: float = None
    threshold_critical: float = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
    def to_dict(self) -> dict:
        """Sözlük formatına çevir"""
        return {
            "name": self.name,
            "value": self.value,
            "unit": self.unit,
            "status": self.status.value,
            "threshold_warning": self.threshold_warning,
            "threshold_critical": self.threshold_critical,
            "timestamp": self.timestamp.isoformat()
        }


class ComponentHealth:
    """Bileşen sağlık durumu"""
    
    def __init__(self, name: str, component: Any):
        self.name = name
        self.component = component
        self.status = HealthStatus.UNKNOWN
        self.metrics: List[HealthMetric] = []
        self.last_check = None
        self.error_count = 0
        self.last_error = None
    
    def to_dict(self) -> dict:
        """Sözlük formatına çevir"""
        return {
            "name": self.name,
            "status": self.status.value,
            "metrics": [m.to_dict() for m in self.metrics],
            "last_check": self.last_check.isoformat() if self.last_check else None,
            "error_count": self.error_count,
            "last_error": self.last_error
        }


class HealthMonitor:
    """Sistem sağlık izleyici sınıfı"""
    
    def __init__(self):
        # Bileşenler
        self.components: Dict[str, ComponentHealth] = {}
        
        # Sistem metrikleri
        self.system_metrics: List[HealthMetric] = []
        self.metric_history: Dict[str, List[HealthMetric]] = {}
        self.max_history_size = 1000
        
        # İzleme durumu
        self.is_monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None
        self.check_interval = 30  # 30 saniye
        
        # Uyarı callback'leri
        self.alert_callbacks: List[Callable] = []
        
        # Eşik değerleri
        self.thresholds = {
            "cpu_usage": {"warning": 70.0, "critical": 90.0},
            "memory_usage": {"warning": 80.0, "critical": 95.0},
            "disk_usage": {"warning": 85.0, "critical": 95.0},
            "response_time": {"warning": 1000.0, "critical": 5000.0}  # ms
        }
        
        logger.info("Health monitor oluşturuldu")
    
    async def initialize(self) -> None:
        """Health monitor'u başlat"""
        try:
            # İlk sistem kontrolü
            await self._check_system_health()
            
            # İzleme görevini başlat
            self.monitor_task = asyncio.create_task(self._monitoring_loop())
            self.is_monitoring = True
            
            logger.info("Health monitor başlatıldı")
            
        except Exception as e:
            logger.error(f"Health monitor başlatma hatası: {e}")
            raise LLMVisionError(f"Health monitor başlatma hatası: {str(e)}")
    
    async def shutdown(self) -> None:
        """Health monitor'u kapat"""
        try:
            self.is_monitoring = False
            
            # İzleme görevini iptal et
            if self.monitor_task and not self.monitor_task.done():
                self.monitor_task.cancel()
                try:
                    await self.monitor_task
                except asyncio.CancelledError:
                    pass
            
            logger.info("Health monitor kapatıldı")
            
        except Exception as e:
            logger.error(f"Health monitor kapatma hatası: {e}")
    
    def register_component(self, name: str, component: Any) -> None:
        """Bileşen kaydet"""
        try:
            self.components[name] = ComponentHealth(name, component)
            logger.debug(f"Bileşen kaydedildi: {name}")
            
        except Exception as e:
            logger.error(f"Bileşen kaydetme hatası: {e}")
    
    def unregister_component(self, name: str) -> bool:
        """Bileşen kaydını sil"""
        try:
            if name in self.components:
                del self.components[name]
                logger.debug(f"Bileşen kaydı silindi: {name}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Bileşen kayıt silme hatası: {e}")
            return False
    
    def add_alert_callback(self, callback: Callable) -> None:
        """Uyarı callback'i ekle"""
        try:
            self.alert_callbacks.append(callback)
            logger.debug("Uyarı callback'i eklendi")
            
        except Exception as e:
            logger.error(f"Uyarı callback ekleme hatası: {e}")
    
    async def _monitoring_loop(self) -> None:
        """İzleme döngüsü"""
        try:
            while self.is_monitoring:
                try:
                    # Sistem sağlığını kontrol et
                    await self._check_system_health()
                    
                    # Bileşen sağlığını kontrol et
                    await self._check_component_health()
                    
                    # Uyarıları kontrol et
                    await self._check_alerts()
                    
                    # Belirtilen süre bekle
                    await asyncio.sleep(self.check_interval)
                    
                except Exception as e:
                    logger.error(f"İzleme döngüsü hatası: {e}")
                    await asyncio.sleep(10)  # Hata durumunda kısa bekle
                    
        except asyncio.CancelledError:
            logger.debug("İzleme döngüsü iptal edildi")
        except Exception as e:
            logger.error(f"İzleme döngüsü kritik hatası: {e}")
    
    async def _check_system_health(self) -> None:
        """Sistem sağlığını kontrol et"""
        try:
            current_metrics = []
            
            # CPU kullanımı
            cpu_usage = psutil.cpu_percent(interval=1)
            cpu_metric = HealthMetric(
                name="cpu_usage",
                value=cpu_usage,
                unit="%",
                status=self._get_status_by_threshold("cpu_usage", cpu_usage),
                threshold_warning=self.thresholds["cpu_usage"]["warning"],
                threshold_critical=self.thresholds["cpu_usage"]["critical"]
            )
            current_metrics.append(cpu_metric)
            
            # Bellek kullanımı
            memory = psutil.virtual_memory()
            memory_metric = HealthMetric(
                name="memory_usage",
                value=memory.percent,
                unit="%",
                status=self._get_status_by_threshold("memory_usage", memory.percent),
                threshold_warning=self.thresholds["memory_usage"]["warning"],
                threshold_critical=self.thresholds["memory_usage"]["critical"]
            )
            current_metrics.append(memory_metric)
            
            # Disk kullanımı
            disk = psutil.disk_usage('/')
            disk_usage = (disk.used / disk.total) * 100
            disk_metric = HealthMetric(
                name="disk_usage",
                value=disk_usage,
                unit="%",
                status=self._get_status_by_threshold("disk_usage", disk_usage),
                threshold_warning=self.thresholds["disk_usage"]["warning"],
                threshold_critical=self.thresholds["disk_usage"]["critical"]
            )
            current_metrics.append(disk_metric)
            
            # Ağ bağlantıları
            connections = len(psutil.net_connections())
            connection_metric = HealthMetric(
                name="network_connections",
                value=connections,
                unit="count",
                status=HealthStatus.HEALTHY  # Basit durum
            )
            current_metrics.append(connection_metric)
            
            # Mevcut metrikleri güncelle
            self.system_metrics = current_metrics
            
            # Geçmişe ekle
            for metric in current_metrics:
                self._add_to_history(metric)
            
            logger.debug("Sistem sağlık kontrolü tamamlandı")
            
        except Exception as e:
            logger.error(f"Sistem sağlık kontrolü hatası: {e}")
    
    async def _check_component_health(self) -> None:
        """Bileşen sağlığını kontrol et"""
        try:
            for name, comp_health in self.components.items():
                try:
                    # Bileşen durumunu kontrol et
                    status = await self._check_single_component(comp_health)
                    comp_health.status = status
                    comp_health.last_check = datetime.now()
                    
                except Exception as e:
                    comp_health.status = HealthStatus.CRITICAL
                    comp_health.error_count += 1
                    comp_health.last_error = str(e)
                    comp_health.last_check = datetime.now()
                    
                    logger.error(f"Bileşen sağlık kontrolü hatası ({name}): {e}")
            
            logger.debug("Bileşen sağlık kontrolü tamamlandı")
            
        except Exception as e:
            logger.error(f"Bileşen sağlık kontrolü genel hatası: {e}")
    
    async def _check_single_component(self, comp_health: ComponentHealth) -> HealthStatus:
        """Tek bileşen sağlık kontrolü"""
        try:
            component = comp_health.component
            
            # Bileşen tipine göre kontrol
            if hasattr(component, 'is_initialized'):
                if not component.is_initialized:
                    return HealthStatus.CRITICAL
            
            if hasattr(component, 'is_running'):
                if not component.is_running:
                    return HealthStatus.WARNING
            
            if hasattr(component, 'is_collecting'):
                if not component.is_collecting:
                    return HealthStatus.WARNING
            
            # Özel health check metodu varsa çağır
            if hasattr(component, 'health_check'):
                try:
                    result = await component.health_check()
                    if isinstance(result, bool):
                        return HealthStatus.HEALTHY if result else HealthStatus.CRITICAL
                    elif isinstance(result, dict):
                        return HealthStatus(result.get("status", "healthy"))
                except Exception as e:
                    logger.error(f"Bileşen health check hatası: {e}")
                    return HealthStatus.CRITICAL
            
            # Varsayılan olarak sağlıklı
            return HealthStatus.HEALTHY
            
        except Exception as e:
            logger.error(f"Tek bileşen kontrolü hatası: {e}")
            return HealthStatus.CRITICAL
    
    async def _check_alerts(self) -> None:
        """Uyarıları kontrol et"""
        try:
            alerts = []
            
            # Sistem metrik uyarıları
            for metric in self.system_metrics:
                if metric.status in [HealthStatus.WARNING, HealthStatus.CRITICAL]:
                    alerts.append({
                        "type": "system_metric",
                        "severity": metric.status.value,
                        "message": f"{metric.name} {metric.status.value}: {metric.value}{metric.unit}",
                        "metric": metric.to_dict()
                    })
            
            # Bileşen uyarıları
            for name, comp_health in self.components.items():
                if comp_health.status in [HealthStatus.WARNING, HealthStatus.CRITICAL]:
                    alerts.append({
                        "type": "component",
                        "severity": comp_health.status.value,
                        "message": f"Bileşen {name} durumu: {comp_health.status.value}",
                        "component": comp_health.to_dict()
                    })
            
            # Uyarı callback'lerini çağır
            if alerts:
                for callback in self.alert_callbacks:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(alerts)
                        else:
                            callback(alerts)
                    except Exception as e:
                        logger.error(f"Uyarı callback hatası: {e}")
            
        except Exception as e:
            logger.error(f"Uyarı kontrolü hatası: {e}")
    
    def _get_status_by_threshold(self, metric_name: str, value: float) -> HealthStatus:
        """Eşik değerine göre durum belirle"""
        try:
            thresholds = self.thresholds.get(metric_name, {})
            critical = thresholds.get("critical")
            warning = thresholds.get("warning")
            
            if critical is not None and value >= critical:
                return HealthStatus.CRITICAL
            elif warning is not None and value >= warning:
                return HealthStatus.WARNING
            else:
                return HealthStatus.HEALTHY
                
        except Exception as e:
            logger.error(f"Eşik değer kontrolü hatası: {e}")
            return HealthStatus.UNKNOWN
    
    def _add_to_history(self, metric: HealthMetric) -> None:
        """Metriği geçmişe ekle"""
        try:
            if metric.name not in self.metric_history:
                self.metric_history[metric.name] = []
            
            self.metric_history[metric.name].append(metric)
            
            # Maksimum boyut kontrolü
            if len(self.metric_history[metric.name]) > self.max_history_size:
                self.metric_history[metric.name].pop(0)
                
        except Exception as e:
            logger.error(f"Metrik geçmiş ekleme hatası: {e}")
    
    def get_overall_health(self) -> HealthStatus:
        """Genel sistem sağlığını al"""
        try:
            # En kötü durumu bul
            worst_status = HealthStatus.HEALTHY
            
            # Sistem metrikleri
            for metric in self.system_metrics:
                if metric.status == HealthStatus.CRITICAL:
                    return HealthStatus.CRITICAL
                elif metric.status == HealthStatus.WARNING and worst_status == HealthStatus.HEALTHY:
                    worst_status = HealthStatus.WARNING
            
            # Bileşenler
            for comp_health in self.components.values():
                if comp_health.status == HealthStatus.CRITICAL:
                    return HealthStatus.CRITICAL
                elif comp_health.status == HealthStatus.WARNING and worst_status == HealthStatus.HEALTHY:
                    worst_status = HealthStatus.WARNING
            
            return worst_status
            
        except Exception as e:
            logger.error(f"Genel sağlık alma hatası: {e}")
            return HealthStatus.UNKNOWN
    
    def get_health_report(self) -> Dict[str, Any]:
        """Sağlık raporu al"""
        try:
            return {
                "overall_status": self.get_overall_health().value,
                "timestamp": datetime.now().isoformat(),
                "system_metrics": [m.to_dict() for m in self.system_metrics],
                "components": {
                    name: comp.to_dict() 
                    for name, comp in self.components.items()
                },
                "monitoring_status": {
                    "is_monitoring": self.is_monitoring,
                    "check_interval": self.check_interval,
                    "registered_components": len(self.components),
                    "alert_callbacks": len(self.alert_callbacks)
                }
            }
            
        except Exception as e:
            logger.error(f"Sağlık raporu alma hatası: {e}")
            return {"error": str(e)}
    
    def get_metric_history(self, 
                          metric_name: str, 
                          limit: int = 100) -> List[HealthMetric]:
        """Metrik geçmişini al"""
        try:
            history = self.metric_history.get(metric_name, [])
            return history[-limit:] if limit else history
            
        except Exception as e:
            logger.error(f"Metrik geçmiş alma hatası: {e}")
            return []
