"""
Sistem durumu CLI komutu
"""

import asyncio
import argparse
import sys
from pathlib import Path

# Proje kök dizinini sys.path'e ekle
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from llm_vision.cli.commands import run_status
from llm_vision.utils.logger import setup_logging


class StatusCommand:
    """Status komutu sınıfı"""
    
    @staticmethod
    def create_parser() -> argparse.ArgumentParser:
        """Argument parser oluştur"""
        parser = argparse.ArgumentParser(
            description="LLM Vision System durumunu kontrol et"
        )
        
        parser.add_argument(
            "--log-level", "-l",
            choices=["DEBUG", "INFO", "WARNING", "ERROR"],
            default="WARNING",
            help="Log seviyesi"
        )
        
        return parser
    
    @staticmethod
    async def main():
        """Ana fonksiyon"""
        parser = StatusCommand.create_parser()
        args = parser.parse_args()
        
        # Logging'i kur
        setup_logging(level=args.log_level)
        
        # Status komutunu çalıştır
        await run_status()


def main():
    """Senkron main fonksiyonu"""
    try:
        asyncio.run(StatusCommand.main())
    except KeyboardInterrupt:
        print("\nİptal edildi")
        sys.exit(0)
    except Exception as e:
        print(f"Hata: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
