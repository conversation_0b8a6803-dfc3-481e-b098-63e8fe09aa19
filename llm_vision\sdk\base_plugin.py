"""
Base Plugin Classes

Plugin geliştirme için temel sınıflar ve interface'ler.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Callable
import asyncio
import time
import traceback

from .plugin_manifest import PluginManifest, PluginStatus
from .plugin_data import PluginData, PluginResponse, PluginEvent, PluginMetrics
from ..utils.logger import get_logger

logger = get_logger(__name__)


class BasePlugin(ABC):
    """Temel plugin sınıfı - Tüm plugin'ler bu sınıftan türetilmelidir"""
    
    def __init__(self, manifest: PluginManifest):
        self.manifest = manifest
        self.status = PluginStatus.INACTIVE
        self.config: Dict[str, Any] = {}
        self.error_message: Optional[str] = None
        self.metrics = PluginMetrics(plugin_name=manifest.name)
        
        # Event callbacks
        self.event_callbacks: List[Callable[[PluginEvent], None]] = []
        
        # Plugin lifecycle hooks
        self.lifecycle_hooks: Dict[str, List[Callable]] = {
            "before_initialize": [],
            "after_initialize": [],
            "before_cleanup": [],
            "after_cleanup": []
        }
        
        logger.info(f"Plugin oluşturuldu: {self.manifest.name} v{self.manifest.version}")
    
    @abstractmethod
    async def initialize(self, config: Dict[str, Any] = None) -> bool:
        """
        Plugin'i başlat
        
        Args:
            config: Plugin konfigürasyonu
            
        Returns:
            bool: Başlatma başarılı mı
        """
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Plugin'i temizle ve kaynakları serbest bırak"""
        pass
    
    @abstractmethod
    def get_schema(self) -> Dict[str, Any]:
        """
        Plugin veri şemasını döndür
        
        Returns:
            Dict: JSON Schema formatında veri şeması
        """
        pass
    
    async def health_check(self) -> bool:
        """
        Plugin sağlık kontrolü
        
        Returns:
            bool: Plugin sağlıklı mı
        """
        return self.status == PluginStatus.ACTIVE
    
    def add_event_callback(self, callback: Callable[[PluginEvent], None]) -> None:
        """Event callback ekle"""
        self.event_callbacks.append(callback)
    
    def remove_event_callback(self, callback: Callable[[PluginEvent], None]) -> None:
        """Event callback kaldır"""
        if callback in self.event_callbacks:
            self.event_callbacks.remove(callback)
    
    async def emit_event(self, event_type: str, data: Dict[str, Any], priority: str = "medium") -> None:
        """Event yayınla"""
        event = PluginEvent(
            event_type=event_type,
            source_plugin=self.manifest.name,
            data=data,
            priority=priority
        )
        
        for callback in self.event_callbacks:
            try:
                await callback(event)
            except Exception as e:
                logger.error(f"Event callback hatası: {e}")
    
    def add_lifecycle_hook(self, hook_name: str, callback: Callable) -> None:
        """Lifecycle hook ekle"""
        if hook_name in self.lifecycle_hooks:
            self.lifecycle_hooks[hook_name].append(callback)
    
    async def _run_lifecycle_hooks(self, hook_name: str) -> None:
        """Lifecycle hook'ları çalıştır"""
        for hook in self.lifecycle_hooks.get(hook_name, []):
            try:
                if asyncio.iscoroutinefunction(hook):
                    await hook()
                else:
                    hook()
            except Exception as e:
                logger.error(f"Lifecycle hook hatası ({hook_name}): {e}")
    
    async def _safe_initialize(self, config: Dict[str, Any] = None) -> bool:
        """Güvenli başlatma wrapper'ı"""
        try:
            self.status = PluginStatus.INITIALIZING
            await self._run_lifecycle_hooks("before_initialize")
            
            start_time = time.time()
            success = await self.initialize(config or {})
            execution_time = (time.time() - start_time) * 1000
            
            if success:
                self.status = PluginStatus.ACTIVE
                self.config = config or {}
                self.error_message = None
                logger.info(f"Plugin başlatıldı: {self.manifest.name}")
            else:
                self.status = PluginStatus.ERROR
                self.error_message = "Initialization failed"
                logger.error(f"Plugin başlatma başarısız: {self.manifest.name}")
            
            self.metrics.update_call(success, execution_time, self.error_message)
            await self._run_lifecycle_hooks("after_initialize")
            
            return success
            
        except Exception as e:
            self.status = PluginStatus.ERROR
            self.error_message = str(e)
            logger.error(f"Plugin başlatma hatası: {self.manifest.name} - {e}")
            logger.debug(traceback.format_exc())
            return False
    
    async def _safe_cleanup(self) -> None:
        """Güvenli temizleme wrapper'ı"""
        try:
            await self._run_lifecycle_hooks("before_cleanup")
            
            start_time = time.time()
            await self.cleanup()
            execution_time = (time.time() - start_time) * 1000
            
            self.status = PluginStatus.INACTIVE
            self.metrics.update_call(True, execution_time)
            
            await self._run_lifecycle_hooks("after_cleanup")
            logger.info(f"Plugin temizlendi: {self.manifest.name}")
            
        except Exception as e:
            self.status = PluginStatus.ERROR
            self.error_message = str(e)
            logger.error(f"Plugin temizleme hatası: {self.manifest.name} - {e}")
    
    def get_info(self) -> Dict[str, Any]:
        """Plugin bilgilerini döndür"""
        return {
            "manifest": self.manifest.to_dict(),
            "status": self.status.value,
            "config": self.config,
            "error_message": self.error_message,
            "metrics": self.metrics.to_dict()
        }


class BaseSensorPlugin(BasePlugin):
    """Sensor plugin'ler için temel sınıf"""
    
    @abstractmethod
    async def collect_data(self) -> PluginResponse:
        """
        Veri topla
        
        Returns:
            PluginResponse: Toplanan veri veya hata
        """
        pass
    
    async def start_continuous_collection(self, interval_seconds: float = 1.0) -> None:
        """Sürekli veri toplama başlat"""
        self._collection_task = asyncio.create_task(
            self._continuous_collection_loop(interval_seconds)
        )
    
    async def stop_continuous_collection(self) -> None:
        """Sürekli veri toplamayı durdur"""
        if hasattr(self, '_collection_task'):
            self._collection_task.cancel()
            try:
                await self._collection_task
            except asyncio.CancelledError:
                pass
    
    async def _continuous_collection_loop(self, interval_seconds: float) -> None:
        """Sürekli veri toplama döngüsü"""
        while True:
            try:
                response = await self.collect_data()
                if response.success and response.data:
                    await self.emit_event("data_collected", response.data.to_dict())
                else:
                    logger.warning(f"Veri toplama başarısız: {response.error_message}")
                
                await asyncio.sleep(interval_seconds)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Sürekli veri toplama hatası: {e}")
                await asyncio.sleep(interval_seconds)


class BaseActionPlugin(BasePlugin):
    """Action plugin'ler için temel sınıf"""
    
    @abstractmethod
    async def execute_action(self, action_name: str, parameters: Dict[str, Any]) -> PluginResponse:
        """
        Aksiyon çalıştır
        
        Args:
            action_name: Çalıştırılacak aksiyon adı
            parameters: Aksiyon parametreleri
            
        Returns:
            PluginResponse: Aksiyon sonucu
        """
        pass
    
    @abstractmethod
    def get_available_actions(self) -> List[Dict[str, Any]]:
        """
        Mevcut aksiyonları listele
        
        Returns:
            List: Aksiyon listesi
        """
        pass


class BaseContextPlugin(BasePlugin):
    """Context plugin'ler için temel sınıf"""
    
    @abstractmethod
    async def analyze_context(self, data: Dict[str, Any]) -> PluginResponse:
        """
        Bağlam analizi yap
        
        Args:
            data: Analiz edilecek veri
            
        Returns:
            PluginResponse: Analiz sonucu
        """
        pass
    
    @abstractmethod
    def get_context_schema(self) -> Dict[str, Any]:
        """
        Bağlam şemasını döndür
        
        Returns:
            Dict: Bağlam şeması
        """
        pass
