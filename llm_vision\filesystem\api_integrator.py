"""
API entegrasyon modülü
"""

import asyncio
import aiohttp
import json
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import os
from urllib.parse import urljoin, urlparse

from ..config import config
from ..utils.logger import get_logger
from ..utils.exceptions import LLMVisionError

logger = get_logger(__name__)


class APIIntegrator:
    """API entegrasyon sınıfı"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.api_configs = {}
        self.cache = {}
        self.cache_ttl = config.performance.cache_ttl
        
        logger.info("API entegratör başlatıldı")
    
    async def initialize(self) -> None:
        """API entegratörü başlat"""
        try:
            # HTTP session oluştur
            timeout = aiohttp.ClientTimeout(total=config.performance.request_timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
            # Varsayılan API konfigürasyonları
            await self._load_default_configs()
            
            logger.info("API entegratör başlatıldı")
            
        except Exception as e:
            logger.error(f"API entegratör başlatma hatası: {e}")
            raise LLMVisionError(f"API entegratör başlatma hatası: {str(e)}")
    
    async def shutdown(self) -> None:
        """API entegratörü kapat"""
        try:
            if self.session:
                await self.session.close()
                self.session = None
            
            logger.info("API entegratör kapatıldı")
            
        except Exception as e:
            logger.error(f"API entegratör kapatma hatası: {e}")
    
    async def _load_default_configs(self) -> None:
        """Varsayılan API konfigürasyonlarını yükle"""
        # GitHub API
        self.api_configs["github"] = {
            "base_url": "https://api.github.com",
            "headers": {
                "Accept": "application/vnd.github.v3+json",
                "User-Agent": "LLM-Vision-System"
            },
            "auth_header": "Authorization",
            "auth_prefix": "token"
        }
        
        # OpenAI API
        self.api_configs["openai"] = {
            "base_url": "https://api.openai.com/v1",
            "headers": {
                "Content-Type": "application/json"
            },
            "auth_header": "Authorization",
            "auth_prefix": "Bearer"
        }
        
        # Notion API
        self.api_configs["notion"] = {
            "base_url": "https://api.notion.com/v1",
            "headers": {
                "Notion-Version": "2022-06-28",
                "Content-Type": "application/json"
            },
            "auth_header": "Authorization",
            "auth_prefix": "Bearer"
        }
        
        # Slack API
        self.api_configs["slack"] = {
            "base_url": "https://slack.com/api",
            "headers": {
                "Content-Type": "application/json"
            },
            "auth_header": "Authorization",
            "auth_prefix": "Bearer"
        }
    
    def add_api_config(self, 
                      name: str, 
                      config: Dict[str, Any]) -> None:
        """API konfigürasyonu ekle"""
        required_fields = ["base_url"]
        for field in required_fields:
            if field not in config:
                raise ValueError(f"API konfigürasyonunda {field} eksik")
        
        self.api_configs[name] = config
        logger.info(f"API konfigürasyonu eklendi: {name}")
    
    async def make_request(self, 
                          api_name: str,
                          endpoint: str,
                          method: str = "GET",
                          params: Dict[str, Any] = None,
                          data: Dict[str, Any] = None,
                          headers: Dict[str, str] = None,
                          auth_token: str = None,
                          use_cache: bool = True) -> Dict[str, Any]:
        """API isteği yap"""
        try:
            if not self.session:
                await self.initialize()
            
            if api_name not in self.api_configs:
                raise ValueError(f"API konfigürasyonu bulunamadı: {api_name}")
            
            config = self.api_configs[api_name]
            
            # URL oluştur
            url = urljoin(config["base_url"], endpoint)
            
            # Cache kontrolü
            cache_key = f"{api_name}:{method}:{url}:{str(params)}:{str(data)}"
            if use_cache and method == "GET" and cache_key in self.cache:
                cache_entry = self.cache[cache_key]
                if datetime.now() - cache_entry["timestamp"] < timedelta(seconds=self.cache_ttl):
                    logger.debug(f"Cache'den döndürülüyor: {cache_key}")
                    return cache_entry["data"]
            
            # Header'ları hazırla
            request_headers = config.get("headers", {}).copy()
            if headers:
                request_headers.update(headers)
            
            # Auth token ekle
            if auth_token and "auth_header" in config:
                auth_prefix = config.get("auth_prefix", "")
                auth_value = f"{auth_prefix} {auth_token}".strip()
                request_headers[config["auth_header"]] = auth_value
            
            # İsteği yap
            async with self.session.request(
                method=method,
                url=url,
                params=params,
                json=data,
                headers=request_headers
            ) as response:
                
                # Yanıt kontrolü
                if response.status >= 400:
                    error_text = await response.text()
                    raise aiohttp.ClientResponseError(
                        request_info=response.request_info,
                        history=response.history,
                        status=response.status,
                        message=f"API hatası: {error_text}"
                    )
                
                # JSON yanıtı parse et
                try:
                    result = await response.json()
                except json.JSONDecodeError:
                    result = {"text": await response.text()}
                
                # Cache'e kaydet
                if use_cache and method == "GET":
                    self.cache[cache_key] = {
                        "data": result,
                        "timestamp": datetime.now()
                    }
                
                logger.debug(f"API isteği başarılı: {method} {url}")
                return result
                
        except Exception as e:
            logger.error(f"API isteği hatası: {e}")
            raise LLMVisionError(f"API isteği hatası: {str(e)}")
    
    async def get_github_user_repos(self, 
                                   username: str,
                                   auth_token: str = None) -> List[Dict[str, Any]]:
        """GitHub kullanıcı repolarını al"""
        try:
            result = await self.make_request(
                api_name="github",
                endpoint=f"/users/{username}/repos",
                auth_token=auth_token
            )
            
            # Repo bilgilerini sadeleştir
            repos = []
            for repo in result:
                repos.append({
                    "name": repo.get("name"),
                    "full_name": repo.get("full_name"),
                    "description": repo.get("description"),
                    "language": repo.get("language"),
                    "stars": repo.get("stargazers_count", 0),
                    "forks": repo.get("forks_count", 0),
                    "updated_at": repo.get("updated_at"),
                    "url": repo.get("html_url")
                })
            
            return repos
            
        except Exception as e:
            logger.error(f"GitHub repo alma hatası: {e}")
            raise LLMVisionError(f"GitHub repo alma hatası: {str(e)}")
    
    async def get_github_repo_content(self, 
                                     owner: str,
                                     repo: str,
                                     path: str = "",
                                     auth_token: str = None) -> List[Dict[str, Any]]:
        """GitHub repo içeriğini al"""
        try:
            endpoint = f"/repos/{owner}/{repo}/contents/{path}"
            result = await self.make_request(
                api_name="github",
                endpoint=endpoint,
                auth_token=auth_token
            )
            
            # Tek dosya ise liste yap
            if isinstance(result, dict):
                result = [result]
            
            # İçerik bilgilerini sadeleştir
            contents = []
            for item in result:
                contents.append({
                    "name": item.get("name"),
                    "path": item.get("path"),
                    "type": item.get("type"),  # file, dir
                    "size": item.get("size"),
                    "download_url": item.get("download_url"),
                    "html_url": item.get("html_url")
                })
            
            return contents
            
        except Exception as e:
            logger.error(f"GitHub içerik alma hatası: {e}")
            raise LLMVisionError(f"GitHub içerik alma hatası: {str(e)}")
    
    def clear_cache(self, api_name: str = None) -> None:
        """Cache'i temizle"""
        if api_name:
            # Belirli API'nin cache'ini temizle
            keys_to_remove = [key for key in self.cache.keys() if key.startswith(f"{api_name}:")]
            for key in keys_to_remove:
                del self.cache[key]
            logger.info(f"{api_name} API cache'i temizlendi")
        else:
            # Tüm cache'i temizle
            self.cache.clear()
            logger.info("Tüm API cache'i temizlendi")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Cache istatistiklerini al"""
        now = datetime.now()
        valid_entries = 0
        expired_entries = 0
        
        for entry in self.cache.values():
            if now - entry["timestamp"] < timedelta(seconds=self.cache_ttl):
                valid_entries += 1
            else:
                expired_entries += 1
        
        return {
            "total_entries": len(self.cache),
            "valid_entries": valid_entries,
            "expired_entries": expired_entries,
            "cache_ttl": self.cache_ttl
        }
    
    async def __aenter__(self):
        """Async context manager giriş"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager çıkış"""
        await self.shutdown()
