"""
Entegrasyon yöneticisi modülü
"""

import asyncio
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta

from ..utils.logger import get_logger
from ..utils.exceptions import LLMVisionError

logger = get_logger(__name__)


class IntegrationManager:
    """Entegrasyon yöneticisi sınıfı"""
    
    def __init__(self):
        # Bileşen referansları
        self.vision_processor = None
        self.context_engine = None
        self.data_collector = None
        self.mcp_transport = None
        self.event_dispatcher = None
        
        # Entegrasyon durumu
        self.is_initialized = False
        self.integration_tasks: List[asyncio.Task] = []
        
        # Callback'ler
        self.data_callbacks: List[Callable] = []
        self.context_callbacks: List[Callable] = []
        
        logger.info("Entegrasyon yöneticisi oluşturuldu")
    
    async def initialize(self, 
                        vision_processor=None,
                        context_engine=None,
                        data_collector=None,
                        mcp_transport=None,
                        event_dispatcher=None) -> None:
        """Entegrasyon yöneticisini başlat"""
        try:
            # Bileşenleri kaydet
            self.vision_processor = vision_processor
            self.context_engine = context_engine
            self.data_collector = data_collector
            self.mcp_transport = mcp_transport
            self.event_dispatcher = event_dispatcher
            
            # Entegrasyonları kur
            await self._setup_integrations()
            
            # Periyodik görevleri başlat
            await self._start_periodic_tasks()
            
            self.is_initialized = True
            logger.info("Entegrasyon yöneticisi başlatıldı")
            
        except Exception as e:
            logger.error(f"Entegrasyon yöneticisi başlatma hatası: {e}")
            raise LLMVisionError(f"Entegrasyon yöneticisi başlatma hatası: {str(e)}")
    
    async def _setup_integrations(self) -> None:
        """Entegrasyonları kur"""
        try:
            # Data collector -> Context engine entegrasyonu
            if self.data_collector and self.context_engine:
                self.data_collector.add_data_callback(self._handle_new_data)
                logger.debug("Data collector -> Context engine entegrasyonu kuruldu")
            
            # Context engine -> MCP transport entegrasyonu
            if self.context_engine and self.mcp_transport:
                self.context_engine.add_context_callback(self._handle_new_context)
                logger.debug("Context engine -> MCP transport entegrasyonu kuruldu")
            
            # Vision processor -> Data collector entegrasyonu
            if self.vision_processor and self.data_collector:
                # Vision processor'dan gelen verileri data collector'a yönlendir
                logger.debug("Vision processor -> Data collector entegrasyonu kuruldu")
            
            logger.info("Tüm entegrasyonlar kuruldu")
            
        except Exception as e:
            logger.error(f"Entegrasyon kurma hatası: {e}")
            raise
    
    async def _start_periodic_tasks(self) -> None:
        """Periyodik görevleri başlat"""
        try:
            # Periyodik bağlam güncelleme
            context_task = asyncio.create_task(self._periodic_context_update())
            self.integration_tasks.append(context_task)
            
            # Periyodik sistem durumu kontrolü
            health_task = asyncio.create_task(self._periodic_health_check())
            self.integration_tasks.append(health_task)
            
            # Periyodik temizlik
            cleanup_task = asyncio.create_task(self._periodic_cleanup())
            self.integration_tasks.append(cleanup_task)
            
            logger.info("Periyodik görevler başlatıldı")
            
        except Exception as e:
            logger.error(f"Periyodik görev başlatma hatası: {e}")
    
    async def _handle_new_data(self, data_entry: Dict[str, Any]) -> None:
        """Yeni veri girişini işle"""
        try:
            data_type = data_entry.get("type", "unknown")
            
            # Veri türüne göre işlem yap
            if data_type == "file_change":
                await self._process_file_change(data_entry)
            elif data_type == "vision":
                await self._process_vision_data(data_entry)
            elif data_type == "system_info":
                await self._process_system_data(data_entry)
            
            # Event dispatcher'a bildir
            if self.event_dispatcher:
                await self.event_dispatcher.dispatch_event(
                    "data.processed",
                    {"data_type": data_type, "timestamp": datetime.now().isoformat()}
                )
            
            logger.debug(f"Yeni veri işlendi: {data_type}")
            
        except Exception as e:
            logger.error(f"Veri işleme hatası: {e}")
    
    async def _process_file_change(self, data_entry: Dict[str, Any]) -> None:
        """Dosya değişiklik verisini işle"""
        try:
            file_data = data_entry.get("data", {})
            file_path = file_data.get("file_path")
            
            if file_path and self.context_engine:
                # Dosya analizi yap
                from ..filesystem.file_analyzer import FileAnalyzer
                analyzer = FileAnalyzer()
                analysis = await analyzer.analyze_file(file_path)
                
                # Context engine ile analiz et
                await self.context_engine.analyze_file_data(analysis)
                
                logger.debug(f"Dosya değişikliği işlendi: {file_path}")
            
        except Exception as e:
            logger.error(f"Dosya değişiklik işleme hatası: {e}")
    
    async def _process_vision_data(self, data_entry: Dict[str, Any]) -> None:
        """Vision verisini işle"""
        try:
            vision_data = data_entry.get("data", {})
            
            if vision_data and self.context_engine:
                # Context engine ile analiz et
                await self.context_engine.analyze_vision_data(vision_data)
                
                logger.debug("Vision verisi işlendi")
            
        except Exception as e:
            logger.error(f"Vision veri işleme hatası: {e}")
    
    async def _process_system_data(self, data_entry: Dict[str, Any]) -> None:
        """Sistem verisini işle"""
        try:
            system_data = data_entry.get("data", {})
            
            if system_data and self.context_engine:
                # Context engine ile analiz et
                await self.context_engine.analyze_system_data(system_data)
                
                logger.debug("Sistem verisi işlendi")
            
        except Exception as e:
            logger.error(f"Sistem veri işleme hatası: {e}")
    
    async def _handle_new_context(self, context_data: Dict[str, Any]) -> None:
        """Yeni bağlam verisini işle"""
        try:
            context_type = context_data.get("data_type", "unknown")
            
            # MCP istemcilerine bildirim gönder
            if self.mcp_transport:
                await self.mcp_transport.notification_manager.broadcast_resource_changed(
                    "context://recent", "updated"
                )
            
            # Event dispatcher'a bildir
            if self.event_dispatcher:
                await self.event_dispatcher.dispatch_event(
                    "context.updated",
                    {"context_type": context_type, "timestamp": datetime.now().isoformat()}
                )
            
            logger.debug(f"Yeni bağlam işlendi: {context_type}")
            
        except Exception as e:
            logger.error(f"Bağlam işleme hatası: {e}")
    
    async def _periodic_context_update(self) -> None:
        """Periyodik bağlam güncelleme"""
        try:
            while self.is_initialized:
                try:
                    # Son verileri al ve kapsamlı bağlam oluştur
                    if self.data_collector and self.context_engine:
                        recent_data = self.data_collector.get_collected_data(limit=50)
                        
                        if recent_data:
                            await self.context_engine.create_comprehensive_context(recent_data)
                            logger.debug("Periyodik bağlam güncellendi")
                    
                    # 5 dakika bekle
                    await asyncio.sleep(300)
                    
                except Exception as e:
                    logger.error(f"Periyodik bağlam güncelleme hatası: {e}")
                    await asyncio.sleep(60)  # Hata durumunda 1 dakika bekle
                    
        except asyncio.CancelledError:
            logger.debug("Periyodik bağlam güncelleme görevi iptal edildi")
    
    async def _periodic_health_check(self) -> None:
        """Periyodik sistem sağlık kontrolü"""
        try:
            while self.is_initialized:
                try:
                    # Bileşen sağlık kontrolü
                    health_status = await self._check_component_health()
                    
                    # Sorunlu bileşenler varsa event gönder
                    if health_status.get("unhealthy_components"):
                        if self.event_dispatcher:
                            await self.event_dispatcher.dispatch_event(
                                "system.health_warning",
                                health_status
                            )
                    
                    # 2 dakika bekle
                    await asyncio.sleep(120)
                    
                except Exception as e:
                    logger.error(f"Periyodik sağlık kontrolü hatası: {e}")
                    await asyncio.sleep(60)
                    
        except asyncio.CancelledError:
            logger.debug("Periyodik sağlık kontrolü görevi iptal edildi")
    
    async def _periodic_cleanup(self) -> None:
        """Periyodik temizlik"""
        try:
            while self.is_initialized:
                try:
                    # MCP transport temizliği
                    if self.mcp_transport:
                        cleaned = self.mcp_transport.client_manager.cleanup_inactive_clients()
                        if cleaned > 0:
                            logger.info(f"Periyodik temizlik: {cleaned} aktif olmayan istemci temizlendi")
                    
                    # Context engine cache temizliği
                    if self.context_engine:
                        self.context_engine.clear_cache()
                        logger.debug("Context engine cache temizlendi")
                    
                    # 30 dakika bekle
                    await asyncio.sleep(1800)
                    
                except Exception as e:
                    logger.error(f"Periyodik temizlik hatası: {e}")
                    await asyncio.sleep(300)  # Hata durumunda 5 dakika bekle
                    
        except asyncio.CancelledError:
            logger.debug("Periyodik temizlik görevi iptal edildi")
    
    async def _check_component_health(self) -> Dict[str, Any]:
        """Bileşen sağlığını kontrol et"""
        try:
            health_status = {
                "timestamp": datetime.now().isoformat(),
                "healthy_components": [],
                "unhealthy_components": []
            }
            
            # Vision processor kontrolü
            if self.vision_processor:
                if hasattr(self.vision_processor, 'is_initialized') and self.vision_processor.is_initialized:
                    health_status["healthy_components"].append("vision_processor")
                else:
                    health_status["unhealthy_components"].append("vision_processor")
            
            # Context engine kontrolü
            if self.context_engine:
                if hasattr(self.context_engine, 'is_initialized') and self.context_engine.is_initialized:
                    health_status["healthy_components"].append("context_engine")
                else:
                    health_status["unhealthy_components"].append("context_engine")
            
            # Data collector kontrolü
            if self.data_collector:
                if hasattr(self.data_collector, 'is_collecting') and self.data_collector.is_collecting:
                    health_status["healthy_components"].append("data_collector")
                else:
                    health_status["unhealthy_components"].append("data_collector")
            
            # MCP transport kontrolü
            if self.mcp_transport:
                if hasattr(self.mcp_transport, 'is_initialized') and self.mcp_transport.is_initialized:
                    health_status["healthy_components"].append("mcp_transport")
                else:
                    health_status["unhealthy_components"].append("mcp_transport")
            
            return health_status
            
        except Exception as e:
            logger.error(f"Bileşen sağlık kontrolü hatası: {e}")
            return {"error": str(e)}
    
    async def shutdown(self) -> None:
        """Entegrasyon yöneticisini kapat"""
        try:
            self.is_initialized = False
            
            # Periyodik görevleri iptal et
            for task in self.integration_tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
            
            self.integration_tasks.clear()
            
            logger.info("Entegrasyon yöneticisi kapatıldı")
            
        except Exception as e:
            logger.error(f"Entegrasyon yöneticisi kapatma hatası: {e}")
    
    def get_integration_status(self) -> Dict[str, Any]:
        """Entegrasyon durumunu al"""
        try:
            return {
                "is_initialized": self.is_initialized,
                "active_tasks": len([t for t in self.integration_tasks if not t.done()]),
                "total_tasks": len(self.integration_tasks),
                "components": {
                    "vision_processor": self.vision_processor is not None,
                    "context_engine": self.context_engine is not None,
                    "data_collector": self.data_collector is not None,
                    "mcp_transport": self.mcp_transport is not None,
                    "event_dispatcher": self.event_dispatcher is not None
                }
            }
            
        except Exception as e:
            logger.error(f"Entegrasyon durumu alma hatası: {e}")
            return {"error": str(e)}
