# LLM Göz <PERSON> (LLM Vision System)

Bu proje, b<PERSON><PERSON><PERSON><PERSON> dil modellerine (LLM) sanal ve fiziksel dünyadan gerçek zamanlı veri sağlayarak "göz" yeteneği kazandıran bir sistemdir. Model Context Protocol (MCP) üzerinden çalışır.

## Mimari

- **Ana LLM**: <PERSON>op gibi istemciler
- **MCP Sunucusu**: Veri toplama ve koordinasyon
- **Context Engine**: Ham verileri analiz eden ikinci LLM
- **Transport Katmanı**: Güvenli iletişim

## Özellikler

- 🎥 Gerçek zamanlı kamera verisi
- 📁 Dosya sistemi erişimi
- 🤖 AI destekli bağlam analizi
- 🔒 Güvenli veri iletimi
- ⚡ Düşük gecikme optimizasyonu

## Kurulum

```bash
# Sanal ortam oluştur
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Bağımlılıkları yükle
pip install -r requirements.txt

# Geliştirme bağımlılıkları
pip install -r requirements-dev.txt
```

## Kullanım

```bash
# MCP sunucusunu başlat
python -m llm_vision.server

# Test et
python -m llm_vision.test
```

## Geliştirme

```bash
# Testleri çalıştır
pytest

# Kod kalitesi kontrolü
black .
flake8 .
mypy .
```

## Lisans

MIT License
