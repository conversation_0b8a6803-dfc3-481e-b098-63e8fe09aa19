"""
OCR (Optical Character Recognition) modülü
"""

import cv2
import numpy as np
import pytesseract
from typing import List, Dict, Tuple, Optional
import asyncio
import re

from ..config import config
from ..utils.logger import get_logger
from ..utils.exceptions import OCRError

logger = get_logger(__name__)


class OCRResult:
    """OCR sonuç sınıfı"""
    
    def __init__(self, 
                 text: str, 
                 confidence: float, 
                 bbox: Tuple[int, int, int, int] = None,
                 word_level: bool = False):
        self.text = text.strip()
        self.confidence = confidence
        self.bbox = bbox  # (x, y, width, height)
        self.word_level = word_level
    
    def to_dict(self) -> dict:
        """Sözlük formatına çevir"""
        return {
            "text": self.text,
            "confidence": self.confidence,
            "bbox": self.bbox,
            "word_level": self.word_level,
            "length": len(self.text)
        }


class OCRProcessor:
    """OCR işlemci sınıfı"""
    
    def __init__(self):
        self.tesseract_path = config.ocr.tesseract_path
        self.languages = config.ocr.languages
        
        # Tesseract yolunu ayarla (Windows için)
        if self.tesseract_path:
            pytesseract.pytesseract.tesseract_cmd = self.tesseract_path
        
        logger.info(f"OCR işlemci başlatıldı - Diller: {self.languages}")
    
    def preprocess_image(self, 
                        image: np.ndarray, 
                        method: str = "adaptive") -> np.ndarray:
        """Görüntüyü OCR için ön işle"""
        try:
            # Gri tonlamaya çevir
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()
            
            if method == "adaptive":
                # Adaptive threshold
                processed = cv2.adaptiveThreshold(
                    gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                    cv2.THRESH_BINARY, 11, 2
                )
            elif method == "otsu":
                # Otsu threshold
                _, processed = cv2.threshold(
                    gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU
                )
            elif method == "simple":
                # Basit threshold
                _, processed = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
            elif method == "denoise":
                # Gürültü azaltma
                processed = cv2.fastNlMeansDenoising(gray)
            elif method == "morph":
                # Morfolojik işlemler
                kernel = np.ones((2, 2), np.uint8)
                processed = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)
            else:
                processed = gray
            
            return processed
            
        except Exception as e:
            logger.error(f"Görüntü ön işleme hatası: {e}")
            return image
    
    def extract_text_simple(self, 
                           image: np.ndarray, 
                           language: str = "eng",
                           preprocess_method: str = "adaptive") -> OCRResult:
        """Basit metin çıkarma"""
        try:
            # Görüntüyü ön işle
            processed_image = self.preprocess_image(image, preprocess_method)
            
            # Tesseract konfigürasyonu
            config_str = f"--oem 3 --psm 6"
            
            # Metin çıkar
            text = pytesseract.image_to_string(
                processed_image, 
                lang=language, 
                config=config_str
            )
            
            # Güven değerini al (ortalama)
            try:
                data = pytesseract.image_to_data(
                    processed_image, 
                    lang=language, 
                    config=config_str,
                    output_type=pytesseract.Output.DICT
                )
                
                confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
                avg_confidence = sum(confidences) / len(confidences) if confidences else 0
                avg_confidence = avg_confidence / 100.0  # 0-1 aralığına çevir
                
            except Exception:
                avg_confidence = 0.5  # Varsayılan değer
            
            return OCRResult(
                text=text,
                confidence=avg_confidence
            )
            
        except Exception as e:
            logger.error(f"Basit OCR hatası: {e}")
            raise OCRError(f"Basit OCR hatası: {str(e)}")
    
    def extract_text_detailed(self, 
                             image: np.ndarray, 
                             language: str = "eng",
                             preprocess_method: str = "adaptive") -> List[OCRResult]:
        """Detaylı metin çıkarma (kelime seviyesinde)"""
        try:
            # Görüntüyü ön işle
            processed_image = self.preprocess_image(image, preprocess_method)
            
            # Tesseract konfigürasyonu
            config_str = f"--oem 3 --psm 6"
            
            # Detaylı veri al
            data = pytesseract.image_to_data(
                processed_image, 
                lang=language, 
                config=config_str,
                output_type=pytesseract.Output.DICT
            )
            
            results = []
            n_boxes = len(data['text'])
            
            for i in range(n_boxes):
                text = data['text'][i].strip()
                conf = int(data['conf'][i])
                
                if text and conf > 0:
                    x = data['left'][i]
                    y = data['top'][i]
                    w = data['width'][i]
                    h = data['height'][i]
                    
                    results.append(OCRResult(
                        text=text,
                        confidence=conf / 100.0,  # 0-1 aralığına çevir
                        bbox=(x, y, w, h),
                        word_level=True
                    ))
            
            logger.info(f"Detaylı OCR ile {len(results)} kelime tanındı")
            return results
            
        except Exception as e:
            logger.error(f"Detaylı OCR hatası: {e}")
            raise OCRError(f"Detaylı OCR hatası: {str(e)}")
    
    def extract_text(self, 
                    image: np.ndarray, 
                    language: str = "eng",
                    detailed: bool = False,
                    preprocess_method: str = "adaptive") -> List[OCRResult]:
        """Metin çıkarma (basit veya detaylı)"""
        if detailed:
            return self.extract_text_detailed(image, language, preprocess_method)
        else:
            simple_result = self.extract_text_simple(image, language, preprocess_method)
            return [simple_result] if simple_result.text else []
    
    async def extract_text_async(self, 
                                image: np.ndarray, 
                                language: str = "eng",
                                detailed: bool = False,
                                preprocess_method: str = "adaptive") -> List[OCRResult]:
        """Async metin çıkarma"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.extract_text, image, language, detailed, preprocess_method
        )
    
    def find_text_patterns(self, 
                          ocr_results: List[OCRResult], 
                          patterns: Dict[str, str]) -> Dict[str, List[OCRResult]]:
        """Metin desenlerini bul"""
        matches = {pattern_name: [] for pattern_name in patterns.keys()}
        
        for result in ocr_results:
            text = result.text
            
            for pattern_name, pattern in patterns.items():
                if re.search(pattern, text, re.IGNORECASE):
                    matches[pattern_name].append(result)
        
        return matches
    
    def get_common_patterns(self) -> Dict[str, str]:
        """Yaygın metin desenlerini al"""
        return {
            "email": r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            "phone": r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b',
            "url": r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+',
            "date": r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b',
            "time": r'\b\d{1,2}:\d{2}(?::\d{2})?\s?(?:AM|PM)?\b',
            "number": r'\b\d+(?:\.\d+)?\b',
            "currency": r'\$\d+(?:\.\d{2})?|\d+(?:\.\d{2})?\s?(?:TL|USD|EUR)',
            "turkish_id": r'\b\d{11}\b',  # TC Kimlik No
            "credit_card": r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b'
        }
    
    def draw_ocr_results(self, 
                        image: np.ndarray, 
                        ocr_results: List[OCRResult],
                        draw_confidence: bool = True) -> np.ndarray:
        """OCR sonuçlarını görüntü üzerine çiz"""
        result_image = image.copy()
        
        for result in ocr_results:
            if result.bbox:
                x, y, w, h = result.bbox
                
                # Bounding box çiz
                cv2.rectangle(result_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
                
                # Metin etiketi
                if draw_confidence:
                    label = f"{result.text[:20]}... ({result.confidence:.2f})"
                else:
                    label = result.text[:20] + "..." if len(result.text) > 20 else result.text
                
                # Etiket arka planı
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
                cv2.rectangle(result_image, (x, y - label_size[1] - 10), 
                             (x + label_size[0], y), (0, 255, 0), -1)
                
                # Etiket metni
                cv2.putText(result_image, label, (x, y - 5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
        
        return result_image
    
    def get_ocr_summary(self, ocr_results: List[OCRResult]) -> dict:
        """OCR özetini al"""
        if not ocr_results:
            return {
                "total_text_blocks": 0,
                "total_characters": 0,
                "average_confidence": 0.0,
                "full_text": "",
                "patterns": {}
            }
        
        # Temel istatistikler
        total_chars = sum(len(result.text) for result in ocr_results)
        avg_confidence = sum(result.confidence for result in ocr_results) / len(ocr_results)
        full_text = " ".join(result.text for result in ocr_results)
        
        # Desen analizi
        patterns = self.get_common_patterns()
        pattern_matches = self.find_text_patterns(ocr_results, patterns)
        pattern_summary = {
            name: len(matches) for name, matches in pattern_matches.items()
        }
        
        return {
            "total_text_blocks": len(ocr_results),
            "total_characters": total_chars,
            "average_confidence": avg_confidence,
            "full_text": full_text,
            "patterns": pattern_summary,
            "text_blocks": [result.to_dict() for result in ocr_results]
        }
    
    def is_available(self) -> bool:
        """Tesseract kullanılabilir mi kontrol et"""
        try:
            # Basit bir test görüntüsü oluştur
            test_image = np.ones((100, 100), dtype=np.uint8) * 255
            cv2.putText(test_image, "TEST", (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, 0, 2)
            
            # OCR test et
            text = pytesseract.image_to_string(test_image)
            return "TEST" in text.upper()
            
        except Exception as e:
            logger.error(f"Tesseract kullanılabilirlik testi hatası: {e}")
            return False
