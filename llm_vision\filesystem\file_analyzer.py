"""
Dosya analizi modülü
"""

import os
import asyncio
import mimetypes
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pathlib import Path
import hashlib
import json
import re

from ..utils.logger import get_logger
from ..utils.exceptions import FileSystemError

logger = get_logger(__name__)


class FileAnalyzer:
    """Dosya analizi sınıfı"""
    
    def __init__(self):
        self.supported_text_extensions = {
            '.txt', '.md', '.py', '.js', '.html', '.css', '.json', '.xml', 
            '.yaml', '.yml', '.ini', '.cfg', '.conf', '.log', '.sql',
            '.sh', '.bat', '.ps1', '.php', '.rb', '.go', '.rs', '.cpp',
            '.c', '.h', '.java', '.kt', '.swift', '.dart', '.ts', '.jsx',
            '.tsx', '.vue', '.svelte', '.scss', '.sass', '.less'
        }
        
        self.code_extensions = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.jsx': 'javascript',
            '.tsx': 'typescript',
            '.html': 'html',
            '.css': 'css',
            '.scss': 'scss',
            '.sass': 'sass',
            '.php': 'php',
            '.rb': 'ruby',
            '.go': 'go',
            '.rs': 'rust',
            '.cpp': 'cpp',
            '.c': 'c',
            '.java': 'java',
            '.kt': 'kotlin',
            '.swift': 'swift',
            '.dart': 'dart'
        }
        
        logger.info("Dosya analizi modülü başlatıldı")
    
    async def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """Dosyayı analiz et"""
        try:
            if not os.path.exists(file_path):
                raise FileSystemError(f"Dosya bulunamadı: {file_path}")
            
            # Temel bilgileri al
            basic_info = await self._get_basic_info(file_path)
            
            # Dosya tipine göre detaylı analiz
            analysis = {
                "basic_info": basic_info,
                "content_analysis": {},
                "metadata": {},
                "security_info": {}
            }
            
            # İçerik analizi
            if basic_info["is_text_file"]:
                analysis["content_analysis"] = await self._analyze_text_content(file_path)
            elif basic_info["is_image"]:
                analysis["content_analysis"] = await self._analyze_image_content(file_path)
            elif basic_info["is_code"]:
                analysis["content_analysis"] = await self._analyze_code_content(file_path)
            
            # Güvenlik analizi
            analysis["security_info"] = await self._analyze_security(file_path)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Dosya analizi hatası: {e}")
            raise FileSystemError(f"Dosya analizi hatası: {str(e)}")
    
    async def _get_basic_info(self, file_path: str) -> Dict[str, Any]:
        """Temel dosya bilgilerini al"""
        try:
            stat = os.stat(file_path)
            path_obj = Path(file_path)
            mime_type, encoding = mimetypes.guess_type(file_path)
            
            extension = path_obj.suffix.lower()
            
            # Dosya hash'i
            file_hash = await self._calculate_file_hash(file_path)
            
            return {
                "path": file_path,
                "name": path_obj.name,
                "stem": path_obj.stem,
                "extension": extension,
                "size": stat.st_size,
                "size_human": self._format_size(stat.st_size),
                "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "created_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "accessed_time": datetime.fromtimestamp(stat.st_atime).isoformat(),
                "mime_type": mime_type,
                "encoding": encoding,
                "is_directory": os.path.isdir(file_path),
                "is_text_file": extension in self.supported_text_extensions,
                "is_code": extension in self.code_extensions,
                "is_image": mime_type and mime_type.startswith('image/') if mime_type else False,
                "is_hidden": path_obj.name.startswith('.'),
                "permissions": oct(stat.st_mode)[-3:],
                "hash_md5": file_hash,
                "language": self.code_extensions.get(extension)
            }
            
        except Exception as e:
            logger.error(f"Temel bilgi alma hatası: {e}")
            return {}
    
    async def _calculate_file_hash(self, file_path: str) -> Optional[str]:
        """Dosya hash'ini hesapla"""
        try:
            # Büyük dosyalar için hash hesaplama sınırı
            max_size = 50 * 1024 * 1024  # 50MB
            
            file_size = os.path.getsize(file_path)
            if file_size > max_size:
                return None
            
            def _hash_file():
                hash_md5 = hashlib.md5()
                with open(file_path, "rb") as f:
                    for chunk in iter(lambda: f.read(4096), b""):
                        hash_md5.update(chunk)
                return hash_md5.hexdigest()
            
            return await asyncio.get_event_loop().run_in_executor(None, _hash_file)
            
        except Exception as e:
            logger.error(f"Hash hesaplama hatası: {e}")
            return None
    
    async def _analyze_text_content(self, file_path: str) -> Dict[str, Any]:
        """Metin içeriği analiz et"""
        try:
            def _analyze():
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                lines = content.split('\n')
                words = content.split()
                
                # Temel istatistikler
                stats = {
                    "line_count": len(lines),
                    "word_count": len(words),
                    "character_count": len(content),
                    "character_count_no_spaces": len(content.replace(' ', '')),
                    "empty_lines": sum(1 for line in lines if not line.strip()),
                    "max_line_length": max(len(line) for line in lines) if lines else 0,
                    "avg_line_length": sum(len(line) for line in lines) / len(lines) if lines else 0
                }
                
                # Dil analizi
                language_hints = self._detect_language_hints(content)
                
                # Özel desenler
                patterns = self._find_text_patterns(content)
                
                return {
                    "stats": stats,
                    "language_hints": language_hints,
                    "patterns": patterns,
                    "preview": content[:500] + "..." if len(content) > 500 else content
                }
            
            return await asyncio.get_event_loop().run_in_executor(None, _analyze)
            
        except Exception as e:
            logger.error(f"Metin analizi hatası: {e}")
            return {}
    
    async def _analyze_code_content(self, file_path: str) -> Dict[str, Any]:
        """Kod içeriği analiz et"""
        try:
            def _analyze():
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                lines = content.split('\n')
                
                # Kod istatistikleri
                code_lines = 0
                comment_lines = 0
                blank_lines = 0
                
                for line in lines:
                    stripped = line.strip()
                    if not stripped:
                        blank_lines += 1
                    elif stripped.startswith('#') or stripped.startswith('//') or stripped.startswith('/*'):
                        comment_lines += 1
                    else:
                        code_lines += 1
                
                # Fonksiyon/sınıf sayısı (basit regex ile)
                functions = len(re.findall(r'def\s+\w+|function\s+\w+|func\s+\w+', content, re.IGNORECASE))
                classes = len(re.findall(r'class\s+\w+|struct\s+\w+', content, re.IGNORECASE))
                
                # Import/include sayısı
                imports = len(re.findall(r'import\s+|from\s+\w+\s+import|#include|require\(', content, re.IGNORECASE))
                
                return {
                    "total_lines": len(lines),
                    "code_lines": code_lines,
                    "comment_lines": comment_lines,
                    "blank_lines": blank_lines,
                    "functions": functions,
                    "classes": classes,
                    "imports": imports,
                    "complexity_estimate": code_lines + functions * 2 + classes * 3,
                    "preview": content[:500] + "..." if len(content) > 500 else content
                }
            
            return await asyncio.get_event_loop().run_in_executor(None, _analyze)
            
        except Exception as e:
            logger.error(f"Kod analizi hatası: {e}")
            return {}
    
    async def _analyze_image_content(self, file_path: str) -> Dict[str, Any]:
        """Görüntü içeriği analiz et"""
        try:
            # Bu fonksiyon vision modülü ile entegre edilebilir
            return {
                "note": "Görüntü analizi vision modülü ile entegre edilecek",
                "file_path": file_path
            }
            
        except Exception as e:
            logger.error(f"Görüntü analizi hatası: {e}")
            return {}
    
    async def _analyze_security(self, file_path: str) -> Dict[str, Any]:
        """Güvenlik analizi"""
        try:
            path_obj = Path(file_path)
            
            security_info = {
                "is_executable": os.access(file_path, os.X_OK),
                "is_readable": os.access(file_path, os.R_OK),
                "is_writable": os.access(file_path, os.W_OK),
                "suspicious_extension": path_obj.suffix.lower() in {'.exe', '.bat', '.sh', '.ps1', '.scr'},
                "hidden_file": path_obj.name.startswith('.'),
                "potential_risks": []
            }
            
            # Potansiyel riskler
            if security_info["suspicious_extension"]:
                security_info["potential_risks"].append("Executable file type")
            
            if security_info["is_executable"] and not security_info["suspicious_extension"]:
                security_info["potential_risks"].append("Unexpected executable permissions")
            
            # Dosya içeriği güvenlik kontrolü (metin dosyaları için)
            if path_obj.suffix.lower() in self.supported_text_extensions:
                content_risks = await self._check_content_security(file_path)
                security_info["potential_risks"].extend(content_risks)
            
            return security_info
            
        except Exception as e:
            logger.error(f"Güvenlik analizi hatası: {e}")
            return {}
    
    async def _check_content_security(self, file_path: str) -> List[str]:
        """İçerik güvenlik kontrolü"""
        try:
            def _check():
                risks = []
                
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read().lower()
                
                # Şüpheli anahtar kelimeler
                suspicious_patterns = [
                    r'password\s*=\s*["\'][^"\']+["\']',
                    r'api[_-]?key\s*=\s*["\'][^"\']+["\']',
                    r'secret\s*=\s*["\'][^"\']+["\']',
                    r'token\s*=\s*["\'][^"\']+["\']',
                    r'eval\s*\(',
                    r'exec\s*\(',
                    r'system\s*\(',
                    r'shell_exec\s*\(',
                ]
                
                for pattern in suspicious_patterns:
                    if re.search(pattern, content, re.IGNORECASE):
                        risks.append(f"Suspicious pattern: {pattern}")
                
                return risks
            
            return await asyncio.get_event_loop().run_in_executor(None, _check)
            
        except Exception as e:
            logger.error(f"İçerik güvenlik kontrolü hatası: {e}")
            return []
    
    def _detect_language_hints(self, content: str) -> Dict[str, int]:
        """Dil ipuçlarını tespit et"""
        hints = {}
        
        # Programlama dili ipuçları
        patterns = {
            'python': [r'def\s+\w+', r'import\s+\w+', r'from\s+\w+\s+import', r'if\s+__name__\s*=='],
            'javascript': [r'function\s+\w+', r'var\s+\w+', r'let\s+\w+', r'const\s+\w+', r'=>'],
            'java': [r'public\s+class', r'private\s+\w+', r'public\s+static\s+void\s+main'],
            'html': [r'<html>', r'<div>', r'<p>', r'<script>'],
            'css': [r'\w+\s*{', r':\s*\w+;', r'@media'],
            'sql': [r'SELECT\s+', r'FROM\s+', r'WHERE\s+', r'INSERT\s+INTO'],
        }
        
        for lang, lang_patterns in patterns.items():
            count = 0
            for pattern in lang_patterns:
                count += len(re.findall(pattern, content, re.IGNORECASE))
            if count > 0:
                hints[lang] = count
        
        return hints
    
    def _find_text_patterns(self, content: str) -> Dict[str, int]:
        """Metin desenlerini bul"""
        patterns = {
            'emails': len(re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', content)),
            'urls': len(re.findall(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', content)),
            'phone_numbers': len(re.findall(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b', content)),
            'dates': len(re.findall(r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b', content)),
            'ip_addresses': len(re.findall(r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b', content)),
            'credit_cards': len(re.findall(r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b', content)),
        }
        
        return {k: v for k, v in patterns.items() if v > 0}
    
    def _format_size(self, size_bytes: int) -> str:
        """Dosya boyutunu okunabilir formata çevir"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    async def analyze_directory(self, 
                              directory_path: str,
                              recursive: bool = True,
                              max_files: int = 1000) -> Dict[str, Any]:
        """Dizini analiz et"""
        try:
            if not os.path.exists(directory_path):
                raise FileSystemError(f"Dizin bulunamadı: {directory_path}")
            
            files_analyzed = 0
            total_size = 0
            file_types = {}
            languages = {}
            
            analysis = {
                "directory": directory_path,
                "total_files": 0,
                "total_size": 0,
                "total_size_human": "",
                "file_types": {},
                "languages": {},
                "largest_files": [],
                "newest_files": [],
                "analysis_summary": {}
            }
            
            # Dosyaları tara
            if recursive:
                for root, dirs, files in os.walk(directory_path):
                    for file in files:
                        if files_analyzed >= max_files:
                            break
                        
                        file_path = os.path.join(root, file)
                        await self._process_file_for_directory_analysis(
                            file_path, analysis
                        )
                        files_analyzed += 1
            else:
                for item in os.listdir(directory_path):
                    if files_analyzed >= max_files:
                        break
                    
                    item_path = os.path.join(directory_path, item)
                    if os.path.isfile(item_path):
                        await self._process_file_for_directory_analysis(
                            item_path, analysis
                        )
                        files_analyzed += 1
            
            # Sonuçları düzenle
            analysis["total_files"] = files_analyzed
            analysis["total_size_human"] = self._format_size(analysis["total_size"])
            
            return analysis
            
        except Exception as e:
            logger.error(f"Dizin analizi hatası: {e}")
            raise FileSystemError(f"Dizin analizi hatası: {str(e)}")
    
    async def _process_file_for_directory_analysis(self, 
                                                  file_path: str, 
                                                  analysis: Dict[str, Any]) -> None:
        """Dizin analizi için dosyayı işle"""
        try:
            basic_info = await self._get_basic_info(file_path)
            
            # Toplam boyut
            analysis["total_size"] += basic_info.get("size", 0)
            
            # Dosya tipi
            ext = basic_info.get("extension", "")
            if ext:
                analysis["file_types"][ext] = analysis["file_types"].get(ext, 0) + 1
            
            # Programlama dili
            lang = basic_info.get("language")
            if lang:
                analysis["languages"][lang] = analysis["languages"].get(lang, 0) + 1
            
            # En büyük dosyalar
            if len(analysis["largest_files"]) < 10:
                analysis["largest_files"].append({
                    "path": file_path,
                    "size": basic_info.get("size", 0),
                    "size_human": basic_info.get("size_human", "")
                })
            else:
                # En küçük dosyayı bul ve değiştir
                min_file = min(analysis["largest_files"], key=lambda x: x["size"])
                if basic_info.get("size", 0) > min_file["size"]:
                    analysis["largest_files"].remove(min_file)
                    analysis["largest_files"].append({
                        "path": file_path,
                        "size": basic_info.get("size", 0),
                        "size_human": basic_info.get("size_human", "")
                    })
            
            # En yeni dosyalar
            if len(analysis["newest_files"]) < 10:
                analysis["newest_files"].append({
                    "path": file_path,
                    "modified_time": basic_info.get("modified_time", "")
                })
            else:
                # En eski dosyayı bul ve değiştir
                oldest_file = min(analysis["newest_files"], key=lambda x: x["modified_time"])
                if basic_info.get("modified_time", "") > oldest_file["modified_time"]:
                    analysis["newest_files"].remove(oldest_file)
                    analysis["newest_files"].append({
                        "path": file_path,
                        "modified_time": basic_info.get("modified_time", "")
                    })
            
        except Exception as e:
            logger.error(f"Dosya işleme hatası: {e}")
