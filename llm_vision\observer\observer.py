"""
Event-driven observer loop sistemi
"""

import asyncio
import json
import websockets
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum

from ..config import config
from ..utils.logger import get_logger
from ..utils.exceptions import LLMVisionError
from .debouncer import EventDebouncer
from .event_processor import EventProcessor

logger = get_logger(__name__)


class EventType(Enum):
    """Event tipleri"""
    VISION_CHANGE = "vision_change"
    FILE_CHANGE = "file_change"
    WINDOW_CHANGE = "window_change"
    BROWSER_CHANGE = "browser_change"
    SYSTEM_CHANGE = "system_change"
    PLUGIN_EVENT = "plugin_event"


@dataclass
class ObserverEvent:
    """Observer event yapısı"""
    type: EventType
    source: str
    data: Dict[str, Any]
    timestamp: datetime
    priority: str = "medium"
    session_id: Optional[str] = None
    user_id: Optional[str] = None


class ObserverLoop:
    """Event-driven observer loop ana sınıfı"""
    
    def __init__(self):
        self.is_running = False
        self.websocket_uri = f"ws://{config.server.host}:{config.server.port}/ws"
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        
        # Event işleme bileşenleri
        self.debouncer = EventDebouncer()
        self.event_processor = EventProcessor()
        
        # Event handler'lar
        self.event_handlers: Dict[EventType, List[Callable]] = {
            event_type: [] for event_type in EventType
        }
        
        # Performans metrikleri
        self.metrics = {
            "events_received": 0,
            "events_processed": 0,
            "events_debounced": 0,
            "errors_occurred": 0,
            "average_processing_time": 0.0
        }
        
        # Fallback mode
        self.fallback_mode = False
        self.fallback_interval = 5.0  # saniye
        
        logger.info("Observer loop oluşturuldu")
    
    async def initialize(self) -> None:
        """Observer loop'u başlat"""
        try:
            logger.info("Observer loop başlatılıyor...")
            
            # Event processor'ı başlat
            await self.event_processor.initialize()
            
            # Debouncer'ı başlat
            await self.debouncer.initialize()
            
            # Default event handler'ları kaydet
            self._register_default_handlers()
            
            logger.info("Observer loop başarıyla başlatıldı")
            
        except Exception as e:
            logger.error(f"Observer loop başlatma hatası: {e}")
            raise LLMVisionError(f"Observer loop başlatma hatası: {str(e)}")
    
    async def start(self) -> None:
        """Observer loop'u çalıştır"""
        if self.is_running:
            return
        
        self.is_running = True
        logger.info("Observer loop çalışmaya başladı")
        
        # WebSocket bağlantısını başlat
        asyncio.create_task(self._websocket_loop())
        
        # Fallback mode task'ını başlat
        asyncio.create_task(self._fallback_loop())
    
    async def stop(self) -> None:
        """Observer loop'u durdur"""
        self.is_running = False
        
        if self.websocket:
            await self.websocket.close()
        
        await self.debouncer.shutdown()
        await self.event_processor.shutdown()
        
        logger.info("Observer loop durduruldu")
    
    async def _websocket_loop(self) -> None:
        """WebSocket bağlantı döngüsü"""
        while self.is_running:
            try:
                logger.info(f"WebSocket bağlantısı kuruluyor: {self.websocket_uri}")
                
                async with websockets.connect(self.websocket_uri) as websocket:
                    self.websocket = websocket
                    self.fallback_mode = False
                    logger.info("WebSocket bağlantısı kuruldu")
                    
                    async for message in websocket:
                        await self._handle_websocket_message(message)
                        
            except websockets.exceptions.ConnectionClosed:
                logger.warning("WebSocket bağlantısı kesildi, yeniden bağlanılıyor...")
                self.fallback_mode = True
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"WebSocket hatası: {e}")
                self.fallback_mode = True
                await asyncio.sleep(5)
    
    async def _handle_websocket_message(self, message: str) -> None:
        """WebSocket mesajını işle"""
        try:
            data = json.loads(message)
            
            # Event oluştur
            event = ObserverEvent(
                type=EventType(data.get("type", "system_change")),
                source=data.get("source", "unknown"),
                data=data.get("data", {}),
                timestamp=datetime.now(),
                priority=data.get("priority", "medium"),
                session_id=data.get("session_id"),
                user_id=data.get("user_id")
            )
            
            # Event'i işle
            await self._process_event(event)
            
        except Exception as e:
            logger.error(f"WebSocket mesaj işleme hatası: {e}")
            self.metrics["errors_occurred"] += 1
    
    async def _process_event(self, event: ObserverEvent) -> None:
        """Event'i işle"""
        try:
            start_time = asyncio.get_event_loop().time()
            
            self.metrics["events_received"] += 1
            
            # Debouncing kontrolü
            should_process = await self.debouncer.should_process_event(event)
            if not should_process:
                self.metrics["events_debounced"] += 1
                return
            
            # Event'i işle
            await self.event_processor.process_event(event)
            
            # Handler'ları çağır
            handlers = self.event_handlers.get(event.type, [])
            for handler in handlers:
                try:
                    await handler(event)
                except Exception as e:
                    logger.error(f"Event handler hatası: {e}")
            
            # Metrikleri güncelle
            end_time = asyncio.get_event_loop().time()
            processing_time = end_time - start_time
            
            self.metrics["events_processed"] += 1
            current_avg = self.metrics["average_processing_time"]
            total_processed = self.metrics["events_processed"]
            self.metrics["average_processing_time"] = (
                (current_avg * (total_processed - 1) + processing_time) / total_processed
            )
            
            logger.debug(f"Event işlendi: {event.type.value} ({processing_time:.3f}s)")
            
        except Exception as e:
            logger.error(f"Event işleme hatası: {e}")
            self.metrics["errors_occurred"] += 1
    
    async def _fallback_loop(self) -> None:
        """Fallback mode döngüsü"""
        while self.is_running:
            if self.fallback_mode:
                try:
                    # Fallback mode'da periyodik veri toplama
                    logger.debug("Fallback mode: Veri toplama")
                    
                    # MCP server'dan veri al
                    # Bu kısım MCP client implementasyonu gerektirir
                    
                except Exception as e:
                    logger.error(f"Fallback mode hatası: {e}")
            
            await asyncio.sleep(self.fallback_interval)
    
    def register_event_handler(self, event_type: EventType, handler: Callable) -> None:
        """Event handler kaydet"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        
        self.event_handlers[event_type].append(handler)
        logger.info(f"Event handler kaydedildi: {event_type.value}")
    
    def _register_default_handlers(self) -> None:
        """Default event handler'ları kaydet"""
        
        async def vision_change_handler(event: ObserverEvent):
            """Görüntü değişikliği handler'ı"""
            logger.info(f"Görüntü değişikliği algılandı: {event.source}")
        
        async def file_change_handler(event: ObserverEvent):
            """Dosya değişikliği handler'ı"""
            logger.info(f"Dosya değişikliği algılandı: {event.data.get('filename', 'unknown')}")
        
        async def window_change_handler(event: ObserverEvent):
            """Pencere değişikliği handler'ı"""
            logger.info(f"Pencere değişikliği algılandı: {event.data.get('window_title', 'unknown')}")
        
        # Handler'ları kaydet
        self.register_event_handler(EventType.VISION_CHANGE, vision_change_handler)
        self.register_event_handler(EventType.FILE_CHANGE, file_change_handler)
        self.register_event_handler(EventType.WINDOW_CHANGE, window_change_handler)
    
    async def send_event(self, event: ObserverEvent) -> None:
        """Manuel event gönder"""
        await self._process_event(event)
    
    def get_metrics(self) -> Dict[str, Any]:
        """Performans metriklerini al"""
        return {
            "observer": self.metrics,
            "debouncer": self.debouncer.get_metrics(),
            "processor": self.event_processor.get_metrics(),
            "fallback_mode": self.fallback_mode,
            "is_running": self.is_running
        }
