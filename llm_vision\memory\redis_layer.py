"""
Redis Memory Layer

Redis tabanlı L1 ve L2 hafıza katmanları.
"""

import json
import time
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass

try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    redis = None

from ..utils.logger import get_logger
from ..config import config

logger = get_logger(__name__)


@dataclass
class RedisMemoryEntry:
    """Redis hafıza girişi"""
    key: str
    data: Dict[str, Any]
    timestamp: float
    ttl: Optional[int] = None
    priority: str = "medium"
    tags: List[str] = None


class RedisMemoryLayer:
    """Redis tabanlı hafıza katmanı"""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.is_initialized = False
        self.connection_pool = None
        
        # Katman konfigürasyonu
        self.l1_prefix = "llm_vision:l1:"
        self.l2_prefix = "llm_vision:l2:"
        self.l1_ttl = 300  # 5 dakika
        self.l2_ttl = 3600  # 1 saat
        
        # Performans metrikleri
        self.metrics = {
            "total_operations": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "errors": 0,
            "connection_errors": 0
        }
        
        logger.info("Redis Memory Layer oluşturuldu")
    
    async def initialize(self) -> None:
        """Redis bağlantısını başlat"""
        try:
            if self.is_initialized:
                return
            
            if not REDIS_AVAILABLE:
                logger.warning("Redis modülü bulunamadı, mock mode aktif")
                self._setup_mock_mode()
                self.is_initialized = True
                return
            
            logger.info("Redis bağlantısı kuruluyor...")
            
            # Connection pool oluştur
            self.connection_pool = redis.ConnectionPool(
                host=config.memory.redis_host,
                port=config.memory.redis_port,
                db=config.memory.redis_db,
                password=config.memory.redis_password,
                decode_responses=True,
                max_connections=20
            )
            
            # Redis client oluştur
            self.redis_client = redis.Redis(connection_pool=self.connection_pool)
            
            # Bağlantıyı test et
            await self.redis_client.ping()
            
            self.is_initialized = True
            logger.info("Redis bağlantısı başarıyla kuruldu")
            
        except Exception as e:
            logger.error(f"Redis başlatma hatası: {e}")
            logger.warning("Mock mode'a geçiliyor...")
            self._setup_mock_mode()
            self.is_initialized = True
    
    def _setup_mock_mode(self) -> None:
        """Mock mode kurulumu"""
        self.mock_storage = {}
        self.redis_client = None
        logger.info("Redis mock mode aktif")
    
    async def store_l1(self, key: str, data: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """L1 katmanına veri kaydet"""
        try:
            full_key = f"{self.l1_prefix}{key}"
            ttl = ttl or self.l1_ttl
            
            entry = RedisMemoryEntry(
                key=key,
                data=data,
                timestamp=time.time(),
                ttl=ttl,
                priority="high"
            )
            
            if self.redis_client:
                serialized_data = json.dumps({
                    "data": entry.data,
                    "timestamp": entry.timestamp,
                    "priority": entry.priority
                })
                
                await self.redis_client.setex(full_key, ttl, serialized_data)
            else:
                # Mock mode
                self.mock_storage[full_key] = {
                    "data": entry.data,
                    "timestamp": entry.timestamp,
                    "expires_at": time.time() + ttl
                }
            
            self.metrics["total_operations"] += 1
            logger.debug(f"L1 veri kaydedildi: {key}")
            return True
            
        except Exception as e:
            logger.error(f"L1 kaydetme hatası: {e}")
            self.metrics["errors"] += 1
            return False
    
    async def get_l1(self, key: str) -> Optional[Dict[str, Any]]:
        """L1 katmanından veri al"""
        try:
            full_key = f"{self.l1_prefix}{key}"
            
            if self.redis_client:
                data = await self.redis_client.get(full_key)
                if data:
                    parsed_data = json.loads(data)
                    self.metrics["cache_hits"] += 1
                    logger.debug(f"L1 cache hit: {key}")
                    return parsed_data["data"]
            else:
                # Mock mode
                if full_key in self.mock_storage:
                    entry = self.mock_storage[full_key]
                    if time.time() < entry["expires_at"]:
                        self.metrics["cache_hits"] += 1
                        return entry["data"]
                    else:
                        del self.mock_storage[full_key]
            
            self.metrics["cache_misses"] += 1
            logger.debug(f"L1 cache miss: {key}")
            return None
            
        except Exception as e:
            logger.error(f"L1 okuma hatası: {e}")
            self.metrics["errors"] += 1
            return None
    
    async def store_l2(self, key: str, data: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """L2 katmanına veri kaydet"""
        try:
            full_key = f"{self.l2_prefix}{key}"
            ttl = ttl or self.l2_ttl
            
            entry = RedisMemoryEntry(
                key=key,
                data=data,
                timestamp=time.time(),
                ttl=ttl,
                priority="medium"
            )
            
            if self.redis_client:
                serialized_data = json.dumps({
                    "data": entry.data,
                    "timestamp": entry.timestamp,
                    "priority": entry.priority
                })
                
                await self.redis_client.setex(full_key, ttl, serialized_data)
            else:
                # Mock mode
                self.mock_storage[full_key] = {
                    "data": entry.data,
                    "timestamp": entry.timestamp,
                    "expires_at": time.time() + ttl
                }
            
            self.metrics["total_operations"] += 1
            logger.debug(f"L2 veri kaydedildi: {key}")
            return True
            
        except Exception as e:
            logger.error(f"L2 kaydetme hatası: {e}")
            self.metrics["errors"] += 1
            return False
    
    async def get_l2(self, key: str) -> Optional[Dict[str, Any]]:
        """L2 katmanından veri al"""
        try:
            full_key = f"{self.l2_prefix}{key}"
            
            if self.redis_client:
                data = await self.redis_client.get(full_key)
                if data:
                    parsed_data = json.loads(data)
                    self.metrics["cache_hits"] += 1
                    logger.debug(f"L2 cache hit: {key}")
                    return parsed_data["data"]
            else:
                # Mock mode
                if full_key in self.mock_storage:
                    entry = self.mock_storage[full_key]
                    if time.time() < entry["expires_at"]:
                        self.metrics["cache_hits"] += 1
                        return entry["data"]
                    else:
                        del self.mock_storage[full_key]
            
            self.metrics["cache_misses"] += 1
            logger.debug(f"L2 cache miss: {key}")
            return None
            
        except Exception as e:
            logger.error(f"L2 okuma hatası: {e}")
            self.metrics["errors"] += 1
            return None
    
    async def delete(self, key: str, layer: str = "both") -> bool:
        """Veriyi sil"""
        try:
            success = True
            
            if layer in ["l1", "both"]:
                full_key = f"{self.l1_prefix}{key}"
                if self.redis_client:
                    await self.redis_client.delete(full_key)
                else:
                    self.mock_storage.pop(full_key, None)
            
            if layer in ["l2", "both"]:
                full_key = f"{self.l2_prefix}{key}"
                if self.redis_client:
                    await self.redis_client.delete(full_key)
                else:
                    self.mock_storage.pop(full_key, None)
            
            logger.debug(f"Veri silindi: {key} ({layer})")
            return success
            
        except Exception as e:
            logger.error(f"Veri silme hatası: {e}")
            return False
    
    async def clear_layer(self, layer: str) -> bool:
        """Katmanı temizle"""
        try:
            if layer == "l1":
                pattern = f"{self.l1_prefix}*"
            elif layer == "l2":
                pattern = f"{self.l2_prefix}*"
            else:
                return False
            
            if self.redis_client:
                keys = await self.redis_client.keys(pattern)
                if keys:
                    await self.redis_client.delete(*keys)
            else:
                # Mock mode
                keys_to_delete = [k for k in self.mock_storage.keys() if k.startswith(pattern.replace("*", ""))]
                for key in keys_to_delete:
                    del self.mock_storage[key]
            
            logger.info(f"{layer.upper()} katmanı temizlendi")
            return True
            
        except Exception as e:
            logger.error(f"Katman temizleme hatası: {e}")
            return False
    
    async def get_keys(self, pattern: str = "*", layer: str = "both") -> List[str]:
        """Anahtarları listele"""
        try:
            keys = []
            
            if layer in ["l1", "both"]:
                l1_pattern = f"{self.l1_prefix}{pattern}"
                if self.redis_client:
                    l1_keys = await self.redis_client.keys(l1_pattern)
                    keys.extend([k.replace(self.l1_prefix, "") for k in l1_keys])
                else:
                    l1_keys = [k for k in self.mock_storage.keys() if k.startswith(self.l1_prefix)]
                    keys.extend([k.replace(self.l1_prefix, "") for k in l1_keys])
            
            if layer in ["l2", "both"]:
                l2_pattern = f"{self.l2_prefix}{pattern}"
                if self.redis_client:
                    l2_keys = await self.redis_client.keys(l2_pattern)
                    keys.extend([k.replace(self.l2_prefix, "") for k in l2_keys])
                else:
                    l2_keys = [k for k in self.mock_storage.keys() if k.startswith(self.l2_prefix)]
                    keys.extend([k.replace(self.l2_prefix, "") for k in l2_keys])
            
            return list(set(keys))  # Duplicate'leri kaldır
            
        except Exception as e:
            logger.error(f"Anahtar listeleme hatası: {e}")
            return []
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Performans metriklerini al"""
        try:
            info = {}
            
            if self.redis_client:
                redis_info = await self.redis_client.info()
                info = {
                    "redis_version": redis_info.get("redis_version"),
                    "used_memory": redis_info.get("used_memory"),
                    "connected_clients": redis_info.get("connected_clients"),
                    "total_commands_processed": redis_info.get("total_commands_processed")
                }
            else:
                info = {
                    "mode": "mock",
                    "mock_storage_size": len(self.mock_storage)
                }
            
            return {
                **self.metrics,
                "redis_info": info,
                "cache_hit_rate": (
                    self.metrics["cache_hits"] / 
                    (self.metrics["cache_hits"] + self.metrics["cache_misses"])
                    if (self.metrics["cache_hits"] + self.metrics["cache_misses"]) > 0 else 0
                )
            }
            
        except Exception as e:
            logger.error(f"Metrik alma hatası: {e}")
            return self.metrics
    
    async def cleanup(self) -> None:
        """Kaynakları temizle"""
        try:
            if self.redis_client:
                await self.redis_client.close()
            
            if self.connection_pool:
                await self.connection_pool.disconnect()
            
            if hasattr(self, 'mock_storage'):
                self.mock_storage.clear()
            
            logger.info("Redis Memory Layer temizlendi")
            
        except Exception as e:
            logger.error(f"Redis temizleme hatası: {e}")
