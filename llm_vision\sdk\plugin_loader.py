"""
Plugin Loader System

Plugin'lerin dinamik olarak yüklenmesi ve başlatılması için sistem.
"""

import importlib.util
import sys
import inspect
from pathlib import Path
from typing import Optional, Type, Dict, Any, List
import ast
import subprocess
import venv

from .base_plugin import BasePlugin
from .plugin_manifest import PluginManifest
from .manifest_validator import ManifestValidator
from ..utils.logger import get_logger

logger = get_logger(__name__)


class PluginLoadError(Exception):
    """Plugin yükleme hatası"""
    pass


class PluginLoader:
    """Plugin yükleme sistemi"""
    
    def __init__(self):
        self.loaded_modules: Dict[str, Any] = {}
        self.manifest_validator = ManifestValidator()
        self.is_initialized = False
        
        # Güvenlik ayarları
        self.allowed_imports = {
            # Standart kütüphaneler
            "os", "sys", "json", "time", "datetime", "pathlib", "typing",
            "asyncio", "logging", "re", "math", "random", "uuid",
            
            # Veri işleme
            "numpy", "pandas", "scipy",
            
            # Web ve API
            "requests", "aiohttp", "fastapi", "websockets",
            
            # LLM Vision modülleri
            "llm_vision.sdk", "llm_vision.utils", "llm_vision.core"
        }
        
        self.forbidden_imports = {
            "subprocess", "eval", "exec", "compile", "__import__"
        }
        
        logger.info("Plugin Loader oluşturuldu")
    
    async def initialize(self) -> None:
        """Plugin loader'ı başlat"""
        try:
            if self.is_initialized:
                return
            
            logger.info("Plugin Loader başlatılıyor...")
            
            # Manifest validator'ı başlat
            await self.manifest_validator.initialize()
            
            self.is_initialized = True
            logger.info("Plugin Loader başarıyla başlatıldı")
            
        except Exception as e:
            logger.error(f"Plugin Loader başlatma hatası: {e}")
            raise
    
    async def load_plugin_from_path(self, plugin_path: Path) -> Optional[BasePlugin]:
        """
        Plugin'i dosya yolundan yükle
        
        Args:
            plugin_path: Plugin dizini
            
        Returns:
            BasePlugin: Yüklenen plugin veya None
        """
        try:
            if not plugin_path.exists() or not plugin_path.is_dir():
                raise PluginLoadError(f"Plugin dizini bulunamadı: {plugin_path}")
            
            logger.info(f"Plugin yükleniyor: {plugin_path}")
            
            # 1. Manifest dosyasını yükle ve doğrula
            manifest = await self._load_and_validate_manifest(plugin_path)
            
            # 2. Bağımlılıkları kontrol et
            await self._check_dependencies(manifest)
            
            # 3. Python modülünü yükle
            plugin_module = await self._load_plugin_module(plugin_path, manifest)
            
            # 4. Plugin sınıfını bul ve örnekle
            plugin_instance = await self._instantiate_plugin(plugin_module, manifest)
            
            logger.info(f"Plugin başarıyla yüklendi: {manifest.name}")
            return plugin_instance
            
        except Exception as e:
            logger.error(f"Plugin yükleme hatası ({plugin_path}): {e}")
            return None
    
    async def _load_and_validate_manifest(self, plugin_path: Path) -> PluginManifest:
        """Manifest dosyasını yükle ve doğrula"""
        # Manifest dosyasını bul
        manifest_files = list(plugin_path.glob("manifest.*"))
        if not manifest_files:
            raise PluginLoadError("Manifest dosyası bulunamadı")
        
        manifest_path = manifest_files[0]
        
        # Manifest'i yükle
        manifest = PluginManifest.from_file(manifest_path)
        
        # Manifest'i doğrula
        validation_errors = await self.manifest_validator.validate(manifest)
        if validation_errors:
            raise PluginLoadError(f"Manifest doğrulama hataları: {validation_errors}")
        
        return manifest
    
    async def _check_dependencies(self, manifest: PluginManifest) -> None:
        """Plugin bağımlılıklarını kontrol et"""
        for dependency in manifest.dependencies:
            try:
                # Python paketini import etmeye çalış
                importlib.import_module(dependency.name)
                logger.debug(f"Bağımlılık mevcut: {dependency.name}")
                
            except ImportError:
                if not dependency.optional:
                    raise PluginLoadError(f"Gerekli bağımlılık bulunamadı: {dependency.name}")
                else:
                    logger.warning(f"Opsiyonel bağımlılık bulunamadı: {dependency.name}")
    
    async def _load_plugin_module(self, plugin_path: Path, manifest: PluginManifest) -> Any:
        """Plugin Python modülünü yükle"""
        # Plugin.py dosyasını bul
        plugin_file = plugin_path / "plugin.py"
        if not plugin_file.exists():
            raise PluginLoadError("plugin.py dosyası bulunamadı")
        
        # Güvenlik kontrolü
        await self._security_check(plugin_file)
        
        # Modülü yükle
        module_name = f"plugin_{manifest.name}_{manifest.version}".replace(".", "_").replace("-", "_")
        
        try:
            spec = importlib.util.spec_from_file_location(module_name, plugin_file)
            if spec is None or spec.loader is None:
                raise PluginLoadError("Modül spec oluşturulamadı")
            
            module = importlib.util.module_from_spec(spec)
            
            # Modülü sys.modules'e ekle
            sys.modules[module_name] = module
            
            # Modülü çalıştır
            spec.loader.exec_module(module)
            
            # Cache'e ekle
            self.loaded_modules[manifest.name] = module
            
            return module
            
        except Exception as e:
            # Hata durumunda sys.modules'den kaldır
            if module_name in sys.modules:
                del sys.modules[module_name]
            raise PluginLoadError(f"Modül yükleme hatası: {e}")
    
    async def _security_check(self, plugin_file: Path) -> None:
        """Plugin dosyasının güvenlik kontrolü"""
        try:
            # Dosyayı oku
            with open(plugin_file, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            # AST parse et
            tree = ast.parse(source_code)
            
            # Güvenlik kontrolü
            security_visitor = SecurityVisitor(self.allowed_imports, self.forbidden_imports)
            security_visitor.visit(tree)
            
            if security_visitor.violations:
                raise PluginLoadError(f"Güvenlik ihlalleri: {security_visitor.violations}")
            
        except SyntaxError as e:
            raise PluginLoadError(f"Syntax hatası: {e}")
        except Exception as e:
            raise PluginLoadError(f"Güvenlik kontrolü hatası: {e}")
    
    async def _instantiate_plugin(self, module: Any, manifest: PluginManifest) -> BasePlugin:
        """Plugin sınıfını bul ve örnekle"""
        # Plugin sınıfını bul
        plugin_class = self._find_plugin_class(module)
        if plugin_class is None:
            raise PluginLoadError("Plugin sınıfı bulunamadı")
        
        # Plugin'i örnekle
        try:
            plugin_instance = plugin_class(manifest)
            
            # BasePlugin'den türetildiğini kontrol et
            if not isinstance(plugin_instance, BasePlugin):
                raise PluginLoadError("Plugin BasePlugin'den türetilmemiş")
            
            return plugin_instance
            
        except Exception as e:
            raise PluginLoadError(f"Plugin örnekleme hatası: {e}")
    
    def _find_plugin_class(self, module: Any) -> Optional[Type[BasePlugin]]:
        """Modülde plugin sınıfını bul"""
        for name, obj in inspect.getmembers(module):
            if (inspect.isclass(obj) and 
                issubclass(obj, BasePlugin) and 
                obj != BasePlugin and
                not name.startswith('Base')):
                return obj
        return None
    
    async def unload_plugin(self, plugin_name: str) -> bool:
        """Plugin'i bellekten kaldır"""
        try:
            if plugin_name in self.loaded_modules:
                module = self.loaded_modules[plugin_name]
                module_name = module.__name__
                
                # sys.modules'den kaldır
                if module_name in sys.modules:
                    del sys.modules[module_name]
                
                # Cache'den kaldır
                del self.loaded_modules[plugin_name]
                
                logger.info(f"Plugin bellekten kaldırıldı: {plugin_name}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Plugin kaldırma hatası: {e}")
            return False
    
    def get_loaded_modules(self) -> Dict[str, Any]:
        """Yüklü modülleri döndür"""
        return self.loaded_modules.copy()


class SecurityVisitor(ast.NodeVisitor):
    """AST güvenlik kontrolü için visitor"""
    
    def __init__(self, allowed_imports: set, forbidden_imports: set):
        self.allowed_imports = allowed_imports
        self.forbidden_imports = forbidden_imports
        self.violations: List[str] = []
    
    def visit_Import(self, node):
        """Import statement'ları kontrol et"""
        for alias in node.names:
            module_name = alias.name.split('.')[0]
            
            if module_name in self.forbidden_imports:
                self.violations.append(f"Yasaklı import: {module_name}")
            elif module_name not in self.allowed_imports and not module_name.startswith('llm_vision'):
                self.violations.append(f"İzin verilmeyen import: {module_name}")
        
        self.generic_visit(node)
    
    def visit_ImportFrom(self, node):
        """From import statement'ları kontrol et"""
        if node.module:
            module_name = node.module.split('.')[0]
            
            if module_name in self.forbidden_imports:
                self.violations.append(f"Yasaklı from import: {module_name}")
            elif module_name not in self.allowed_imports and not module_name.startswith('llm_vision'):
                self.violations.append(f"İzin verilmeyen from import: {module_name}")
        
        self.generic_visit(node)
    
    def visit_Call(self, node):
        """Function call'ları kontrol et"""
        if isinstance(node.func, ast.Name):
            func_name = node.func.id
            if func_name in self.forbidden_imports:
                self.violations.append(f"Yasaklı function call: {func_name}")
        
        self.generic_visit(node)
