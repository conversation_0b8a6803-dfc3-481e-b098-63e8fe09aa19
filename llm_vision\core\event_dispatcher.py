"""
Event dispatcher modülü
"""

import asyncio
from typing import Dict, List, Optional, Any, Callable, Set
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from ..utils.logger import get_logger
from ..utils.exceptions import LLMVisionError

logger = get_logger(__name__)


class EventPriority(str, Enum):
    """Event öncelik seviyeleri"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class Event:
    """Event sınıfı"""
    name: str
    data: Dict[str, Any]
    priority: EventPriority = EventPriority.NORMAL
    timestamp: datetime = None
    source: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
    def to_dict(self) -> dict:
        """Sözlük formatına çevir"""
        return {
            "name": self.name,
            "data": self.data,
            "priority": self.priority.value,
            "timestamp": self.timestamp.isoformat(),
            "source": self.source
        }


class EventDispatcher:
    """Event dispatcher sınıfı"""
    
    def __init__(self):
        # Event handler'ları
        self.handlers: Dict[str, List[Callable]] = {}
        self.wildcard_handlers: List[Callable] = []
        
        # Event kuyruğu
        self.event_queue: asyncio.Queue = asyncio.Queue()
        self.is_processing = False
        self.processor_task: Optional[asyncio.Task] = None
        
        # Event geçmişi
        self.event_history: List[Event] = []
        self.max_history_size = 10000
        
        # İstatistikler
        self.event_stats = {
            "total_dispatched": 0,
            "total_processed": 0,
            "total_failed": 0,
            "handler_counts": {},
            "event_type_counts": {}
        }
        
        # Filtreler
        self.event_filters: List[Callable] = []
        
        logger.info("Event dispatcher oluşturuldu")
    
    async def initialize(self) -> None:
        """Event dispatcher'ı başlat"""
        try:
            # Event işleme görevini başlat
            self.processor_task = asyncio.create_task(self._process_events())
            self.is_processing = True
            
            logger.info("Event dispatcher başlatıldı")
            
        except Exception as e:
            logger.error(f"Event dispatcher başlatma hatası: {e}")
            raise LLMVisionError(f"Event dispatcher başlatma hatası: {str(e)}")
    
    async def shutdown(self) -> None:
        """Event dispatcher'ı kapat"""
        try:
            self.is_processing = False
            
            # İşleme görevini iptal et
            if self.processor_task and not self.processor_task.done():
                self.processor_task.cancel()
                try:
                    await self.processor_task
                except asyncio.CancelledError:
                    pass
            
            # Kalan event'leri işle
            while not self.event_queue.empty():
                try:
                    event = self.event_queue.get_nowait()
                    await self._handle_event(event)
                except asyncio.QueueEmpty:
                    break
                except Exception as e:
                    logger.error(f"Son event işleme hatası: {e}")
            
            logger.info("Event dispatcher kapatıldı")
            
        except Exception as e:
            logger.error(f"Event dispatcher kapatma hatası: {e}")
    
    def register_handler(self, event_name: str, handler: Callable) -> None:
        """Event handler kaydet"""
        try:
            if event_name not in self.handlers:
                self.handlers[event_name] = []
            
            self.handlers[event_name].append(handler)
            
            # İstatistikleri güncelle
            self.event_stats["handler_counts"][event_name] = \
                self.event_stats["handler_counts"].get(event_name, 0) + 1
            
            logger.debug(f"Event handler kaydedildi: {event_name}")
            
        except Exception as e:
            logger.error(f"Event handler kaydetme hatası: {e}")
    
    def unregister_handler(self, event_name: str, handler: Callable) -> bool:
        """Event handler kaydını sil"""
        try:
            if event_name in self.handlers and handler in self.handlers[event_name]:
                self.handlers[event_name].remove(handler)
                
                # Boş liste ise sil
                if not self.handlers[event_name]:
                    del self.handlers[event_name]
                
                # İstatistikleri güncelle
                self.event_stats["handler_counts"][event_name] = \
                    max(0, self.event_stats["handler_counts"].get(event_name, 1) - 1)
                
                logger.debug(f"Event handler kaydı silindi: {event_name}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Event handler kayıt silme hatası: {e}")
            return False
    
    def register_wildcard_handler(self, handler: Callable) -> None:
        """Wildcard event handler kaydet (tüm event'ler için)"""
        try:
            self.wildcard_handlers.append(handler)
            logger.debug("Wildcard event handler kaydedildi")
            
        except Exception as e:
            logger.error(f"Wildcard handler kaydetme hatası: {e}")
    
    def add_event_filter(self, filter_func: Callable) -> None:
        """Event filtresi ekle"""
        try:
            self.event_filters.append(filter_func)
            logger.debug("Event filtresi eklendi")
            
        except Exception as e:
            logger.error(f"Event filtre ekleme hatası: {e}")
    
    async def dispatch_event(self, 
                           event_name: str, 
                           event_data: Dict[str, Any],
                           priority: EventPriority = EventPriority.NORMAL,
                           source: str = None) -> None:
        """Event gönder"""
        try:
            # Event oluştur
            event = Event(
                name=event_name,
                data=event_data,
                priority=priority,
                source=source
            )
            
            # Filtreleri uygula
            if not self._apply_filters(event):
                logger.debug(f"Event filtre edildi: {event_name}")
                return
            
            # Kuyruğa ekle
            await self.event_queue.put(event)
            
            # İstatistikleri güncelle
            self.event_stats["total_dispatched"] += 1
            self.event_stats["event_type_counts"][event_name] = \
                self.event_stats["event_type_counts"].get(event_name, 0) + 1
            
            logger.debug(f"Event gönderildi: {event_name}")
            
        except Exception as e:
            logger.error(f"Event gönderme hatası: {e}")
    
    def _apply_filters(self, event: Event) -> bool:
        """Event filtrelerini uygula"""
        try:
            for filter_func in self.event_filters:
                try:
                    if not filter_func(event):
                        return False
                except Exception as e:
                    logger.error(f"Event filtre hatası: {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"Event filtre uygulama hatası: {e}")
            return True  # Hata durumunda geçir
    
    async def _process_events(self) -> None:
        """Event'leri işle"""
        try:
            while self.is_processing:
                try:
                    # Event al (1 saniye timeout)
                    event = await asyncio.wait_for(
                        self.event_queue.get(), timeout=1.0
                    )
                    
                    # Event'i işle
                    await self._handle_event(event)
                    
                except asyncio.TimeoutError:
                    # Timeout normal, devam et
                    continue
                except Exception as e:
                    logger.error(f"Event işleme hatası: {e}")
                    
        except asyncio.CancelledError:
            logger.debug("Event işleme görevi iptal edildi")
        except Exception as e:
            logger.error(f"Event işleme döngüsü hatası: {e}")
    
    async def _handle_event(self, event: Event) -> None:
        """Tek bir event'i işle"""
        try:
            # Geçmişe ekle
            self._add_to_history(event)
            
            # Handler'ları çağır
            handlers_called = 0
            
            # Spesifik handler'lar
            if event.name in self.handlers:
                for handler in self.handlers[event.name]:
                    try:
                        if asyncio.iscoroutinefunction(handler):
                            await handler(event.data)
                        else:
                            handler(event.data)
                        handlers_called += 1
                    except Exception as e:
                        logger.error(f"Event handler hatası ({event.name}): {e}")
                        self.event_stats["total_failed"] += 1
            
            # Wildcard handler'lar
            for handler in self.wildcard_handlers:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(event)
                    else:
                        handler(event)
                    handlers_called += 1
                except Exception as e:
                    logger.error(f"Wildcard handler hatası: {e}")
                    self.event_stats["total_failed"] += 1
            
            # İstatistikleri güncelle
            self.event_stats["total_processed"] += 1
            
            logger.debug(f"Event işlendi: {event.name} ({handlers_called} handler)")
            
        except Exception as e:
            logger.error(f"Event işleme hatası: {e}")
            self.event_stats["total_failed"] += 1
    
    def _add_to_history(self, event: Event) -> None:
        """Event'i geçmişe ekle"""
        try:
            self.event_history.append(event)
            
            # Maksimum boyut kontrolü
            if len(self.event_history) > self.max_history_size:
                self.event_history.pop(0)
                
        except Exception as e:
            logger.error(f"Event geçmiş ekleme hatası: {e}")
    
    def get_event_history(self, 
                         limit: int = 100,
                         event_name: str = None,
                         priority: EventPriority = None) -> List[Event]:
        """Event geçmişini al"""
        try:
            history = self.event_history.copy()
            
            # Filtrele
            if event_name:
                history = [e for e in history if e.name == event_name]
            
            if priority:
                history = [e for e in history if e.priority == priority]
            
            # Sırala ve sınırla
            history.sort(key=lambda x: x.timestamp, reverse=True)
            return history[:limit]
            
        except Exception as e:
            logger.error(f"Event geçmiş alma hatası: {e}")
            return []
    
    def get_event_stats(self) -> Dict[str, Any]:
        """Event istatistiklerini al"""
        try:
            return {
                **self.event_stats,
                "queue_size": self.event_queue.qsize(),
                "history_size": len(self.event_history),
                "is_processing": self.is_processing,
                "registered_handlers": {
                    event_name: len(handlers) 
                    for event_name, handlers in self.handlers.items()
                },
                "wildcard_handlers": len(self.wildcard_handlers),
                "active_filters": len(self.event_filters)
            }
            
        except Exception as e:
            logger.error(f"Event istatistik alma hatası: {e}")
            return {}
    
    def clear_history(self) -> int:
        """Event geçmişini temizle"""
        try:
            count = len(self.event_history)
            self.event_history.clear()
            logger.info(f"Event geçmişi temizlendi ({count} event)")
            return count
            
        except Exception as e:
            logger.error(f"Event geçmiş temizleme hatası: {e}")
            return 0
    
    async def wait_for_event(self, 
                           event_name: str, 
                           timeout: float = None) -> Optional[Event]:
        """Belirli bir event'i bekle"""
        try:
            future = asyncio.Future()
            
            def handler(event_data):
                if not future.done():
                    # Event'i geçmişten bul
                    for event in reversed(self.event_history):
                        if event.name == event_name:
                            future.set_result(event)
                            break
            
            # Geçici handler kaydet
            self.register_handler(event_name, handler)
            
            try:
                # Event'i bekle
                if timeout:
                    event = await asyncio.wait_for(future, timeout=timeout)
                else:
                    event = await future
                
                return event
                
            finally:
                # Handler'ı temizle
                self.unregister_handler(event_name, handler)
                
        except asyncio.TimeoutError:
            logger.debug(f"Event bekleme timeout: {event_name}")
            return None
        except Exception as e:
            logger.error(f"Event bekleme hatası: {e}")
            return None
