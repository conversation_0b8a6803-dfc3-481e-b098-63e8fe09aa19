"""
<PERSON> görüntü işleme modülü
"""

import cv2
import numpy as np
import asyncio
import base64
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import io
from PIL import Image

from ..utils.logger import get_logger
from ..utils.exceptions import VisionError
from .camera import CameraManager
from .detector import ObjectDetector, DetectedObject
from .ocr import OCRProcessor, OCRResult
from .screen import ScreenCapture

logger = get_logger(__name__)


class VisionProcessor:
    """Ana görüntü işleme sınıfı"""
    
    def __init__(self):
        self.camera_manager = CameraManager()
        self.object_detector = ObjectDetector()
        self.ocr_processor = OCRProcessor()
        self.screen_capture = ScreenCapture()
        
        self.is_initialized = False
        logger.info("Vision processor başlatıldı")
    
    async def initialize(self, 
                        enable_camera: bool = True,
                        enable_yolo: bool = False,
                        yolo_weights: str = None,
                        yolo_config: str = None) -> bool:
        """Vision processor'ı başlat"""
        try:
            success = True
            
            # Kamerayı başlat
            if enable_camera:
                try:
                    camera_success = self.camera_manager.start()
                    if not camera_success:
                        logger.warning("Kamera başlatılamadı")
                        success = False
                except Exception as e:
                    logger.error(f"Kamera başlatma hatası: {e}")
                    success = False
            
            # YOLO modelini yükle
            if enable_yolo:
                try:
                    yolo_success = self.object_detector.load_yolo_model(
                        yolo_weights, yolo_config
                    )
                    if not yolo_success:
                        logger.warning("YOLO modeli yüklenemedi, basit algılama kullanılacak")
                except Exception as e:
                    logger.error(f"YOLO model yükleme hatası: {e}")
            
            # Bileşenlerin kullanılabilirliğini kontrol et
            components_status = await self.get_system_status()
            logger.info(f"Sistem durumu: {components_status}")
            
            self.is_initialized = True
            logger.info("Vision processor başarıyla başlatıldı")
            return success
            
        except Exception as e:
            logger.error(f"Vision processor başlatma hatası: {e}")
            return False
    
    async def shutdown(self) -> None:
        """Vision processor'ı kapat"""
        try:
            self.camera_manager.stop()
            self.is_initialized = False
            logger.info("Vision processor kapatıldı")
        except Exception as e:
            logger.error(f"Vision processor kapatma hatası: {e}")
    
    async def process_camera_feed(self, 
                                 detect_objects: bool = True,
                                 extract_text: bool = False,
                                 confidence_threshold: float = 0.5) -> Dict[str, Any]:
        """Kamera görüntüsünü işle"""
        try:
            # Kameradan frame al
            frame = await self.camera_manager.get_frame_async()
            if frame is None:
                raise VisionError("Kameradan görüntü alınamadı")
            
            return await self._process_image(
                frame, detect_objects, extract_text, confidence_threshold
            )
            
        except Exception as e:
            logger.error(f"Kamera görüntüsü işleme hatası: {e}")
            raise VisionError(f"Kamera görüntüsü işleme hatası: {str(e)}")
    
    async def process_screen_capture(self, 
                                   region: Tuple[int, int, int, int] = None,
                                   detect_objects: bool = True,
                                   extract_text: bool = True,
                                   confidence_threshold: float = 0.5) -> Dict[str, Any]:
        """Ekran görüntüsünü işle"""
        try:
            # Ekran görüntüsü al
            screenshot = await self.screen_capture.capture_screen_async(region)
            if screenshot is None:
                raise VisionError("Ekran görüntüsü alınamadı")
            
            return await self._process_image(
                screenshot, detect_objects, extract_text, confidence_threshold
            )
            
        except Exception as e:
            logger.error(f"Ekran görüntüsü işleme hatası: {e}")
            raise VisionError(f"Ekran görüntüsü işleme hatası: {str(e)}")
    
    async def process_image_file(self, 
                               file_path: str,
                               detect_objects: bool = True,
                               extract_text: bool = False,
                               confidence_threshold: float = 0.5) -> Dict[str, Any]:
        """Dosyadan görüntü işle"""
        try:
            # Görüntü dosyasını yükle
            image = cv2.imread(file_path)
            if image is None:
                raise VisionError(f"Görüntü dosyası yüklenemedi: {file_path}")
            
            return await self._process_image(
                image, detect_objects, extract_text, confidence_threshold
            )
            
        except Exception as e:
            logger.error(f"Görüntü dosyası işleme hatası: {e}")
            raise VisionError(f"Görüntü dosyası işleme hatası: {str(e)}")
    
    async def _process_image(self, 
                           image: np.ndarray,
                           detect_objects: bool = True,
                           extract_text: bool = False,
                           confidence_threshold: float = 0.5) -> Dict[str, Any]:
        """Görüntüyü işle"""
        try:
            result = {
                "timestamp": datetime.now().isoformat(),
                "image_info": {
                    "shape": image.shape,
                    "size": image.size,
                    "dtype": str(image.dtype)
                },
                "objects": [],
                "text": [],
                "summary": ""
            }
            
            # Nesne tanıma
            if detect_objects:
                try:
                    objects = await self.object_detector.detect_objects_async(
                        image, confidence_threshold
                    )
                    result["objects"] = [obj.to_dict() for obj in objects]
                    
                    # Nesne özetini ekle
                    object_summary = self.object_detector.get_detection_summary(objects)
                    result["object_summary"] = object_summary
                    
                except Exception as e:
                    logger.error(f"Nesne tanıma hatası: {e}")
                    result["object_error"] = str(e)
            
            # Metin çıkarma
            if extract_text:
                try:
                    ocr_results = await self.ocr_processor.extract_text_async(
                        image, detailed=True
                    )
                    result["text"] = [ocr.to_dict() for ocr in ocr_results]
                    
                    # OCR özetini ekle
                    ocr_summary = self.ocr_processor.get_ocr_summary(ocr_results)
                    result["ocr_summary"] = ocr_summary
                    
                except Exception as e:
                    logger.error(f"OCR hatası: {e}")
                    result["ocr_error"] = str(e)
            
            # Genel özet oluştur
            result["summary"] = self._create_summary(result)
            
            # Görüntüyü base64 formatında ekle (isteğe bağlı)
            result["image_base64"] = self._image_to_base64(image)
            
            return result
            
        except Exception as e:
            logger.error(f"Görüntü işleme hatası: {e}")
            raise VisionError(f"Görüntü işleme hatası: {str(e)}")
    
    def _create_summary(self, result: Dict[str, Any]) -> str:
        """İşleme sonuçlarından özet oluştur"""
        summary_parts = []
        
        # Görüntü bilgisi
        shape = result["image_info"]["shape"]
        summary_parts.append(f"Görüntü boyutu: {shape[1]}x{shape[0]}")
        
        # Nesne tanıma özeti
        if result["objects"]:
            object_count = len(result["objects"])
            summary_parts.append(f"{object_count} nesne tanındı")
            
            # En yaygın nesneler
            if "object_summary" in result:
                classes = result["object_summary"].get("classes", {})
                if classes:
                    top_classes = sorted(classes.items(), key=lambda x: x[1], reverse=True)[:3]
                    class_list = [f"{name}({count})" for name, count in top_classes]
                    summary_parts.append(f"Nesneler: {', '.join(class_list)}")
        
        # OCR özeti
        if result["text"]:
            if "ocr_summary" in result:
                char_count = result["ocr_summary"].get("total_characters", 0)
                if char_count > 0:
                    summary_parts.append(f"{char_count} karakter metin bulundu")
                    
                    # Desen analizi
                    patterns = result["ocr_summary"].get("patterns", {})
                    found_patterns = [name for name, count in patterns.items() if count > 0]
                    if found_patterns:
                        summary_parts.append(f"Desenler: {', '.join(found_patterns)}")
        
        return ". ".join(summary_parts) if summary_parts else "İşleme tamamlandı"
    
    def _image_to_base64(self, image: np.ndarray, format: str = "JPEG") -> str:
        """Görüntüyü base64 formatına çevir"""
        try:
            # OpenCV BGR'den RGB'ye çevir
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # PIL Image'a çevir
            pil_image = Image.fromarray(image_rgb)
            
            # Base64'e çevir
            buffer = io.BytesIO()
            pil_image.save(buffer, format=format, quality=85)
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            
            return f"data:image/{format.lower()};base64,{image_base64}"
            
        except Exception as e:
            logger.error(f"Base64 çevirme hatası: {e}")
            return ""
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Sistem durumunu al"""
        try:
            status = {
                "initialized": self.is_initialized,
                "camera": {
                    "available": self.camera_manager.is_available(),
                    "running": self.camera_manager.is_running,
                    "info": self.camera_manager.get_camera_info()
                },
                "object_detection": {
                    "yolo_loaded": self.object_detector.is_loaded,
                    "classes_count": len(self.object_detector.classes)
                },
                "ocr": {
                    "available": self.ocr_processor.is_available(),
                    "languages": self.ocr_processor.languages
                },
                "screen_capture": {
                    "available": self.screen_capture.is_available(),
                    "screen_size": self.screen_capture.get_screen_size(),
                    "platform": self.screen_capture.platform
                }
            }
            
            return status
            
        except Exception as e:
            logger.error(f"Sistem durumu alma hatası: {e}")
            return {"error": str(e)}
    
    async def create_annotated_image(self, 
                                   image: np.ndarray,
                                   objects: List[DetectedObject] = None,
                                   ocr_results: List[OCRResult] = None) -> np.ndarray:
        """Açıklamalı görüntü oluştur"""
        try:
            annotated_image = image.copy()
            
            # Nesne tanıma sonuçlarını çiz
            if objects:
                annotated_image = self.object_detector.draw_detections(
                    annotated_image, objects
                )
            
            # OCR sonuçlarını çiz
            if ocr_results:
                annotated_image = self.ocr_processor.draw_ocr_results(
                    annotated_image, ocr_results
                )
            
            return annotated_image
            
        except Exception as e:
            logger.error(f"Açıklamalı görüntü oluşturma hatası: {e}")
            return image
    
    def __enter__(self):
        """Context manager giriş"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager çıkış"""
        asyncio.create_task(self.shutdown())
    
    async def __aenter__(self):
        """Async context manager giriş"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager çıkış"""
        await self.shutdown()
