"""
CLI komutları modülü
"""

import asyncio
import json
import sys
from typing import Dict, Any, Optional
from datetime import datetime

from ..core.system_coordinator import SystemCoordinator
from ..utils.logger import get_logger, setup_logging
from ..config import config

logger = get_logger(__name__)


class CLICommands:
    """CLI komutları sınıfı"""
    
    def __init__(self):
        self.coordinator: Optional[SystemCoordinator] = None
        
    async def initialize_system(self) -> bool:
        """<PERSON><PERSON><PERSON> başlat"""
        try:
            self.coordinator = SystemCoordinator()
            await self.coordinator.initialize()
            return True
        except Exception as e:
            logger.error(f"Sistem başlatma hatası: {e}")
            return False
    
    async def shutdown_system(self) -> None:
        """Sistemi kapat"""
        try:
            if self.coordinator:
                await self.coordinator.shutdown()
        except Exception as e:
            logger.error(f"Siste<PERSON> kapatma hatası: {e}")
    
    async def status_command(self) -> Dict[str, Any]:
        """Sistem durumu komutunu çalıştır"""
        try:
            if not self.coordinator:
                if not await self.initialize_system():
                    return {"error": "Sistem başlatılamadı"}
            
            status = self.coordinator.get_system_status()
            return status
            
        except Exception as e:
            logger.error(f"Status komutu hatası: {e}")
            return {"error": str(e)}
        finally:
            await self.shutdown_system()
    
    async def test_camera_command(self) -> Dict[str, Any]:
        """Kamera test komutunu çalıştır"""
        try:
            if not self.coordinator:
                if not await self.initialize_system():
                    return {"error": "Sistem başlatılamadı"}
            
            # Vision processor'ı kontrol et
            vision_processor = self.coordinator.vision_processor
            if not vision_processor:
                return {"error": "Vision processor mevcut değil"}
            
            # Kamera test et
            result = await vision_processor.process_camera_feed(
                detect_objects=True,
                extract_text=False,
                confidence_threshold=0.5
            )
            
            return {
                "success": True,
                "message": "Kamera testi başarılı",
                "result": result
            }
            
        except Exception as e:
            logger.error(f"Kamera test hatası: {e}")
            return {"error": str(e)}
        finally:
            await self.shutdown_system()
    
    async def test_screen_command(self) -> Dict[str, Any]:
        """Ekran görüntüsü test komutunu çalıştır"""
        try:
            if not self.coordinator:
                if not await self.initialize_system():
                    return {"error": "Sistem başlatılamadı"}
            
            # Vision processor'ı kontrol et
            vision_processor = self.coordinator.vision_processor
            if not vision_processor:
                return {"error": "Vision processor mevcut değil"}
            
            # Ekran görüntüsü test et
            result = await vision_processor.process_screen_capture(
                detect_objects=True,
                extract_text=True
            )
            
            return {
                "success": True,
                "message": "Ekran görüntüsü testi başarılı",
                "result": result
            }
            
        except Exception as e:
            logger.error(f"Ekran test hatası: {e}")
            return {"error": str(e)}
        finally:
            await self.shutdown_system()
    
    async def test_context_command(self, query: str = None) -> Dict[str, Any]:
        """Context engine test komutunu çalıştır"""
        try:
            if not self.coordinator:
                if not await self.initialize_system():
                    return {"error": "Sistem başlatılamadı"}
            
            # Context engine'i kontrol et
            context_engine = self.coordinator.context_engine
            if not context_engine:
                return {"error": "Context engine mevcut değil"}
            
            # Test verisi oluştur
            test_data = [
                {
                    "type": "test",
                    "timestamp": datetime.now().isoformat(),
                    "data": {"message": "Test verisi"}
                }
            ]
            
            # Context oluştur
            result = await context_engine.create_comprehensive_context(
                test_data, query or "Test sorgusu"
            )
            
            return {
                "success": True,
                "message": "Context engine testi başarılı",
                "result": result
            }
            
        except Exception as e:
            logger.error(f"Context test hatası: {e}")
            return {"error": str(e)}
        finally:
            await self.shutdown_system()
    
    async def analyze_file_command(self, file_path: str) -> Dict[str, Any]:
        """Dosya analizi komutunu çalıştır"""
        try:
            if not self.coordinator:
                if not await self.initialize_system():
                    return {"error": "Sistem başlatılamadı"}
            
            # File analyzer'ı kullan
            from ..filesystem.file_analyzer import FileAnalyzer
            analyzer = FileAnalyzer()
            
            result = await analyzer.analyze_file(file_path)
            
            # Context engine ile analiz et
            context_engine = self.coordinator.context_engine
            if context_engine:
                analysis = await context_engine.analyze_file_data(result)
                return {
                    "success": True,
                    "message": f"Dosya analizi tamamlandı: {file_path}",
                    "analysis": analysis,
                    "raw_data": result
                }
            else:
                return {
                    "success": True,
                    "message": f"Dosya analizi tamamlandı: {file_path}",
                    "raw_data": result
                }
            
        except Exception as e:
            logger.error(f"Dosya analizi hatası: {e}")
            return {"error": str(e)}
        finally:
            await self.shutdown_system()
    
    async def config_command(self, action: str = "show", key: str = None, value: str = None) -> Dict[str, Any]:
        """Konfigürasyon komutunu çalıştır"""
        try:
            if action == "show":
                return {
                    "success": True,
                    "config": config.to_dict()
                }
            elif action == "get" and key:
                # Nested key desteği (örn: "vision.enable_camera")
                keys = key.split(".")
                current = config.to_dict()
                
                for k in keys:
                    if isinstance(current, dict) and k in current:
                        current = current[k]
                    else:
                        return {"error": f"Konfigürasyon anahtarı bulunamadı: {key}"}
                
                return {
                    "success": True,
                    "key": key,
                    "value": current
                }
            elif action == "set" and key and value:
                return {"error": "Konfigürasyon ayarlama henüz desteklenmiyor"}
            else:
                return {"error": "Geçersiz konfigürasyon komutu"}
                
        except Exception as e:
            logger.error(f"Konfigürasyon komutu hatası: {e}")
            return {"error": str(e)}
    
    async def health_command(self) -> Dict[str, Any]:
        """Sistem sağlık komutunu çalıştır"""
        try:
            if not self.coordinator:
                if not await self.initialize_system():
                    return {"error": "Sistem başlatılamadı"}
            
            # Health monitor'dan rapor al
            health_monitor = self.coordinator.health_monitor
            if not health_monitor:
                return {"error": "Health monitor mevcut değil"}
            
            report = health_monitor.get_health_report()
            
            return {
                "success": True,
                "message": "Sistem sağlık raporu",
                "report": report
            }
            
        except Exception as e:
            logger.error(f"Sağlık komutu hatası: {e}")
            return {"error": str(e)}
        finally:
            await self.shutdown_system()
    
    def print_result(self, result: Dict[str, Any], format_type: str = "json") -> None:
        """Sonucu yazdır"""
        try:
            if format_type == "json":
                print(json.dumps(result, indent=2, ensure_ascii=False))
            elif format_type == "simple":
                if "error" in result:
                    print(f"HATA: {result['error']}")
                elif "success" in result and result["success"]:
                    print(f"BAŞARILI: {result.get('message', 'İşlem tamamlandı')}")
                else:
                    print("Bilinmeyen sonuç")
            else:
                print(str(result))
                
        except Exception as e:
            print(f"Sonuç yazdırma hatası: {e}")


# CLI fonksiyonları
async def run_status():
    """Status komutunu çalıştır"""
    cli = CLICommands()
    result = await cli.status_command()
    cli.print_result(result, "simple")


async def run_test_camera():
    """Kamera test komutunu çalıştır"""
    cli = CLICommands()
    result = await cli.test_camera_command()
    cli.print_result(result, "simple")


async def run_test_screen():
    """Ekran test komutunu çalıştır"""
    cli = CLICommands()
    result = await cli.test_screen_command()
    cli.print_result(result, "simple")


async def run_test_context(query: str = None):
    """Context test komutunu çalıştır"""
    cli = CLICommands()
    result = await cli.test_context_command(query)
    cli.print_result(result, "simple")


async def run_analyze_file(file_path: str):
    """Dosya analizi komutunu çalıştır"""
    cli = CLICommands()
    result = await cli.analyze_file_command(file_path)
    cli.print_result(result, "simple")


async def run_config(action: str = "show", key: str = None, value: str = None):
    """Konfigürasyon komutunu çalıştır"""
    cli = CLICommands()
    result = await cli.config_command(action, key, value)
    cli.print_result(result, "json")


async def run_health():
    """Sağlık komutunu çalıştır"""
    cli = CLICommands()
    result = await cli.health_command()
    cli.print_result(result, "simple")
