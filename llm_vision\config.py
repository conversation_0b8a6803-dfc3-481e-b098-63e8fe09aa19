"""
Konfigürasyon yönetimi modülü
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, Field


class ServerConfig(BaseSettings):
    """Server konfigürasyonu"""
    host: str = Field(default="localhost", env="SERVER_HOST")
    port: int = Field(default=8000, env="SERVER_PORT")
    debug: bool = Field(default=False, env="DEBUG")
    
    class Config:
        env_file = ".env"


class MCPConfig(BaseSettings):
    """MCP protokol konfigürasyonu"""
    protocol_version: str = Field(default="2024-11-05", env="MCP_PROTOCOL_VERSION")
    server_name: str = Field(default="llm-vision-server", env="MCP_SERVER_NAME")
    server_version: str = Field(default="0.1.0", env="MCP_SERVER_VERSION")
    
    class Config:
        env_file = ".env"


class VisionConfig(BaseSettings):
    """<PERSON>ö<PERSON>ü<PERSON>ü işleme konfigürasyonu"""
    camera_index: int = Field(default=0, env="CAMERA_INDEX")
    camera_width: int = Field(default=640, env="CAMERA_WIDTH")
    camera_height: int = Field(default=480, env="CAMERA_HEIGHT")
    camera_fps: int = Field(default=30, env="CAMERA_FPS")
    
    class Config:
        env_file = ".env"


class ContextConfig(BaseSettings):
    """Context Engine konfigürasyonu"""
    model: str = Field(default="mistral-7b-instruct", env="CONTEXT_MODEL")
    api_key: Optional[str] = Field(default=None, env="CONTEXT_API_KEY")
    max_tokens: int = Field(default=2048, env="CONTEXT_MAX_TOKENS")
    
    class Config:
        env_file = ".env"


class SecurityConfig(BaseSettings):
    """Güvenlik konfigürasyonu"""
    secret_key: str = Field(default="dev-secret-key", env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    class Config:
        env_file = ".env"


class LoggingConfig(BaseSettings):
    """Loglama konfigürasyonu"""
    level: str = Field(default="INFO", env="LOG_LEVEL")
    file: str = Field(default="logs/llm_vision.log", env="LOG_FILE")
    
    class Config:
        env_file = ".env"


class PerformanceConfig(BaseSettings):
    """Performans konfigürasyonu"""
    max_concurrent_requests: int = Field(default=10, env="MAX_CONCURRENT_REQUESTS")
    request_timeout: int = Field(default=30, env="REQUEST_TIMEOUT")
    cache_ttl: int = Field(default=300, env="CACHE_TTL")
    
    class Config:
        env_file = ".env"


class FileSystemConfig(BaseSettings):
    """Dosya sistemi konfigürasyonu"""
    watch_directories: List[str] = Field(
        default=["./", "~/Documents", "~/Desktop"], 
        env="WATCH_DIRECTORIES"
    )
    max_file_size_mb: int = Field(default=10, env="MAX_FILE_SIZE_MB")
    
    class Config:
        env_file = ".env"


class OCRConfig(BaseSettings):
    """OCR konfigürasyonu"""
    tesseract_path: str = Field(default="/usr/bin/tesseract", env="TESSERACT_PATH")
    languages: List[str] = Field(default=["eng", "tur"], env="OCR_LANGUAGES")
    
    class Config:
        env_file = ".env"


class MemoryConfig(BaseSettings):
    """Hibrit hafıza sistemi konfigürasyonu"""
    # Redis (L1/L2 Cache)
    redis_host: str = Field(default="localhost", env="REDIS_HOST")
    redis_port: int = Field(default=6379, env="REDIS_PORT")
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")

    # Cache TTL'leri (saniye)
    l1_cache_ttl: int = Field(default=300, env="L1_CACHE_TTL")  # 5 dakika
    l2_session_ttl: int = Field(default=86400, env="L2_SESSION_TTL")  # 24 saat

    # SQLite (L3/L4 Storage)
    sqlite_path: str = Field(default="data/memory.db", env="SQLITE_PATH")
    l3_retention_days: int = Field(default=7, env="L3_RETENTION_DAYS")
    l4_retention_days: int = Field(default=90, env="L4_RETENTION_DAYS")

    # Archive (L5)
    archive_path: str = Field(default="data/archive", env="ARCHIVE_PATH")
    archive_retention_days: int = Field(default=365, env="ARCHIVE_RETENTION_DAYS")

    # Performans
    max_memory_mb: int = Field(default=500, env="MAX_MEMORY_MB")
    cleanup_interval_hours: int = Field(default=6, env="CLEANUP_INTERVAL_HOURS")

    class Config:
        env_file = ".env"


class PluginConfig(BaseSettings):
    """Plugin sistemi konfigürasyonu"""
    plugin_directory: str = Field(default="plugins", env="PLUGIN_DIRECTORY")
    enable_plugins: bool = Field(default=True, env="ENABLE_PLUGINS")
    plugin_timeout: int = Field(default=30, env="PLUGIN_TIMEOUT")
    max_plugins: int = Field(default=20, env="MAX_PLUGINS")

    # Core plugin'ler
    enable_vscode_plugin: bool = Field(default=True, env="ENABLE_VSCODE_PLUGIN")
    enable_browser_plugin: bool = Field(default=True, env="ENABLE_BROWSER_PLUGIN")
    enable_blender_plugin: bool = Field(default=False, env="ENABLE_BLENDER_PLUGIN")

    class Config:
        env_file = ".env"


class Config:
    """Ana konfigürasyon sınıfı"""

    def __init__(self):
        self.server = ServerConfig()
        self.mcp = MCPConfig()
        self.vision = VisionConfig()
        self.context = ContextConfig()
        self.security = SecurityConfig()
        self.logging = LoggingConfig()
        self.performance = PerformanceConfig()
        self.filesystem = FileSystemConfig()
        self.ocr = OCRConfig()
        self.memory = MemoryConfig()
        self.plugins = PluginConfig()


# Global konfigürasyon instance
config = Config()
