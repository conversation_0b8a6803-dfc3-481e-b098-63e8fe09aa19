"""
Kamera yönetimi modülü
"""

import cv2
import numpy as np
import asyncio
from typing import Op<PERSON>, Tu<PERSON>, List
from threading import Thread, Lock
import time

from ..config import config
from ..utils.logger import get_logger
from ..utils.exceptions import CameraError

logger = get_logger(__name__)


class CameraManager:
    """Kamera yönetici sınıfı"""
    
    def __init__(self, camera_index: int = None):
        self.camera_index = camera_index or config.vision.camera_index
        self.width = config.vision.camera_width
        self.height = config.vision.camera_height
        self.fps = config.vision.camera_fps
        
        self.cap: Optional[cv2.VideoCapture] = None
        self.is_running = False
        self.current_frame: Optional[np.ndarray] = None
        self.frame_lock = Lock()
        self.capture_thread: Optional[Thread] = None
        
        logger.info(f"Kamera yöneticisi başlatıldı - Index: {self.camera_index}")
    
    def start(self) -> bool:
        """<PERSON><PERSON><PERSON><PERSON> başlat"""
        try:
            if self.is_running:
                logger.warning("Kamera zaten çalışıyor")
                return True
            
            # Kamerayı aç
            self.cap = cv2.VideoCapture(self.camera_index)
            
            if not self.cap.isOpened():
                raise CameraError(f"Kamera açılamadı: {self.camera_index}")
            
            # Kamera ayarlarını yapılandır
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.height)
            self.cap.set(cv2.CAP_PROP_FPS, self.fps)
            
            # Gerçek ayarları kontrol et
            actual_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            actual_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            actual_fps = self.cap.get(cv2.CAP_PROP_FPS)
            
            logger.info(f"Kamera ayarları - Çözünürlük: {actual_width}x{actual_height}, FPS: {actual_fps}")
            
            # Yakalama thread'ini başlat
            self.is_running = True
            self.capture_thread = Thread(target=self._capture_loop, daemon=True)
            self.capture_thread.start()
            
            logger.info("Kamera başarıyla başlatıldı")
            return True
            
        except Exception as e:
            logger.error(f"Kamera başlatma hatası: {e}")
            self.stop()
            raise CameraError(f"Kamera başlatma hatası: {str(e)}")
    
    def stop(self) -> None:
        """Kamerayı durdur"""
        try:
            self.is_running = False
            
            if self.capture_thread and self.capture_thread.is_alive():
                self.capture_thread.join(timeout=2.0)
            
            if self.cap:
                self.cap.release()
                self.cap = None
            
            with self.frame_lock:
                self.current_frame = None
            
            logger.info("Kamera durduruldu")
            
        except Exception as e:
            logger.error(f"Kamera durdurma hatası: {e}")
    
    def _capture_loop(self) -> None:
        """Kamera yakalama döngüsü"""
        frame_time = 1.0 / self.fps
        
        while self.is_running and self.cap and self.cap.isOpened():
            try:
                start_time = time.time()
                
                ret, frame = self.cap.read()
                
                if ret and frame is not None:
                    with self.frame_lock:
                        self.current_frame = frame.copy()
                else:
                    logger.warning("Kameradan frame alınamadı")
                
                # FPS kontrolü
                elapsed = time.time() - start_time
                sleep_time = max(0, frame_time - elapsed)
                if sleep_time > 0:
                    time.sleep(sleep_time)
                    
            except Exception as e:
                logger.error(f"Frame yakalama hatası: {e}")
                break
    
    def get_frame(self) -> Optional[np.ndarray]:
        """Mevcut frame'i al"""
        with self.frame_lock:
            if self.current_frame is not None:
                return self.current_frame.copy()
            return None
    
    async def get_frame_async(self) -> Optional[np.ndarray]:
        """Async olarak frame al"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.get_frame
        )
    
    def capture_image(self) -> Optional[np.ndarray]:
        """Tek bir görüntü yakala"""
        if not self.is_running:
            # Geçici olarak kamerayı aç
            temp_cap = cv2.VideoCapture(self.camera_index)
            if not temp_cap.isOpened():
                logger.error("Geçici kamera açılamadı")
                return None
            
            try:
                ret, frame = temp_cap.read()
                if ret:
                    return frame
                else:
                    logger.error("Geçici kameradan frame alınamadı")
                    return None
            finally:
                temp_cap.release()
        else:
            return self.get_frame()
    
    async def capture_image_async(self) -> Optional[np.ndarray]:
        """Async olarak tek görüntü yakala"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.capture_image
        )
    
    def is_available(self) -> bool:
        """Kamera kullanılabilir mi kontrol et"""
        try:
            test_cap = cv2.VideoCapture(self.camera_index)
            available = test_cap.isOpened()
            test_cap.release()
            return available
        except Exception:
            return False
    
    def get_camera_info(self) -> dict:
        """Kamera bilgilerini al"""
        info = {
            "index": self.camera_index,
            "is_running": self.is_running,
            "is_available": self.is_available(),
            "configured_resolution": f"{self.width}x{self.height}",
            "configured_fps": self.fps
        }
        
        if self.cap and self.cap.isOpened():
            info.update({
                "actual_width": int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
                "actual_height": int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
                "actual_fps": self.cap.get(cv2.CAP_PROP_FPS),
                "backend": self.cap.getBackendName()
            })
        
        return info
    
    def __enter__(self):
        """Context manager giriş"""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager çıkış"""
        self.stop()
    
    def __del__(self):
        """Destructor"""
        self.stop()


class MultiCameraManager:
    """Çoklu kamera yöneticisi"""
    
    def __init__(self, camera_indices: List[int] = None):
        self.camera_indices = camera_indices or [0]
        self.cameras: dict[int, CameraManager] = {}
        
        logger.info(f"Çoklu kamera yöneticisi - Kameralar: {self.camera_indices}")
    
    def start_all(self) -> dict[int, bool]:
        """Tüm kameraları başlat"""
        results = {}
        
        for index in self.camera_indices:
            try:
                camera = CameraManager(index)
                success = camera.start()
                
                if success:
                    self.cameras[index] = camera
                    results[index] = True
                    logger.info(f"Kamera {index} başlatıldı")
                else:
                    results[index] = False
                    logger.warning(f"Kamera {index} başlatılamadı")
                    
            except Exception as e:
                logger.error(f"Kamera {index} hatası: {e}")
                results[index] = False
        
        return results
    
    def stop_all(self) -> None:
        """Tüm kameraları durdur"""
        for index, camera in self.cameras.items():
            try:
                camera.stop()
                logger.info(f"Kamera {index} durduruldu")
            except Exception as e:
                logger.error(f"Kamera {index} durdurma hatası: {e}")
        
        self.cameras.clear()
    
    def get_frame(self, camera_index: int) -> Optional[np.ndarray]:
        """Belirtilen kameradan frame al"""
        camera = self.cameras.get(camera_index)
        if camera:
            return camera.get_frame()
        return None
    
    async def get_frame_async(self, camera_index: int) -> Optional[np.ndarray]:
        """Async olarak frame al"""
        camera = self.cameras.get(camera_index)
        if camera:
            return await camera.get_frame_async()
        return None
    
    def get_all_frames(self) -> dict[int, Optional[np.ndarray]]:
        """Tüm kameralardan frame al"""
        frames = {}
        for index, camera in self.cameras.items():
            frames[index] = camera.get_frame()
        return frames
    
    async def get_all_frames_async(self) -> dict[int, Optional[np.ndarray]]:
        """Async olarak tüm frame'leri al"""
        tasks = []
        indices = []
        
        for index, camera in self.cameras.items():
            tasks.append(camera.get_frame_async())
            indices.append(index)
        
        frames_list = await asyncio.gather(*tasks)
        
        return dict(zip(indices, frames_list))
    
    def get_camera_info(self, camera_index: int = None) -> dict:
        """Kamera bilgilerini al"""
        if camera_index is not None:
            camera = self.cameras.get(camera_index)
            if camera:
                return camera.get_camera_info()
            return {"error": f"Kamera {camera_index} bulunamadı"}
        
        # Tüm kamera bilgileri
        info = {}
        for index, camera in self.cameras.items():
            info[index] = camera.get_camera_info()
        
        return info
    
    def __enter__(self):
        """Context manager giriş"""
        self.start_all()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager çıkış"""
        self.stop_all()
