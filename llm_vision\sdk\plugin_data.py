"""
Plugin Data Structures

Plugin'lerin veri alışverişinde kullandığı yapılar.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import json
import time

from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class PluginData:
    """Plugin veri yapısı"""
    source: str
    data: Dict[str, Any]
    timestamp: float = field(default_factory=time.time)
    confidence: float = 1.0
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Veri doğrulama"""
        if not self.source:
            raise ValueError("Source boş olamaz")
        if not isinstance(self.data, dict):
            raise ValueError("Data dictionary olmalıdır")
        if not 0.0 <= self.confidence <= 1.0:
            raise ValueError("Confidence 0.0-1.0 arasında olmalıdır")
    
    def to_dict(self) -> Dict[str, Any]:
        """Dictionary'e çevir"""
        return {
            "source": self.source,
            "data": self.data,
            "timestamp": self.timestamp,
            "confidence": self.confidence,
            "tags": self.tags,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "PluginData":
        """Dictionary'den oluştur"""
        return cls(
            source=data["source"],
            data=data["data"],
            timestamp=data.get("timestamp", time.time()),
            confidence=data.get("confidence", 1.0),
            tags=data.get("tags", []),
            metadata=data.get("metadata", {})
        )
    
    def add_tag(self, tag: str) -> None:
        """Tag ekle"""
        if tag not in self.tags:
            self.tags.append(tag)
    
    def remove_tag(self, tag: str) -> None:
        """Tag kaldır"""
        if tag in self.tags:
            self.tags.remove(tag)
    
    def has_tag(self, tag: str) -> bool:
        """Tag var mı kontrol et"""
        return tag in self.tags
    
    def set_metadata(self, key: str, value: Any) -> None:
        """Metadata ekle/güncelle"""
        self.metadata[key] = value
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """Metadata al"""
        return self.metadata.get(key, default)
    
    def age_seconds(self) -> float:
        """Verinin yaşını saniye cinsinden döndür"""
        return time.time() - self.timestamp
    
    def is_fresh(self, max_age_seconds: float) -> bool:
        """Veri taze mi kontrol et"""
        return self.age_seconds() <= max_age_seconds


@dataclass
class PluginResponse:
    """Plugin yanıt yapısı"""
    success: bool
    data: Optional[PluginData] = None
    error_message: Optional[str] = None
    error_code: Optional[str] = None
    execution_time_ms: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Dictionary'e çevir"""
        result = {
            "success": self.success,
            "execution_time_ms": self.execution_time_ms,
            "metadata": self.metadata
        }
        
        if self.data:
            result["data"] = self.data.to_dict()
        
        if self.error_message:
            result["error_message"] = self.error_message
        
        if self.error_code:
            result["error_code"] = self.error_code
        
        return result
    
    @classmethod
    def success_response(cls, data: PluginData, execution_time_ms: float = 0.0) -> "PluginResponse":
        """Başarılı yanıt oluştur"""
        return cls(
            success=True,
            data=data,
            execution_time_ms=execution_time_ms
        )
    
    @classmethod
    def error_response(cls, error_message: str, error_code: str = None, execution_time_ms: float = 0.0) -> "PluginResponse":
        """Hata yanıtı oluştur"""
        return cls(
            success=False,
            error_message=error_message,
            error_code=error_code,
            execution_time_ms=execution_time_ms
        )


@dataclass
class PluginEvent:
    """Plugin event yapısı"""
    event_type: str
    source_plugin: str
    data: Dict[str, Any]
    timestamp: float = field(default_factory=time.time)
    priority: str = "medium"  # low, medium, high, critical
    correlation_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Dictionary'e çevir"""
        return {
            "event_type": self.event_type,
            "source_plugin": self.source_plugin,
            "data": self.data,
            "timestamp": self.timestamp,
            "priority": self.priority,
            "correlation_id": self.correlation_id
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "PluginEvent":
        """Dictionary'den oluştur"""
        return cls(
            event_type=data["event_type"],
            source_plugin=data["source_plugin"],
            data=data["data"],
            timestamp=data.get("timestamp", time.time()),
            priority=data.get("priority", "medium"),
            correlation_id=data.get("correlation_id")
        )


@dataclass
class PluginMetrics:
    """Plugin performans metrikleri"""
    plugin_name: str
    calls_count: int = 0
    success_count: int = 0
    error_count: int = 0
    total_execution_time_ms: float = 0.0
    average_execution_time_ms: float = 0.0
    last_call_timestamp: Optional[float] = None
    last_error_timestamp: Optional[float] = None
    last_error_message: Optional[str] = None
    
    def update_call(self, success: bool, execution_time_ms: float, error_message: str = None) -> None:
        """Çağrı metriklerini güncelle"""
        self.calls_count += 1
        self.last_call_timestamp = time.time()
        self.total_execution_time_ms += execution_time_ms
        
        if success:
            self.success_count += 1
        else:
            self.error_count += 1
            self.last_error_timestamp = time.time()
            self.last_error_message = error_message
        
        # Ortalama hesapla
        if self.calls_count > 0:
            self.average_execution_time_ms = self.total_execution_time_ms / self.calls_count
    
    def get_success_rate(self) -> float:
        """Başarı oranını döndür"""
        if self.calls_count == 0:
            return 0.0
        return self.success_count / self.calls_count
    
    def get_error_rate(self) -> float:
        """Hata oranını döndür"""
        if self.calls_count == 0:
            return 0.0
        return self.error_count / self.calls_count
    
    def to_dict(self) -> Dict[str, Any]:
        """Dictionary'e çevir"""
        return {
            "plugin_name": self.plugin_name,
            "calls_count": self.calls_count,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "success_rate": self.get_success_rate(),
            "error_rate": self.get_error_rate(),
            "total_execution_time_ms": self.total_execution_time_ms,
            "average_execution_time_ms": self.average_execution_time_ms,
            "last_call_timestamp": self.last_call_timestamp,
            "last_error_timestamp": self.last_error_timestamp,
            "last_error_message": self.last_error_message
        }
