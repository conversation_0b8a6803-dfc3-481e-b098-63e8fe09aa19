"""
Prompt yönetimi modü<PERSON>
"""

import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

from ..utils.logger import get_logger
from ..utils.exceptions import ContextError

logger = get_logger(__name__)


class PromptTemplate:
    """Prompt şablonu sınıfı"""
    
    def __init__(self, 
                 name: str,
                 template: str,
                 variables: List[str] = None,
                 description: str = "",
                 category: str = "general"):
        self.name = name
        self.template = template
        self.variables = variables or []
        self.description = description
        self.category = category
        self.created_at = datetime.now()
    
    def render(self, **kwargs) -> str:
        """Şablonu değişkenlerle render et"""
        try:
            # Eksik değişkenleri kontrol et
            missing_vars = [var for var in self.variables if var not in kwargs]
            if missing_vars:
                logger.warning(f"Eksik değişkenler: {missing_vars}")
            
            # Template'i render et
            return self.template.format(**kwargs)
            
        except KeyError as e:
            raise ContextError(f"Prompt template render hatası: {e}")
    
    def to_dict(self) -> dict:
        """Sözlük formatına çevir"""
        return {
            "name": self.name,
            "template": self.template,
            "variables": self.variables,
            "description": self.description,
            "category": self.category,
            "created_at": self.created_at.isoformat()
        }


class PromptManager:
    """Prompt yönetici sınıfı"""
    
    def __init__(self):
        self.templates: Dict[str, PromptTemplate] = {}
        self.categories = set()
        
        # Varsayılan şablonları yükle
        self._load_default_templates()
        
        logger.info("Prompt yöneticisi başlatıldı")
    
    def _load_default_templates(self) -> None:
        """Varsayılan prompt şablonlarını yükle"""
        
        # Görüntü analizi şablonları
        self.add_template(PromptTemplate(
            name="vision_analysis",
            template="""Aşağıdaki görüntü analizi verilerini incele ve özetle:

Tespit edilen nesneler: {objects}
Bulunan metinler: {texts}
Görüntü boyutu: {image_size}
Güven skorları: {confidence_scores}

Lütfen şunları yap:
1. Görüntüde ne olduğunu açıkla
2. Önemli nesneleri listele
3. Metin içeriğini özetle
4. Genel değerlendirme yap

Türkçe yanıt ver.""",
            variables=["objects", "texts", "image_size", "confidence_scores"],
            description="Görüntü analizi sonuçlarını özetlemek için",
            category="vision"
        ))
        
        self.add_template(PromptTemplate(
            name="object_detection_summary",
            template="""Görüntüde {object_count} nesne tespit edildi:

{object_list}

En yüksek güven skoru: {max_confidence}
Ortalama güven skoru: {avg_confidence}

Bu nesnelerin genel anlamını ve görüntünün içeriğini Türkçe olarak açıkla.""",
            variables=["object_count", "object_list", "max_confidence", "avg_confidence"],
            description="Nesne tanıma sonuçlarını özetlemek için",
            category="vision"
        ))
        
        # Dosya analizi şablonları
        self.add_template(PromptTemplate(
            name="file_analysis",
            template="""Dosya analizi sonuçları:

Dosya: {file_path}
Boyut: {file_size}
Tür: {file_type}
Son değişiklik: {modified_time}

İçerik analizi:
{content_analysis}

Güvenlik durumu:
{security_info}

Bu dosya hakkında kapsamlı bir değerlendirme yap ve Türkçe özetle.""",
            variables=["file_path", "file_size", "file_type", "modified_time", "content_analysis", "security_info"],
            description="Dosya analizi sonuçlarını özetlemek için",
            category="file"
        ))
        
        # Sistem analizi şablonları
        self.add_template(PromptTemplate(
            name="system_status",
            template="""Sistem durumu raporu:

CPU Kullanımı: {cpu_usage}%
Bellek Kullanımı: {memory_usage}%
Disk Kullanımı: {disk_usage}%

Platform: {platform}
Çalışma süresi: {uptime}

Son dosya değişiklikleri: {recent_files}

Sistem performansını değerlendir ve öneriler sun. Türkçe yanıt ver.""",
            variables=["cpu_usage", "memory_usage", "disk_usage", "platform", "uptime", "recent_files"],
            description="Sistem durumunu özetlemek için",
            category="system"
        ))
        
        # Genel analiz şablonları
        self.add_template(PromptTemplate(
            name="general_context",
            template="""Mevcut durum özeti:

Zaman: {timestamp}
Veri kaynakları: {data_sources}
Toplam veri: {data_count} öğe

Son aktiviteler:
{recent_activities}

Önemli değişiklikler:
{important_changes}

Bu bilgileri kullanarak kullanıcının sorularını yanıtlamak için bağlam oluştur. Türkçe açıkla.""",
            variables=["timestamp", "data_sources", "data_count", "recent_activities", "important_changes"],
            description="Genel bağlam oluşturmak için",
            category="general"
        ))
        
        # Hata analizi şablonları
        self.add_template(PromptTemplate(
            name="error_analysis",
            template="""Hata analizi:

Hata türü: {error_type}
Hata mesajı: {error_message}
Oluşma zamanı: {error_time}
Etkilenen bileşen: {component}

Sistem durumu:
{system_state}

Bu hatanın nedenlerini analiz et ve çözüm önerileri sun. Türkçe açıkla.""",
            variables=["error_type", "error_message", "error_time", "component", "system_state"],
            description="Hata durumlarını analiz etmek için",
            category="error"
        ))
        
        # API veri analizi şablonları
        self.add_template(PromptTemplate(
            name="api_data_summary",
            template="""API veri özeti:

Kaynak: {api_source}
Veri türü: {data_type}
Toplam kayıt: {record_count}
Son güncelleme: {last_update}

Veri içeriği:
{data_content}

Bu API verilerini analiz et ve önemli bilgileri özetle. Türkçe yanıt ver.""",
            variables=["api_source", "data_type", "record_count", "last_update", "data_content"],
            description="API verilerini özetlemek için",
            category="api"
        ))
    
    def add_template(self, template: PromptTemplate) -> None:
        """Yeni şablon ekle"""
        self.templates[template.name] = template
        self.categories.add(template.category)
        logger.debug(f"Prompt şablonu eklendi: {template.name}")
    
    def get_template(self, name: str) -> Optional[PromptTemplate]:
        """Şablon al"""
        return self.templates.get(name)
    
    def list_templates(self, category: str = None) -> List[PromptTemplate]:
        """Şablonları listele"""
        templates = list(self.templates.values())
        
        if category:
            templates = [t for t in templates if t.category == category]
        
        return sorted(templates, key=lambda x: x.name)
    
    def get_categories(self) -> List[str]:
        """Kategorileri al"""
        return sorted(list(self.categories))
    
    def render_template(self, name: str, **kwargs) -> str:
        """Şablonu render et"""
        template = self.get_template(name)
        if not template:
            raise ContextError(f"Şablon bulunamadı: {name}")
        
        return template.render(**kwargs)
    
    def create_vision_prompt(self, 
                           objects: List[Dict[str, Any]],
                           texts: List[Dict[str, Any]],
                           image_info: Dict[str, Any]) -> str:
        """Görüntü analizi promptu oluştur"""
        try:
            # Nesne listesi oluştur
            object_list = []
            confidence_scores = []
            
            for obj in objects:
                object_list.append(f"- {obj.get('class_name', 'unknown')} (güven: {obj.get('confidence', 0):.2f})")
                confidence_scores.append(obj.get('confidence', 0))
            
            # Metin listesi oluştur
            text_list = []
            for text in texts:
                if text.get('text'):
                    text_list.append(f"- {text['text'][:50]}...")
            
            # Şablonu render et
            return self.render_template(
                "vision_analysis",
                objects="\n".join(object_list) if object_list else "Nesne bulunamadı",
                texts="\n".join(text_list) if text_list else "Metin bulunamadı",
                image_size=f"{image_info.get('width', 0)}x{image_info.get('height', 0)}",
                confidence_scores=f"Max: {max(confidence_scores):.2f}, Ort: {sum(confidence_scores)/len(confidence_scores):.2f}" if confidence_scores else "N/A"
            )
            
        except Exception as e:
            logger.error(f"Vision prompt oluşturma hatası: {e}")
            raise ContextError(f"Vision prompt oluşturma hatası: {str(e)}")
    
    def create_file_prompt(self, file_analysis: Dict[str, Any]) -> str:
        """Dosya analizi promptu oluştur"""
        try:
            basic_info = file_analysis.get("basic_info", {})
            content_analysis = file_analysis.get("content_analysis", {})
            security_info = file_analysis.get("security_info", {})
            
            return self.render_template(
                "file_analysis",
                file_path=basic_info.get("path", "unknown"),
                file_size=basic_info.get("size_human", "unknown"),
                file_type=basic_info.get("mime_type", "unknown"),
                modified_time=basic_info.get("modified_time", "unknown"),
                content_analysis=json.dumps(content_analysis, indent=2, ensure_ascii=False),
                security_info=json.dumps(security_info, indent=2, ensure_ascii=False)
            )
            
        except Exception as e:
            logger.error(f"File prompt oluşturma hatası: {e}")
            raise ContextError(f"File prompt oluşturma hatası: {str(e)}")
    
    def create_system_prompt(self, system_data: Dict[str, Any]) -> str:
        """Sistem durumu promptu oluştur"""
        try:
            platform_info = system_data.get("platform", {})
            cpu_info = system_data.get("cpu", {})
            memory_info = system_data.get("memory", {})
            disk_info = system_data.get("disk", {})
            
            return self.render_template(
                "system_status",
                cpu_usage=cpu_info.get("usage", 0),
                memory_usage=memory_info.get("usage", 0),
                disk_usage=disk_info.get("usage", 0),
                platform=f"{platform_info.get('system', 'unknown')} {platform_info.get('release', '')}",
                uptime="N/A",  # Bu bilgi eklenmeli
                recent_files="Son dosya değişiklikleri analiz edilecek"
            )
            
        except Exception as e:
            logger.error(f"System prompt oluşturma hatası: {e}")
            raise ContextError(f"System prompt oluşturma hatası: {str(e)}")
    
    def create_context_prompt(self, 
                            data_summary: Dict[str, Any],
                            recent_activities: List[Dict[str, Any]]) -> str:
        """Genel bağlam promptu oluştur"""
        try:
            # Son aktiviteleri formatla
            activity_list = []
            for activity in recent_activities[:5]:  # Son 5 aktivite
                activity_list.append(f"- {activity.get('type', 'unknown')}: {activity.get('timestamp', 'unknown')}")
            
            # Önemli değişiklikleri belirle
            important_changes = []
            if data_summary.get("new_files", 0) > 0:
                important_changes.append(f"- {data_summary['new_files']} yeni dosya")
            if data_summary.get("modified_files", 0) > 0:
                important_changes.append(f"- {data_summary['modified_files']} değiştirilmiş dosya")
            
            return self.render_template(
                "general_context",
                timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                data_sources=", ".join(data_summary.get("sources", [])),
                data_count=data_summary.get("total_items", 0),
                recent_activities="\n".join(activity_list) if activity_list else "Aktivite bulunamadı",
                important_changes="\n".join(important_changes) if important_changes else "Önemli değişiklik yok"
            )
            
        except Exception as e:
            logger.error(f"Context prompt oluşturma hatası: {e}")
            raise ContextError(f"Context prompt oluşturma hatası: {str(e)}")
    
    def save_templates(self, file_path: str) -> bool:
        """Şablonları dosyaya kaydet"""
        try:
            templates_data = {
                name: template.to_dict() 
                for name, template in self.templates.items()
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(templates_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Şablonlar kaydedildi: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Şablon kaydetme hatası: {e}")
            return False
    
    def load_templates(self, file_path: str) -> bool:
        """Şablonları dosyadan yükle"""
        try:
            if not Path(file_path).exists():
                logger.warning(f"Şablon dosyası bulunamadı: {file_path}")
                return False
            
            with open(file_path, 'r', encoding='utf-8') as f:
                templates_data = json.load(f)
            
            for name, data in templates_data.items():
                template = PromptTemplate(
                    name=data["name"],
                    template=data["template"],
                    variables=data.get("variables", []),
                    description=data.get("description", ""),
                    category=data.get("category", "general")
                )
                self.add_template(template)
            
            logger.info(f"Şablonlar yüklendi: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Şablon yükleme hatası: {e}")
            return False
    
    def get_template_stats(self) -> Dict[str, Any]:
        """Şablon istatistiklerini al"""
        category_counts = {}
        for template in self.templates.values():
            category_counts[template.category] = category_counts.get(template.category, 0) + 1
        
        return {
            "total_templates": len(self.templates),
            "categories": list(self.categories),
            "category_counts": category_counts,
            "template_names": list(self.templates.keys())
        }
