# 🧠 Agentic Context-Aware LLM System v2.0

## 📌 Amaç

<PERSON> sistem, bir LLM'in hem **kullanıcıdan gelen aktif sorguları** hem de **sürekli çevresel gözlemleri** kullanarak **bağlam<PERSON> farkındalıkla çalışan** proaktif bir <PERSON> (Proto-AGI) ajanı olmasını hedefler. Sistem modülerdir, çok kaynaklı veri girişi sağ<PERSON>, analiz edebilir ve gerektiğinde aksiyon alabilir.

---

## 🏛️ Mimarinin Temel Katmanları

### 1. **Sensory & Data Collection Layer (MCP Server)**

Gerçek dünya ve sanal kaynaklardan sürekli veri toplar:

| Kaynak             | Görev                           | Plugin API |
| ------------------ | ------------------------------- | ---------- |
| `camera_feed`      | <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> verisi / nesne alg<PERSON> | ✅ Pluggable |
| `filesystem_watch` | Son a<PERSON>, de<PERSON><PERSON><PERSON> | ✅ Pluggable |
| `active_window`    | Kullanıcının şu anki uygulaması | ✅ Pluggable |
| `browser_state`    | Tarayıcı sekmeleri              | ✅ Pluggable |
| `microphone_input` | (isteğe bağlı) Ses ortamı       | ✅ Pluggable |
| `custom_sensors`   | Üçüncü parti sensör entegrasyonu | ✅ Plugin SDK |

> MCP FastAPI sunucusu olarak çalışır. `/collect` endpoint'i tüm sensör verilerini toplar ve JSON olarak döner.

#### Plugin SDK Mimarisi:
```python
class BaseSensorPlugin:
    def initialize(self) -> bool
    def collect_data(self) -> Dict[str, Any]
    def get_schema(self) -> Dict[str, Any]
    def cleanup(self) -> None
```

---

### 2. **Perception Observer Loop**

* Arka planda sürekli çalışır (örn. `observer.py`)
* **Event-driven** + **WebSocket** tabanlı gerçek zamanlı veri akışı
* Debouncing mekanizması ile gereksiz güncellemeleri önler
* Veride bir değişiklik varsa, Context Engine'e gönderir
* Fallback mekanizması ile sensör hatalarını yönetir

```mermaid
graph LR
    MCP["MCP Server"] --> |WebSocket| Loop["Observer Loop"]
    Loop --> |Debounced Events| ContextEngine
    ContextEngine --> |Structured Context| Claude
    Loop --> |Fallback Mode| Claude
```

---

### 3. **Hibrit Hafıza Sistemi**

#### Katmanlı Hafıza Mimarisi:

| Katman | Teknoloji | Saklama Süresi | Kullanım Amacı |
|--------|-----------|---------------|----------------|
| **L1 Cache** | Redis | 5 dakika | Gerçek zamanlı context |
| **L2 Session** | Redis | 24 saat | Oturum bazlı bağlam |
| **L3 Short-term** | SQLite | 7 gün | Kısa dönem pattern'ler |
| **L4 Long-term** | SQLite | 90 gün | Uzun dönem öğrenme |
| **L5 Archive** | File System | 1 yıl | Pasif analiz |

#### Veri Yaşam Döngüsü:
```python
class MemoryLifecycle:
    def store_context(self, data: Dict, priority: str):
        # L1: Aktif bağlam
        redis.setex(f"context:{timestamp}", 300, data)
        
        # L2: Oturum verisi
        if priority == "high":
            redis.setex(f"session:{user_id}", 86400, data)
        
        # L3: Kısa dönem
        if self.is_significant(data):
            sqlite.insert("short_term_memory", data, ttl=7*24*3600)
    
    def cleanup_expired(self):
        # Otomatik temizleme stratejisi
        pass
```

---

### 4. **Context Engine (Prompt Layer / LLM)**

Gelen verileri doğal dil bağlama çevirir. Structured data formatı kullanır:

```json
{
  "context_snapshot": {
    "timestamp": "2024-01-15T14:30:00Z",
    "environment": {
      "visual": ["masa", "laptop", "kahve_bardağı"],
      "active_file": {
        "name": "project.py",
        "type": "python",
        "content_summary": "OpenCV ile nesne takip kodu"
      },
      "user_activity": "kod_yazma"
    },
    "memory_context": {
      "recent_patterns": ["python_geliştirme", "opencv_kullanımı"],
      "session_goal": "nesne_takip_projesi"
    }
  }
}
```

**Template-based Prompt Generation:**

```txt
# Bağlam Analizi
Zaman: {timestamp}
Görsel Ortam: {visual_objects}
Aktif Dosya: {filename} ({file_type})
Kod Özeti: {code_summary}
Kullanıcı Aktivitesi: {activity_type}

# Geçmiş Bağlam
Son Aktiviteler: {recent_patterns}
Oturum Hedefi: {session_goal}

# Analiz Görevleri
1. Mevcut durumu değerlendir
2. Kullanıcı ihtiyaçlarını tahmin et
3. Proaktif öneriler sun
4. Kritik noktalara dikkat çek
```

---

### 5. **Ana LLM (Claude, GPT, Grok)**

* Kullanıcı etkileşimlerini işler
* Hibrit hafıza sisteminden gelen bağlamı kullanır
* Structured context'i doğal dil yanıtlarına dönüştürür
* Gerekirse aksiyon başlatır (örn. kod önerisi, dosya açma)
* Caching mekanizması ile benzer durumlarda LLM çağrısı yapmaz

---

## 🔌 Plugin Ekosistemi

### Core Plugin Types:

| Plugin Tipi | Açıklama | Örnek Implementasyonlar |
|-------------|----------|-------------------------|
| **Sensor Plugins** | Veri toplama | Camera, Microphone, IoT sensors |
| **Context Plugins** | Bağlam analizi | Code analyzer, Document parser |
| **Action Plugins** | Aksiyon alma | File operations, API calls |
| **Memory Plugins** | Hafıza yönetimi | Vector DB, Graph DB |

### Plugin Manifest:
```yaml
plugin:
  name: "VSCode Integration"
  version: "1.0.0"
  type: "sensor"
  dependencies: ["filesystem_watch"]
  permissions: ["read_workspace", "read_extensions"]
  endpoints:
    - "/vscode/active_file"
    - "/vscode/debug_state"
    - "/vscode/git_status"
```

### Plugin SDK:
```python
from agentic_sdk import BaseSensorPlugin, PluginRegistry

class VSCodePlugin(BaseSensorPlugin):
    def collect_data(self) -> Dict[str, Any]:
        return {
            "active_file": self.get_active_file(),
            "git_status": self.get_git_status(),
            "debug_state": self.get_debug_state()
        }
    
    def get_schema(self) -> Dict[str, Any]:
        return {
            "properties": {
                "active_file": {"type": "string"},
                "git_status": {"type": "object"},
                "debug_state": {"type": "boolean"}
            }
        }

# Plugin Registration
PluginRegistry.register("vscode", VSCodePlugin)
```

---

## 🔄 Veri Akışı (Event-Driven Karma Mod)

```mermaid
sequenceDiagram
    participant User
    participant Claude
    participant ObserverLoop
    participant MCP
    participant ContextEngine
    participant Memory

    User->>Claude: Soru sorar ("Dosyayı analiz et")
    Claude->>Memory: Bağlam geçmişi al
    Memory-->>Claude: L1+L2 Cache data
    Claude->>MCP: Güncel veri iste
    MCP-->>Claude: Structured data
    Claude-->>User: Bağlamlı yanıt
    
    Note over Memory: Yanıt L1/L2'ye kaydedilir

    loop Event-Driven Observation
        MCP->>ObserverLoop: WebSocket event
        ObserverLoop->>ObserverLoop: Debounce control
        ObserverLoop->>ContextEngine: Significant change
        ContextEngine->>Memory: Store context
        ContextEngine-->>Claude: Context update
        
        alt Hafıza Temizleme
            Memory->>Memory: Lifecycle cleanup
        end
    end
```

---

## 🧱 Revize Edilmiş Sistem Yapısı

```
agentic-context-system/
├── mcp_server/
│   ├── main.py (FastAPI + WebSocket sunucu)
│   ├── core_sensors/
│   │   ├── camera.py
│   │   ├── files.py
│   │   └── active_window.py
│   └── plugin_loader/
│       ├── registry.py
│       ├── sdk.py
│       └── validator.py
├── plugins/
│   ├── vscode/
│   │   ├── plugin.py
│   │   └── manifest.yml
│   ├── blender/
│   └── browser/
├── observer_loop/
│   ├── observer.py (WebSocket client)
│   ├── debouncer.py
│   └── event_processor.py
├── memory_system/
│   ├── hibrit_memory.py
│   ├── lifecycle_manager.py
│   ├── redis_layer.py
│   └── sqlite_layer.py
├── context_engine/
│   ├── structured_context.py
│   ├── template_generator.py
│   └── prompt_builder.py
├── agent_bridge/
│   ├── claude_connector.py
│   ├── caching_layer.py
│   └── fallback_handler.py
├── sdk/
│   ├── __init__.py
│   ├── base_plugin.py
│   ├── registry.py
│   └── examples/
├── config/
│   ├── memory_config.yml
│   ├── plugin_config.yml
│   └── security_config.yml
├── logs/
│   └── context.log
└── README.md
```

---

## 🛡️ Güvenlik ve Performans

| Alan | Önlem / Öneri | Implementasyon |
|------|---------------|----------------|
| **Gecikme** | WebSocket + Event-driven | Debouncing, Caching |
| **Güvenlik** | Plugin sandboxing | Permission system |
| **CPU** | Intelligent polling | Event-based triggers |
| **Hafıza** | Lifecycle management | Hibrit katmanlı sistem |
| **Ölçekleme** | Plugin ekosistemi | Modüler mimari |

---

## 🧪 Örnek Test Senaryosu

**Komut:**
> "Çalışma ortamımda ne var? Kod dosyamı analiz eder misin?"

**Arka Plan (Structured Context):**
```json
{
  "visual_environment": ["masa", "laptop", "kahve_fincanı"],
  "active_file": {
    "name": "object_tracker.py",
    "language": "python",
    "imports": ["cv2", "numpy"],
    "functions": ["track_object", "detect_motion"]
  },
  "memory_context": {
    "recent_files": ["opencv_tutorial.py", "camera_test.py"],
    "session_pattern": "computer_vision_development"
  }
}
```

**Claude Yanıtı (Hibrit Hafıza Destekli):**
> Masa başında bilgisayar görüşü projesinde çalıştığınızı görüyorum. `object_tracker.py` dosyanızda OpenCV kullanarak nesne takibi yapıyorsunuz. Geçmiş dosyalarınıza bakınca bu konuda deneyimli olduğunuz anlaşılıyor. 
> 
> Kodunuzda `boundingRect` optimizasyonu ve kalman filtresi eklenmesi performansı artırabilir. Yardımcı olayım mı?

---

## 🚀 Geliştirme Yol Haritası

| Aşama | Açıklama | Öncelik |
|-------|----------|---------|
| ✅ **V0.1** | MCP + WebSocket + Basit hafıza | 🔴 Kritik |
| 🧠 **V0.2** | Hibrit hafıza + Lifecycle management | 🔴 Kritik |
| 🔌 **V0.3** | Plugin SDK + Core plugin'ler | 🟡 Önemli |
| 🎯 **V0.4** | Structured context + Template system | 🟡 Önemli |
| 🤖 **V0.5** | Event-driven optimization | 🟢 İyileştirme |
| 🦾 **V1.0** | Tam otonom Proto-AGI ajanı | 🟢 Vizyon |

---

## 📦 Teknoloji Stack'i

| Katman | Teknoloji | Versiyon |
|--------|-----------|----------|
| **MCP Server** | FastAPI + WebSocket | 3.8+ |
| **L1/L2 Cache** | Redis | 6.0+ |
| **L3/L4 Storage** | SQLite | 3.35+ |
| **Image Processing** | OpenCV | 4.5+ |
| **Plugin System** | Python importlib | 3.8+ |
| **LLM Interface** | Claude API | Latest |
| **Monitoring** | Prometheus + Grafana | Optional |

---

## 🎯 Vizyon Cümlesi

> **Bu sistem, yapay zekayı yalnızca soru-cevap veren bir araç olmaktan çıkarıp; çevresini sürekli izleyen, geçmişten öğrenen, geleceği tahmin eden ve gerektiğinde proaktif hareket eden; hem bireysel hem de ekosistem düzeyinde genişletilebilir dijital bir zihne dönüştürür.**

---

## 📊 Performans Metrikleri

| Metrik | Hedef | Ölçüm Yöntemi |
|--------|-------|---------------|
| **Context Relevance** | >85% | Kullanıcı feedback |
| **Response Time** | <2 saniye | End-to-end latency |
| **Memory Efficiency** | <500MB | RAM usage monitoring |
| **Plugin Compatibility** | 95% | Automated testing |
| **User Satisfaction** | >4.5/5 | Weekly survey |

---

## 🔧 Kurulum ve Başlatma

```bash
# Repository klonla
git clone https://github.com/user/agentic-context-system.git
cd agentic-context-system

# Bağımlılıkları yükle
pip install -r requirements.txt

# Redis başlat
docker run -d -p 6379:6379 redis:latest

# Sistem başlat
python main.py --config config/production.yml
```

---

Bu revize edilmiş mimari, önerdiğiniz hibrit hafıza sistemi ve plugin ekosistemi ile gerçek bir Proto-AGI ajanı yaratmak için gerekli tüm bileşenleri içermektedir.