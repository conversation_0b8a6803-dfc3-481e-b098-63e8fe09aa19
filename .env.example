# LLM Vision System Configuration

# Server Configuration
SERVER_HOST=localhost
SERVER_PORT=8000
DEBUG=true

# MCP Configuration
MCP_PROTOCOL_VERSION=2024-11-05
MCP_SERVER_NAME=llm-vision-server
MCP_SERVER_VERSION=0.1.0

# Vision Configuration
CAMERA_INDEX=0
CAMERA_WIDTH=640
CAMERA_HEIGHT=480
CAMERA_FPS=30

# Context Engine Configuration
CONTEXT_MODEL=mistral-7b-instruct
CONTEXT_API_KEY=your_api_key_here
CONTEXT_MAX_TOKENS=2048

# Security Configuration
SECRET_KEY=your_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/llm_vision.log

# Performance Configuration
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=30
CACHE_TTL=300

# File System Configuration
WATCH_DIRECTORIES=./,~/Documents,~/Desktop
MAX_FILE_SIZE_MB=10

# OCR Configuration
TESSERACT_PATH=/usr/bin/tesseract
OCR_LANGUAGES=eng,tur
