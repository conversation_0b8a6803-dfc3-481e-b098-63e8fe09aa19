"""
Veri toplama modülü
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
import os

from ..utils.logger import get_logger
from ..utils.exceptions import LLMVisionError
from .file_monitor import FileMonitor, FileChangeEvent
from .file_analyzer import FileAnalyzer
from .api_integrator import APIIntegrator

logger = get_logger(__name__)


class DataCollector:
    """Veri toplama sınıfı"""
    
    def __init__(self):
        self.file_monitor = FileMonitor()
        self.file_analyzer = FileAnalyzer()
        self.api_integrator = APIIntegrator()
        
        self.is_collecting = False
        self.collected_data = []
        self.data_callbacks: List[Callable[[Dict[str, Any]], None]] = []
        self.max_data_entries = 10000
        
        logger.info("Veri toplayıcı başlatıldı")
    
    async def initialize(self) -> None:
        """Veri toplayıcıyı başlat"""
        try:
            # API entegratörü başlat
            await self.api_integrator.initialize()
            
            # Dosya izleyici callback'i ekle
            self.file_monitor.add_callback(self._handle_file_change)
            
            logger.info("Veri toplayıcı başlatıldı")
            
        except Exception as e:
            logger.error(f"Veri toplayıcı başlatma hatası: {e}")
            raise LLMVisionError(f"Veri toplayıcı başlatma hatası: {str(e)}")
    
    async def shutdown(self) -> None:
        """Veri toplayıcıyı kapat"""
        try:
            await self.stop_collection()
            await self.api_integrator.shutdown()
            
            logger.info("Veri toplayıcı kapatıldı")
            
        except Exception as e:
            logger.error(f"Veri toplayıcı kapatma hatası: {e}")
    
    def add_data_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """Veri callback'i ekle"""
        self.data_callbacks.append(callback)
    
    def remove_data_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """Veri callback'ini kaldır"""
        if callback in self.data_callbacks:
            self.data_callbacks.remove(callback)
    
    async def start_collection(self) -> bool:
        """Veri toplamayı başlat"""
        try:
            if self.is_collecting:
                logger.warning("Veri toplama zaten aktif")
                return True
            
            # Dosya izlemeyi başlat
            monitor_success = self.file_monitor.start_monitoring()
            if not monitor_success:
                logger.error("Dosya izleme başlatılamadı")
                return False
            
            self.is_collecting = True
            logger.info("Veri toplama başlatıldı")
            return True
            
        except Exception as e:
            logger.error(f"Veri toplama başlatma hatası: {e}")
            return False
    
    async def stop_collection(self) -> None:
        """Veri toplamayı durdur"""
        try:
            self.is_collecting = False
            self.file_monitor.stop_monitoring()
            
            logger.info("Veri toplama durduruldu")
            
        except Exception as e:
            logger.error(f"Veri toplama durdurma hatası: {e}")
    
    def _handle_file_change(self, event: FileChangeEvent) -> None:
        """Dosya değişiklik olayını işle"""
        try:
            # Veri girişi oluştur
            data_entry = {
                "type": "file_change",
                "timestamp": datetime.now().isoformat(),
                "source": "file_monitor",
                "data": event.to_dict()
            }
            
            # Veriyi kaydet
            self._store_data(data_entry)
            
            # Async analiz başlat (fire-and-forget)
            if event.event_type in ["created", "modified"]:
                asyncio.create_task(self._analyze_file_async(event.file_path))
            
        except Exception as e:
            logger.error(f"Dosya değişiklik işleme hatası: {e}")
    
    async def _analyze_file_async(self, file_path: str) -> None:
        """Dosyayı async olarak analiz et"""
        try:
            if not os.path.exists(file_path):
                return
            
            # Dosya analizi
            analysis = await self.file_analyzer.analyze_file(file_path)
            
            # Analiz sonucunu kaydet
            data_entry = {
                "type": "file_analysis",
                "timestamp": datetime.now().isoformat(),
                "source": "file_analyzer",
                "data": {
                    "file_path": file_path,
                    "analysis": analysis
                }
            }
            
            self._store_data(data_entry)
            
        except Exception as e:
            logger.error(f"Dosya analizi hatası: {e}")
    
    def _store_data(self, data_entry: Dict[str, Any]) -> None:
        """Veri girişini kaydet"""
        try:
            # Veriyi listeye ekle
            self.collected_data.append(data_entry)
            
            # Maksimum boyut kontrolü
            if len(self.collected_data) > self.max_data_entries:
                # En eski veriyi sil
                self.collected_data.pop(0)
            
            # Callback'leri çağır
            for callback in self.data_callbacks:
                try:
                    callback(data_entry)
                except Exception as e:
                    logger.error(f"Data callback hatası: {e}")
            
            logger.debug(f"Veri kaydedildi: {data_entry['type']}")
            
        except Exception as e:
            logger.error(f"Veri kaydetme hatası: {e}")
    
    async def collect_system_info(self) -> Dict[str, Any]:
        """Sistem bilgilerini topla"""
        try:
            import platform
            import psutil
            
            system_info = {
                "platform": {
                    "system": platform.system(),
                    "release": platform.release(),
                    "version": platform.version(),
                    "machine": platform.machine(),
                    "processor": platform.processor()
                },
                "cpu": {
                    "count": psutil.cpu_count(),
                    "usage": psutil.cpu_percent(interval=1)
                },
                "memory": {
                    "total": psutil.virtual_memory().total,
                    "available": psutil.virtual_memory().available,
                    "usage": psutil.virtual_memory().percent
                },
                "disk": {
                    "total": psutil.disk_usage('/').total,
                    "free": psutil.disk_usage('/').free,
                    "usage": psutil.disk_usage('/').percent
                }
            }
            
            # Sistem bilgisini kaydet
            data_entry = {
                "type": "system_info",
                "timestamp": datetime.now().isoformat(),
                "source": "data_collector",
                "data": system_info
            }
            
            self._store_data(data_entry)
            return system_info
            
        except Exception as e:
            logger.error(f"Sistem bilgisi toplama hatası: {e}")
            return {}
    
    async def collect_github_data(self, 
                                 username: str,
                                 auth_token: str = None) -> Dict[str, Any]:
        """GitHub verilerini topla"""
        try:
            # Kullanıcı repolarını al
            repos = await self.api_integrator.get_github_user_repos(
                username, auth_token
            )
            
            github_data = {
                "username": username,
                "repos": repos,
                "total_repos": len(repos),
                "total_stars": sum(repo.get("stars", 0) for repo in repos),
                "languages": list(set(repo.get("language") for repo in repos if repo.get("language")))
            }
            
            # GitHub verisini kaydet
            data_entry = {
                "type": "github_data",
                "timestamp": datetime.now().isoformat(),
                "source": "api_integrator",
                "data": github_data
            }
            
            self._store_data(data_entry)
            return github_data
            
        except Exception as e:
            logger.error(f"GitHub veri toplama hatası: {e}")
            return {}
    
    async def collect_directory_analysis(self, 
                                        directory_path: str,
                                        recursive: bool = True) -> Dict[str, Any]:
        """Dizin analizi verilerini topla"""
        try:
            # Dizin analizi
            analysis = await self.file_analyzer.analyze_directory(
                directory_path, recursive
            )
            
            # Analiz verisini kaydet
            data_entry = {
                "type": "directory_analysis",
                "timestamp": datetime.now().isoformat(),
                "source": "file_analyzer",
                "data": analysis
            }
            
            self._store_data(data_entry)
            return analysis
            
        except Exception as e:
            logger.error(f"Dizin analizi veri toplama hatası: {e}")
            return {}
    
    def get_collected_data(self, 
                          data_type: str = None,
                          since: datetime = None,
                          limit: int = 100) -> List[Dict[str, Any]]:
        """Toplanan verileri al"""
        try:
            data = self.collected_data.copy()
            
            # Tip filtresi
            if data_type:
                data = [entry for entry in data if entry.get("type") == data_type]
            
            # Zaman filtresi
            if since:
                data = [
                    entry for entry in data 
                    if datetime.fromisoformat(entry["timestamp"]) >= since
                ]
            
            # Sırala ve sınırla
            data.sort(key=lambda x: x["timestamp"], reverse=True)
            return data[:limit]
            
        except Exception as e:
            logger.error(f"Veri alma hatası: {e}")
            return []
    
    def get_data_summary(self, 
                        since: datetime = None) -> Dict[str, Any]:
        """Veri özetini al"""
        try:
            if since is None:
                since = datetime.now() - timedelta(hours=24)
            
            data = self.get_collected_data(since=since)
            
            # Tip sayıları
            type_counts = {}
            source_counts = {}
            
            for entry in data:
                entry_type = entry.get("type", "unknown")
                entry_source = entry.get("source", "unknown")
                
                type_counts[entry_type] = type_counts.get(entry_type, 0) + 1
                source_counts[entry_source] = source_counts.get(entry_source, 0) + 1
            
            return {
                "total_entries": len(data),
                "time_range": {
                    "since": since.isoformat(),
                    "until": datetime.now().isoformat()
                },
                "type_counts": type_counts,
                "source_counts": source_counts,
                "collection_status": {
                    "is_collecting": self.is_collecting,
                    "file_monitoring": self.file_monitor.get_monitoring_status(),
                    "api_cache": self.api_integrator.get_cache_stats()
                }
            }
            
        except Exception as e:
            logger.error(f"Veri özeti alma hatası: {e}")
            return {}
    
    def export_data(self, 
                   file_path: str,
                   data_type: str = None,
                   since: datetime = None) -> bool:
        """Verileri dosyaya aktar"""
        try:
            data = self.get_collected_data(data_type, since)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Veriler aktarıldı: {file_path} ({len(data)} giriş)")
            return True
            
        except Exception as e:
            logger.error(f"Veri aktarma hatası: {e}")
            return False
    
    def clear_data(self, data_type: str = None) -> int:
        """Verileri temizle"""
        try:
            if data_type:
                # Belirli tip verileri temizle
                original_count = len(self.collected_data)
                self.collected_data = [
                    entry for entry in self.collected_data 
                    if entry.get("type") != data_type
                ]
                removed_count = original_count - len(self.collected_data)
            else:
                # Tüm verileri temizle
                removed_count = len(self.collected_data)
                self.collected_data.clear()
            
            logger.info(f"{removed_count} veri girişi temizlendi")
            return removed_count
            
        except Exception as e:
            logger.error(f"Veri temizleme hatası: {e}")
            return 0
    
    async def __aenter__(self):
        """Async context manager giriş"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager çıkış"""
        await self.shutdown()
