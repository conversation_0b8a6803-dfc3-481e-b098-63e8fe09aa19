"""
SQLite Memory Layer

SQLite tabanlı L3 ve L4 hafıza katmanları.
"""

import json
import time
import asyncio
import sqlite3
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
from pathlib import Path

try:
    import aiosqlite
    AIOSQLITE_AVAILABLE = True
except ImportError:
    AIOSQLITE_AVAILABLE = False
    aiosqlite = None

from ..utils.logger import get_logger
from ..config import config

logger = get_logger(__name__)


@dataclass
class SQLiteMemoryEntry:
    """SQLite hafıza girişi"""
    id: Optional[int]
    key: str
    data: Dict[str, Any]
    timestamp: float
    layer: str
    priority: str = "medium"
    tags: List[str] = None
    expires_at: Optional[float] = None


class SQLiteMemoryLayer:
    """SQLite tabanlı hafıza katmanı"""
    
    def __init__(self):
        self.db_path = Path(config.memory.sqlite_path)
        self.db_connection = None
        self.is_initialized = False
        
        # Katman konfigürasyonu
        self.l3_ttl = 86400  # 1 gün
        self.l4_ttl = 604800  # 1 hafta
        
        # Performans metrikleri
        self.metrics = {
            "total_operations": 0,
            "l3_operations": 0,
            "l4_operations": 0,
            "errors": 0,
            "database_size_mb": 0
        }
        
        logger.info("SQLite Memory Layer oluşturuldu")
    
    async def initialize(self) -> None:
        """SQLite veritabanını başlat"""
        try:
            if self.is_initialized:
                return
            
            if not AIOSQLITE_AVAILABLE:
                logger.warning("aiosqlite modülü bulunamadı, mock mode aktif")
                self._setup_mock_mode()
                self.is_initialized = True
                return
            
            logger.info("SQLite veritabanı başlatılıyor...")
            
            # Dizini oluştur
            self.db_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Veritabanı bağlantısını aç
            self.db_connection = await aiosqlite.connect(str(self.db_path))
            
            # Tabloları oluştur
            await self._create_tables()
            
            # İndeksleri oluştur
            await self._create_indexes()
            
            # Cleanup task'ını başlat
            asyncio.create_task(self._cleanup_expired_entries())
            
            self.is_initialized = True
            logger.info("SQLite veritabanı başarıyla başlatıldı")
            
        except Exception as e:
            logger.error(f"SQLite başlatma hatası: {e}")
            logger.warning("Mock mode'a geçiliyor...")
            self._setup_mock_mode()
            self.is_initialized = True
    
    def _setup_mock_mode(self) -> None:
        """Mock mode kurulumu"""
        self.mock_storage = {}
        self.db_connection = None
        logger.info("SQLite mock mode aktif")
    
    async def _create_tables(self) -> None:
        """Veritabanı tablolarını oluştur"""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS memory_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT NOT NULL,
            data TEXT NOT NULL,
            timestamp REAL NOT NULL,
            layer TEXT NOT NULL,
            priority TEXT DEFAULT 'medium',
            tags TEXT,
            expires_at REAL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        await self.db_connection.execute(create_table_sql)
        await self.db_connection.commit()
    
    async def _create_indexes(self) -> None:
        """Veritabanı indekslerini oluştur"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_key_layer ON memory_entries(key, layer)",
            "CREATE INDEX IF NOT EXISTS idx_timestamp ON memory_entries(timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_expires_at ON memory_entries(expires_at)",
            "CREATE INDEX IF NOT EXISTS idx_layer ON memory_entries(layer)",
            "CREATE INDEX IF NOT EXISTS idx_priority ON memory_entries(priority)"
        ]
        
        for index_sql in indexes:
            await self.db_connection.execute(index_sql)
        
        await self.db_connection.commit()
    
    async def store_l3(self, key: str, data: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """L3 katmanına veri kaydet"""
        try:
            ttl = ttl or self.l3_ttl
            expires_at = time.time() + ttl
            
            entry = SQLiteMemoryEntry(
                id=None,
                key=key,
                data=data,
                timestamp=time.time(),
                layer="l3",
                priority="medium",
                expires_at=expires_at
            )
            
            if self.db_connection:
                # Mevcut girişi sil
                await self.db_connection.execute(
                    "DELETE FROM memory_entries WHERE key = ? AND layer = ?",
                    (key, "l3")
                )
                
                # Yeni girişi ekle
                await self.db_connection.execute(
                    """INSERT INTO memory_entries 
                       (key, data, timestamp, layer, priority, expires_at)
                       VALUES (?, ?, ?, ?, ?, ?)""",
                    (key, json.dumps(data), entry.timestamp, "l3", "medium", expires_at)
                )
                
                await self.db_connection.commit()
            else:
                # Mock mode
                self.mock_storage[f"l3:{key}"] = {
                    "data": data,
                    "timestamp": entry.timestamp,
                    "expires_at": expires_at
                }
            
            self.metrics["total_operations"] += 1
            self.metrics["l3_operations"] += 1
            logger.debug(f"L3 veri kaydedildi: {key}")
            return True
            
        except Exception as e:
            logger.error(f"L3 kaydetme hatası: {e}")
            self.metrics["errors"] += 1
            return False
    
    async def get_l3(self, key: str) -> Optional[Dict[str, Any]]:
        """L3 katmanından veri al"""
        try:
            if self.db_connection:
                cursor = await self.db_connection.execute(
                    """SELECT data FROM memory_entries 
                       WHERE key = ? AND layer = ? AND 
                       (expires_at IS NULL OR expires_at > ?)""",
                    (key, "l3", time.time())
                )
                
                row = await cursor.fetchone()
                if row:
                    logger.debug(f"L3 hit: {key}")
                    return json.loads(row[0])
            else:
                # Mock mode
                mock_key = f"l3:{key}"
                if mock_key in self.mock_storage:
                    entry = self.mock_storage[mock_key]
                    if time.time() < entry["expires_at"]:
                        return entry["data"]
                    else:
                        del self.mock_storage[mock_key]
            
            logger.debug(f"L3 miss: {key}")
            return None
            
        except Exception as e:
            logger.error(f"L3 okuma hatası: {e}")
            self.metrics["errors"] += 1
            return None
    
    async def store_l4(self, key: str, data: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """L4 katmanına veri kaydet"""
        try:
            ttl = ttl or self.l4_ttl
            expires_at = time.time() + ttl
            
            entry = SQLiteMemoryEntry(
                id=None,
                key=key,
                data=data,
                timestamp=time.time(),
                layer="l4",
                priority="low",
                expires_at=expires_at
            )
            
            if self.db_connection:
                # Mevcut girişi sil
                await self.db_connection.execute(
                    "DELETE FROM memory_entries WHERE key = ? AND layer = ?",
                    (key, "l4")
                )
                
                # Yeni girişi ekle
                await self.db_connection.execute(
                    """INSERT INTO memory_entries 
                       (key, data, timestamp, layer, priority, expires_at)
                       VALUES (?, ?, ?, ?, ?, ?)""",
                    (key, json.dumps(data), entry.timestamp, "l4", "low", expires_at)
                )
                
                await self.db_connection.commit()
            else:
                # Mock mode
                self.mock_storage[f"l4:{key}"] = {
                    "data": data,
                    "timestamp": entry.timestamp,
                    "expires_at": expires_at
                }
            
            self.metrics["total_operations"] += 1
            self.metrics["l4_operations"] += 1
            logger.debug(f"L4 veri kaydedildi: {key}")
            return True
            
        except Exception as e:
            logger.error(f"L4 kaydetme hatası: {e}")
            self.metrics["errors"] += 1
            return False
    
    async def get_l4(self, key: str) -> Optional[Dict[str, Any]]:
        """L4 katmanından veri al"""
        try:
            if self.db_connection:
                cursor = await self.db_connection.execute(
                    """SELECT data FROM memory_entries 
                       WHERE key = ? AND layer = ? AND 
                       (expires_at IS NULL OR expires_at > ?)""",
                    (key, "l4", time.time())
                )
                
                row = await cursor.fetchone()
                if row:
                    logger.debug(f"L4 hit: {key}")
                    return json.loads(row[0])
            else:
                # Mock mode
                mock_key = f"l4:{key}"
                if mock_key in self.mock_storage:
                    entry = self.mock_storage[mock_key]
                    if time.time() < entry["expires_at"]:
                        return entry["data"]
                    else:
                        del self.mock_storage[mock_key]
            
            logger.debug(f"L4 miss: {key}")
            return None
            
        except Exception as e:
            logger.error(f"L4 okuma hatası: {e}")
            self.metrics["errors"] += 1
            return None
    
    async def delete(self, key: str, layer: str = "both") -> bool:
        """Veriyi sil"""
        try:
            if self.db_connection:
                if layer == "both":
                    await self.db_connection.execute(
                        "DELETE FROM memory_entries WHERE key = ? AND layer IN ('l3', 'l4')",
                        (key,)
                    )
                else:
                    await self.db_connection.execute(
                        "DELETE FROM memory_entries WHERE key = ? AND layer = ?",
                        (key, layer)
                    )
                
                await self.db_connection.commit()
            else:
                # Mock mode
                if layer == "both":
                    self.mock_storage.pop(f"l3:{key}", None)
                    self.mock_storage.pop(f"l4:{key}", None)
                else:
                    self.mock_storage.pop(f"{layer}:{key}", None)
            
            logger.debug(f"Veri silindi: {key} ({layer})")
            return True
            
        except Exception as e:
            logger.error(f"Veri silme hatası: {e}")
            return False
    
    async def _cleanup_expired_entries(self) -> None:
        """Süresi dolmuş girişleri temizle"""
        while True:
            try:
                await asyncio.sleep(3600)  # Her saat çalıştır
                
                if self.db_connection:
                    cursor = await self.db_connection.execute(
                        "DELETE FROM memory_entries WHERE expires_at IS NOT NULL AND expires_at < ?",
                        (time.time(),)
                    )
                    
                    deleted_count = cursor.rowcount
                    await self.db_connection.commit()
                    
                    if deleted_count > 0:
                        logger.info(f"Süresi dolmuş {deleted_count} giriş temizlendi")
                else:
                    # Mock mode cleanup
                    current_time = time.time()
                    expired_keys = [
                        k for k, v in self.mock_storage.items()
                        if v.get("expires_at", float('inf')) < current_time
                    ]
                    
                    for key in expired_keys:
                        del self.mock_storage[key]
                    
                    if expired_keys:
                        logger.info(f"Mock mode: {len(expired_keys)} giriş temizlendi")
                
            except Exception as e:
                logger.error(f"Cleanup hatası: {e}")
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Performans metriklerini al"""
        try:
            if self.db_connection:
                # Veritabanı boyutunu hesapla
                if self.db_path.exists():
                    self.metrics["database_size_mb"] = self.db_path.stat().st_size / (1024 * 1024)
                
                # Giriş sayılarını al
                cursor = await self.db_connection.execute(
                    "SELECT layer, COUNT(*) FROM memory_entries GROUP BY layer"
                )
                
                layer_counts = {}
                async for row in cursor:
                    layer_counts[row[0]] = row[1]
                
                return {
                    **self.metrics,
                    "layer_counts": layer_counts,
                    "database_path": str(self.db_path)
                }
            else:
                return {
                    **self.metrics,
                    "mode": "mock",
                    "mock_storage_size": len(self.mock_storage)
                }
            
        except Exception as e:
            logger.error(f"Metrik alma hatası: {e}")
            return self.metrics
    
    async def cleanup(self) -> None:
        """Kaynakları temizle"""
        try:
            if self.db_connection:
                await self.db_connection.close()
            
            if hasattr(self, 'mock_storage'):
                self.mock_storage.clear()
            
            logger.info("SQLite Memory Layer temizlendi")
            
        except Exception as e:
            logger.error(f"SQLite temizleme hatası: {e}")
