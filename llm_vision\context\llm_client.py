"""
LLM istemci modülü
"""

import asyncio
import aiohttp
import json
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import openai

from ..config import config
from ..utils.logger import get_logger
from ..utils.exceptions import ContextError

logger = get_logger(__name__)


class LLMClient:
    """LLM istemci sınıfı"""
    
    def __init__(self):
        self.model = config.context.model
        self.api_key = config.context.api_key
        self.max_tokens = config.context.max_tokens
        
        self.session: Optional[aiohttp.ClientSession] = None
        self.openai_client: Optional[openai.AsyncOpenAI] = None
        
        # Model konfigürasyonları
        self.model_configs = {
            "gpt-3.5-turbo": {
                "provider": "openai",
                "max_tokens": 4096,
                "supports_functions": True
            },
            "gpt-4": {
                "provider": "openai", 
                "max_tokens": 8192,
                "supports_functions": True
            },
            "gpt-4-turbo": {
                "provider": "openai",
                "max_tokens": 128000,
                "supports_functions": True
            },
            "mistral-7b-instruct": {
                "provider": "huggingface",
                "max_tokens": 8192,
                "supports_functions": False
            },
            "llama-2-7b-chat": {
                "provider": "huggingface",
                "max_tokens": 4096,
                "supports_functions": False
            }
        }
        
        logger.info(f"LLM istemci başlatıldı - Model: {self.model}")
    
    async def initialize(self) -> None:
        """LLM istemciyi başlat"""
        try:
            # HTTP session oluştur
            timeout = aiohttp.ClientTimeout(total=60)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
            # OpenAI istemci oluştur
            if self.api_key:
                self.openai_client = openai.AsyncOpenAI(api_key=self.api_key)
            
            logger.info("LLM istemci başlatıldı")
            
        except Exception as e:
            logger.error(f"LLM istemci başlatma hatası: {e}")
            raise ContextError(f"LLM istemci başlatma hatası: {str(e)}")
    
    async def shutdown(self) -> None:
        """LLM istemciyi kapat"""
        try:
            if self.session:
                await self.session.close()
                self.session = None
            
            if self.openai_client:
                await self.openai_client.close()
                self.openai_client = None
            
            logger.info("LLM istemci kapatıldı")
            
        except Exception as e:
            logger.error(f"LLM istemci kapatma hatası: {e}")
    
    async def generate_completion(self, 
                                prompt: str,
                                system_prompt: str = None,
                                temperature: float = 0.7,
                                max_tokens: int = None,
                                model: str = None) -> str:
        """Metin tamamlama oluştur"""
        try:
            model = model or self.model
            max_tokens = max_tokens or self.max_tokens
            
            model_config = self.model_configs.get(model, {})
            provider = model_config.get("provider", "openai")
            
            if provider == "openai":
                return await self._generate_openai_completion(
                    prompt, system_prompt, temperature, max_tokens, model
                )
            elif provider == "huggingface":
                return await self._generate_huggingface_completion(
                    prompt, system_prompt, temperature, max_tokens, model
                )
            else:
                raise ContextError(f"Desteklenmeyen provider: {provider}")
                
        except Exception as e:
            logger.error(f"Completion oluşturma hatası: {e}")
            raise ContextError(f"Completion oluşturma hatası: {str(e)}")
    
    async def _generate_openai_completion(self, 
                                        prompt: str,
                                        system_prompt: str = None,
                                        temperature: float = 0.7,
                                        max_tokens: int = None,
                                        model: str = None) -> str:
        """OpenAI completion oluştur"""
        try:
            if not self.openai_client:
                raise ContextError("OpenAI istemci başlatılmamış")
            
            messages = []
            
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            
            messages.append({"role": "user", "content": prompt})
            
            response = await self.openai_client.chat.completions.create(
                model=model or self.model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"OpenAI completion hatası: {e}")
            raise ContextError(f"OpenAI completion hatası: {str(e)}")
    
    async def _generate_huggingface_completion(self, 
                                             prompt: str,
                                             system_prompt: str = None,
                                             temperature: float = 0.7,
                                             max_tokens: int = None,
                                             model: str = None) -> str:
        """Hugging Face completion oluştur"""
        try:
            # Bu fonksiyon Hugging Face API entegrasyonu için placeholder
            # Gerçek implementasyon için Hugging Face Inference API kullanılabilir
            
            full_prompt = prompt
            if system_prompt:
                full_prompt = f"{system_prompt}\n\n{prompt}"
            
            # Basit mock response
            return f"[Mock Response for {model}] Analiz tamamlandı: {full_prompt[:100]}..."
            
        except Exception as e:
            logger.error(f"Hugging Face completion hatası: {e}")
            raise ContextError(f"Hugging Face completion hatası: {str(e)}")
    
    async def analyze_data(self, 
                          data: Dict[str, Any],
                          analysis_type: str = "general",
                          custom_prompt: str = None) -> Dict[str, Any]:
        """Veriyi analiz et"""
        try:
            # Analiz promptu oluştur
            if custom_prompt:
                prompt = custom_prompt
            else:
                prompt = self._create_analysis_prompt(data, analysis_type)
            
            # System prompt
            system_prompt = self._get_system_prompt(analysis_type)
            
            # Completion oluştur
            response = await self.generate_completion(
                prompt=prompt,
                system_prompt=system_prompt,
                temperature=0.3  # Analiz için düşük temperature
            )
            
            # Sonucu yapılandır
            result = {
                "analysis_type": analysis_type,
                "timestamp": datetime.now().isoformat(),
                "input_data": data,
                "analysis": response,
                "model": self.model,
                "prompt_used": prompt
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Veri analizi hatası: {e}")
            raise ContextError(f"Veri analizi hatası: {str(e)}")
    
    def _create_analysis_prompt(self, 
                               data: Dict[str, Any], 
                               analysis_type: str) -> str:
        """Analiz promptu oluştur"""
        
        if analysis_type == "vision":
            return f"""
Aşağıdaki görüntü analizi verilerini incele ve özetle:

{json.dumps(data, indent=2, ensure_ascii=False)}

Lütfen şunları yap:
1. Görüntüde tespit edilen nesneleri listele
2. Bulunan metinleri özetle
3. Genel görüntü içeriğini tanımla
4. Önemli bulgular varsa belirt

Türkçe yanıt ver.
"""
        
        elif analysis_type == "file":
            return f"""
Aşağıdaki dosya analizi verilerini incele ve özetle:

{json.dumps(data, indent=2, ensure_ascii=False)}

Lütfen şunları yap:
1. Dosya türü ve boyutunu belirt
2. İçerik özelliklerini özetle
3. Güvenlik durumunu değerlendir
4. Önemli bulgular varsa belirt

Türkçe yanıt ver.
"""
        
        elif analysis_type == "system":
            return f"""
Aşağıdaki sistem bilgilerini incele ve özetle:

{json.dumps(data, indent=2, ensure_ascii=False)}

Lütfen şunları yap:
1. Sistem performansını değerlendir
2. Kaynak kullanımını analiz et
3. Potansiyel sorunları belirt
4. Öneriler sun

Türkçe yanıt ver.
"""
        
        else:  # general
            return f"""
Aşağıdaki veriyi analiz et ve özetle:

{json.dumps(data, indent=2, ensure_ascii=False)}

Lütfen veriyi incele ve önemli noktaları Türkçe olarak özetle.
"""
    
    def _get_system_prompt(self, analysis_type: str) -> str:
        """System prompt al"""
        
        base_prompt = """Sen LLM Vision System'in Context Engine'isin. Görevin ham verileri analiz edip anlamlı bağlam oluşturmak."""
        
        if analysis_type == "vision":
            return f"""{base_prompt}
            
Görüntü analizi konusunda uzmansın. Nesne tanıma, OCR ve görüntü içeriği analizi yapabilirsin.
Teknik detayları kullanıcı dostu bir şekilde açıkla."""
        
        elif analysis_type == "file":
            return f"""{base_prompt}
            
Dosya analizi konusunda uzmansın. Dosya türleri, içerik analizi ve güvenlik değerlendirmesi yapabilirsin.
Teknik bilgileri anlaşılır şekilde sun."""
        
        elif analysis_type == "system":
            return f"""{base_prompt}
            
Sistem analizi konusunda uzmansın. Performans metrikleri, kaynak kullanımı ve sistem sağlığı değerlendirmesi yapabilirsin.
Pratik öneriler sun."""
        
        else:
            return f"""{base_prompt}
            
Genel veri analizi yapabilirsin. Verilerdeki önemli kalıpları ve anlamlı bilgileri çıkarabilirsin.
Net ve öz açıklamalar yap."""
    
    async def create_context_summary(self, 
                                   multiple_data: List[Dict[str, Any]],
                                   context_type: str = "comprehensive") -> str:
        """Çoklu veriden bağlam özeti oluştur"""
        try:
            # Veri özetini hazırla
            data_summary = {
                "total_items": len(multiple_data),
                "data_types": list(set(item.get("type", "unknown") for item in multiple_data)),
                "time_range": {
                    "earliest": min(item.get("timestamp", "") for item in multiple_data if item.get("timestamp")),
                    "latest": max(item.get("timestamp", "") for item in multiple_data if item.get("timestamp"))
                },
                "items": multiple_data[:10]  # İlk 10 öğe
            }
            
            prompt = f"""
Aşağıdaki çoklu veri kaynağından kapsamlı bir bağlam özeti oluştur:

{json.dumps(data_summary, indent=2, ensure_ascii=False)}

Lütfen şunları yap:
1. Genel durumu özetle
2. Önemli değişiklikleri belirt
3. Dikkat çeken kalıpları tanımla
4. Ana LLM için yararlı bağlam bilgisi oluştur

Türkçe, net ve öz bir özet ver.
"""
            
            system_prompt = """Sen LLM Vision System'in Context Engine'isin. 
Çoklu veri kaynağından Ana LLM için anlamlı bağlam oluşturuyorsun.
Özet, Ana LLM'nin kullanıcı sorularını daha iyi yanıtlamasına yardımcı olmalı."""
            
            response = await self.generate_completion(
                prompt=prompt,
                system_prompt=system_prompt,
                temperature=0.3
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Bağlam özeti oluşturma hatası: {e}")
            raise ContextError(f"Bağlam özeti oluşturma hatası: {str(e)}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Model bilgilerini al"""
        model_config = self.model_configs.get(self.model, {})
        
        return {
            "current_model": self.model,
            "provider": model_config.get("provider", "unknown"),
            "max_tokens": model_config.get("max_tokens", self.max_tokens),
            "supports_functions": model_config.get("supports_functions", False),
            "api_key_configured": bool(self.api_key),
            "available_models": list(self.model_configs.keys())
        }
    
    async def __aenter__(self):
        """Async context manager giriş"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager çıkış"""
        await self.shutdown()
