"""
Security Manager System

Plugin güvenliği ve izin yönetimi için sistem.
"""

from typing import Dict, List, Set, Optional, Any
import hashlib
import time
from pathlib import Path
from dataclasses import dataclass, field

from .base_plugin import BasePlugin
from .plugin_manifest import PluginManifest
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class SecurityPolicy:
    """Güvenlik politikası"""
    max_memory_mb: int = 100
    max_cpu_percent: float = 50.0
    max_network_connections: int = 10
    max_file_operations_per_minute: int = 100
    allowed_file_extensions: Set[str] = field(default_factory=lambda: {'.txt', '.json', '.yml', '.yaml'})
    blocked_domains: Set[str] = field(default_factory=set)
    sandbox_enabled: bool = True


@dataclass
class PluginSecurityContext:
    """Plugin güvenlik bağlamı"""
    plugin_name: str
    permissions: Set[str]
    resource_usage: Dict[str, Any] = field(default_factory=dict)
    violation_count: int = 0
    last_violation_time: Optional[float] = None
    is_trusted: bool = False


class SecurityManager:
    """Plugin güvenlik yöneticisi"""
    
    def __init__(self):
        self.is_initialized = False
        self.security_policy = SecurityPolicy()
        self.plugin_contexts: Dict[str, PluginSecurityContext] = {}
        
        # Güvenlik kuralları
        self.permission_hierarchy = {
            'file_read': {'file_read'},
            'file_write': {'file_read', 'file_write'},
            'network_access': {'network_access'},
            'camera_access': {'camera_access'},
            'system_info': {'system_info'}
        }
        
        # Risk skorları
        self.permission_risk_scores = {
            'file_read': 2,
            'file_write': 5,
            'network_access': 4,
            'camera_access': 6,
            'system_info': 3,
            'microphone_access': 7,
            'location_access': 8
        }
        
        # Güvenilir plugin'ler (hash tabanlı)
        self.trusted_plugin_hashes: Set[str] = set()
        
        logger.info("Security Manager oluşturuldu")
    
    async def initialize(self) -> None:
        """Security Manager'ı başlat"""
        try:
            if self.is_initialized:
                return
            
            logger.info("Security Manager başlatılıyor...")
            
            # Güvenilir plugin hash'lerini yükle
            await self._load_trusted_plugins()
            
            self.is_initialized = True
            logger.info("Security Manager başarıyla başlatıldı")
            
        except Exception as e:
            logger.error(f"Security Manager başlatma hatası: {e}")
            raise
    
    async def validate_plugin(self, plugin: BasePlugin) -> bool:
        """
        Plugin'i güvenlik açısından doğrula
        
        Args:
            plugin: Doğrulanacak plugin
            
        Returns:
            bool: Plugin güvenli mi
        """
        try:
            manifest = plugin.manifest
            plugin_name = manifest.name
            
            logger.info(f"Plugin güvenlik kontrolü: {plugin_name}")
            
            # 1. Manifest güvenlik kontrolü
            if not await self._validate_manifest_security(manifest):
                logger.error(f"Manifest güvenlik kontrolü başarısız: {plugin_name}")
                return False
            
            # 2. İzin kontrolü
            if not await self._validate_permissions(manifest):
                logger.error(f"İzin kontrolü başarısız: {plugin_name}")
                return False
            
            # 3. Risk değerlendirmesi
            risk_score = await self._calculate_risk_score(manifest)
            if risk_score > 20:  # Yüksek risk threshold
                logger.warning(f"Yüksek risk skoru: {plugin_name} (skor: {risk_score})")
                return False
            
            # 4. Plugin hash kontrolü
            plugin_hash = await self._calculate_plugin_hash(plugin)
            is_trusted = plugin_hash in self.trusted_plugin_hashes
            
            # 5. Güvenlik bağlamı oluştur
            context = PluginSecurityContext(
                plugin_name=plugin_name,
                permissions={perm.name for perm in manifest.permissions},
                is_trusted=is_trusted
            )
            self.plugin_contexts[plugin_name] = context
            
            logger.info(f"Plugin güvenlik kontrolü başarılı: {plugin_name} (trusted: {is_trusted})")
            return True
            
        except Exception as e:
            logger.error(f"Plugin güvenlik kontrolü hatası: {e}")
            return False
    
    async def check_permission(self, plugin_name: str, permission: str) -> bool:
        """
        Plugin'in belirli bir izni var mı kontrol et
        
        Args:
            plugin_name: Plugin adı
            permission: Kontrol edilecek izin
            
        Returns:
            bool: İzin var mı
        """
        context = self.plugin_contexts.get(plugin_name)
        if not context:
            logger.warning(f"Plugin güvenlik bağlamı bulunamadı: {plugin_name}")
            return False
        
        # İzin hiyerarşisini kontrol et
        required_permissions = self.permission_hierarchy.get(permission, {permission})
        return required_permissions.issubset(context.permissions)
    
    async def record_resource_usage(self, plugin_name: str, resource_type: str, value: Any) -> None:
        """
        Plugin kaynak kullanımını kaydet
        
        Args:
            plugin_name: Plugin adı
            resource_type: Kaynak tipi (memory, cpu, network, etc.)
            value: Kaynak değeri
        """
        context = self.plugin_contexts.get(plugin_name)
        if not context:
            return
        
        context.resource_usage[resource_type] = value
        
        # Limit kontrolü
        await self._check_resource_limits(plugin_name, context)
    
    async def report_violation(self, plugin_name: str, violation_type: str, details: str) -> None:
        """
        Güvenlik ihlali rapor et
        
        Args:
            plugin_name: Plugin adı
            violation_type: İhlal tipi
            details: İhlal detayları
        """
        context = self.plugin_contexts.get(plugin_name)
        if not context:
            return
        
        context.violation_count += 1
        context.last_violation_time = time.time()
        
        logger.warning(f"Güvenlik ihlali: {plugin_name} - {violation_type}: {details}")
        
        # Çok fazla ihlal varsa plugin'i devre dışı bırak
        if context.violation_count > 5:
            logger.error(f"Plugin çok fazla güvenlik ihlali yaptı: {plugin_name}")
            await self._disable_plugin(plugin_name)
    
    async def _validate_manifest_security(self, manifest: PluginManifest) -> bool:
        """Manifest güvenlik kontrolü"""
        # Yasaklı izinler
        forbidden_permissions = {'system_admin', 'root_access', 'kernel_access'}
        permission_names = {perm.name for perm in manifest.permissions}
        
        if permission_names & forbidden_permissions:
            logger.error(f"Yasaklı izinler tespit edildi: {permission_names & forbidden_permissions}")
            return False
        
        # Çok fazla izin
        if len(manifest.permissions) > 10:
            logger.warning(f"Çok fazla izin talep edildi: {len(manifest.permissions)}")
            return False
        
        # Endpoint güvenlik kontrolü
        for endpoint in manifest.endpoints:
            if endpoint.path.startswith('/admin') or endpoint.path.startswith('/system'):
                logger.error(f"Hassas endpoint tespit edildi: {endpoint.path}")
                return False
        
        return True
    
    async def _validate_permissions(self, manifest: PluginManifest) -> bool:
        """İzin doğrulaması"""
        for permission in manifest.permissions:
            # İzin formatı kontrolü
            if not permission.name.islower() or not permission.name.replace('_', '').isalnum():
                logger.error(f"Geçersiz izin formatı: {permission.name}")
                return False
            
            # Bilinmeyen izinler
            known_permissions = set(self.permission_risk_scores.keys())
            if permission.name not in known_permissions:
                logger.warning(f"Bilinmeyen izin: {permission.name}")
        
        return True
    
    async def _calculate_risk_score(self, manifest: PluginManifest) -> int:
        """Plugin risk skorunu hesapla"""
        risk_score = 0
        
        # İzin risk skorları
        for permission in manifest.permissions:
            risk_score += self.permission_risk_scores.get(permission.name, 1)
        
        # Bağımlılık risk skoru
        risk_score += len(manifest.dependencies) * 0.5
        
        # Endpoint risk skoru
        risk_score += len(manifest.endpoints) * 0.2
        
        # Yüksek riskli kombinasyonlar
        permission_names = {perm.name for perm in manifest.permissions}
        if 'file_write' in permission_names and 'network_access' in permission_names:
            risk_score += 5
        
        return int(risk_score)
    
    async def _calculate_plugin_hash(self, plugin: BasePlugin) -> str:
        """Plugin hash'ini hesapla"""
        # Manifest bilgilerini hash'le
        manifest_data = f"{plugin.manifest.name}:{plugin.manifest.version}:{plugin.manifest.author}"
        return hashlib.sha256(manifest_data.encode()).hexdigest()
    
    async def _check_resource_limits(self, plugin_name: str, context: PluginSecurityContext) -> None:
        """Kaynak limitlerini kontrol et"""
        usage = context.resource_usage
        
        # Memory kontrolü
        if 'memory_mb' in usage and usage['memory_mb'] > self.security_policy.max_memory_mb:
            await self.report_violation(
                plugin_name, 
                'memory_limit_exceeded', 
                f"Memory usage: {usage['memory_mb']}MB > {self.security_policy.max_memory_mb}MB"
            )
        
        # CPU kontrolü
        if 'cpu_percent' in usage and usage['cpu_percent'] > self.security_policy.max_cpu_percent:
            await self.report_violation(
                plugin_name,
                'cpu_limit_exceeded',
                f"CPU usage: {usage['cpu_percent']}% > {self.security_policy.max_cpu_percent}%"
            )
    
    async def _load_trusted_plugins(self) -> None:
        """Güvenilir plugin hash'lerini yükle"""
        try:
            # Varsayılan güvenilir plugin'ler
            self.trusted_plugin_hashes.update({
                # Core plugin'lerin hash'leri buraya eklenecek
            })
            
            logger.info(f"Güvenilir plugin sayısı: {len(self.trusted_plugin_hashes)}")
            
        except Exception as e:
            logger.error(f"Güvenilir plugin yükleme hatası: {e}")
    
    async def _disable_plugin(self, plugin_name: str) -> None:
        """Plugin'i güvenlik nedeniyle devre dışı bırak"""
        # Bu fonksiyon plugin registry ile entegre edilecek
        logger.critical(f"Plugin güvenlik nedeniyle devre dışı bırakıldı: {plugin_name}")
    
    def get_security_report(self, plugin_name: str = None) -> Dict[str, Any]:
        """Güvenlik raporu al"""
        if plugin_name:
            context = self.plugin_contexts.get(plugin_name)
            if not context:
                return {"error": "Plugin bulunamadı"}
            
            return {
                "plugin_name": context.plugin_name,
                "permissions": list(context.permissions),
                "resource_usage": context.resource_usage,
                "violation_count": context.violation_count,
                "last_violation_time": context.last_violation_time,
                "is_trusted": context.is_trusted
            }
        else:
            return {
                "total_plugins": len(self.plugin_contexts),
                "trusted_plugins": sum(1 for ctx in self.plugin_contexts.values() if ctx.is_trusted),
                "plugins_with_violations": sum(1 for ctx in self.plugin_contexts.values() if ctx.violation_count > 0),
                "security_policy": {
                    "max_memory_mb": self.security_policy.max_memory_mb,
                    "max_cpu_percent": self.security_policy.max_cpu_percent,
                    "sandbox_enabled": self.security_policy.sandbox_enabled
                }
            }
